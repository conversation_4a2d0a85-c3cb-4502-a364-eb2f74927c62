{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, toDisplayString as _toDisplayString, createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode, Fragment as _Fragment, withCtx as _withCtx, createTextVNode as _createTextVNode, renderList as _renderList, createStaticVNode as _createStaticVNode } from \"vue\";\nvar _hoisted_1 = {\n  class: \"SuggestBasicInfo\"\n};\nvar _hoisted_2 = {\n  class: \"SuggestDetailInfo\"\n};\nvar _hoisted_3 = {\n  class: \"SuggestDetailNumberTime\"\n};\nvar _hoisted_4 = {\n  class: \"SuggestDetailNumber\"\n};\nvar _hoisted_5 = {\n  class: \"SuggestDetailTime\"\n};\nvar _hoisted_6 = {\n  class: \"SuggestDetailType\"\n};\nvar _hoisted_7 = {\n  key: 0,\n  class: \"SuggestDetailBigType\"\n};\nvar _hoisted_8 = {\n  key: 1,\n  class: \"SuggestDetailSmallType\"\n};\nvar _hoisted_9 = {\n  class: \"SuggestDetailTitle\"\n};\nvar _hoisted_10 = {\n  key: 0,\n  class: \"SuggestDetailInfoItem\"\n};\nvar _hoisted_11 = {\n  class: \"SuggestDetailInfo\"\n};\nvar _hoisted_12 = {\n  class: \"SuggestDetailInfoItem\"\n};\nvar _hoisted_13 = {\n  class: \"SuggestDetailInfoItem\"\n};\nvar _hoisted_14 = {\n  class: \"SuggestDetailInfo\"\n};\nvar _hoisted_15 = {\n  class: \"SuggestDetailInfoItem\"\n};\nvar _hoisted_16 = {\n  class: \"SuggestDetailInfoItem\"\n};\nvar _hoisted_17 = {\n  class: \"SuggestDetailInfo\"\n};\nvar _hoisted_18 = {\n  class: \"SuggestDetailInfoItem\"\n};\nvar _hoisted_19 = {\n  class: \"SuggestDetailInfoItem\"\n};\nvar _hoisted_20 = {\n  class: \"SuggestDetailInfoItem\"\n};\nvar _hoisted_21 = {\n  class: \"SuggestDetailInfoItem\"\n};\nvar _hoisted_22 = [\"innerHTML\"];\nvar _hoisted_23 = {\n  key: 2,\n  class: \"SuggestDetailInfoItem\"\n};\nvar _hoisted_24 = {\n  key: 3,\n  class: \"SuggestDetailInfoItem\"\n};\nvar _hoisted_25 = {\n  key: 4,\n  class: \"SuggestDetailInfoItem\"\n};\nvar _hoisted_26 = {\n  key: 5,\n  class: \"SuggestDetailInfoItem\"\n};\nvar _hoisted_27 = {\n  key: 6,\n  class: \"SuggestDetailInfoItem\"\n};\nvar _hoisted_28 = {\n  key: 7,\n  class: \"SuggestDetailInfoItem\"\n};\nvar _hoisted_29 = {\n  class: \"SuggestDetailInfoItem\"\n};\nvar _hoisted_30 = {\n  key: 8,\n  class: \"SuggestDetailInfoName\"\n};\nvar _hoisted_31 = {\n  key: 9,\n  class: \"SuggestDetailTable\"\n};\nvar _hoisted_32 = {\n  class: \"SuggestDetailTableItem row1\"\n};\nvar _hoisted_33 = {\n  class: \"SuggestDetailTableItem row1\"\n};\nvar _hoisted_34 = {\n  class: \"SuggestDetailTableItem row1\"\n};\nvar _hoisted_35 = {\n  class: \"SuggestDetailTableItem row3\"\n};\nvar _hoisted_36 = {\n  key: 0,\n  class: \"SuggestDetailTableItem row1\"\n};\nvar _hoisted_37 = {\n  key: 1,\n  class: \"SuggestDetailTableItem row1\"\n};\nvar _hoisted_38 = {\n  key: 2,\n  class: \"SuggestDetailTableItem row1\"\n};\nvar _hoisted_39 = {\n  key: 10,\n  class: \"SuggestDetailInfoName\"\n};\nvar _hoisted_40 = {\n  key: 11,\n  class: \"SuggestDetailTable\"\n};\nvar _hoisted_41 = {\n  class: \"SuggestDetailTableItem row1\"\n};\nvar _hoisted_42 = {\n  class: \"SuggestDetailTableItem row1\"\n};\nvar _hoisted_43 = {\n  class: \"SuggestDetailTableItem row4\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _$setup$details$submi, _$setup$details$submi2, _$setup$details$secto, _$setup$details$submi3, _$setup$details$submi4, _$setup$details$submi5, _$setup$details$submi6, _$setup$details$submi7, _$setup$details$sugge, _$setup$details$sugge2, _$setup$details$isMak, _$setup$details$notHa, _$setup$details$isHop, _$setup$details$isNee, _$setup$details$hopeH, _$setup$details$joinU, _$setup$details$joinU2, _$setup$details$conta, _$setup$details$conta2;\n  var _component_global_dynamic_title = _resolveComponent(\"global-dynamic-title\");\n  var _component_el_table_column = _resolveComponent(\"el-table-column\");\n  var _component_el_table = _resolveComponent(\"el-table\");\n  var _component_xyl_global_file = _resolveComponent(\"xyl-global-file\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_global_dynamic_title, {\n    templateCode: \"proposal_title\",\n    params: {\n      businessId: $setup.props.id\n    }\n  }, null, 8 /* PROPS */, [\"params\"]), _createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"div\", _hoisted_4, \"提案编号：\" + _toDisplayString($setup.details.serialNumber), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_5, \"提案时间：\" + _toDisplayString($setup.format($setup.details.createDate)), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_6, [$setup.details.bigThemeName ? (_openBlock(), _createElementBlock(\"div\", _hoisted_7, _toDisplayString($setup.details.bigThemeName), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true), $setup.details.smallThemeName ? (_openBlock(), _createElementBlock(\"div\", _hoisted_8, _toDisplayString($setup.details.smallThemeName), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true)])]), _createElementVNode(\"div\", _hoisted_9, _toDisplayString($setup.details.title), 1 /* TEXT */), $setup.details.suggestSubmitWay === 'team' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_10, \"提案者：\" + _toDisplayString($setup.details.suggestUserName), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true), $setup.details.suggestSubmitWay === 'cppcc_member' ? (_openBlock(), _createElementBlock(_Fragment, {\n    key: 1\n  }, [_createElementVNode(\"div\", _hoisted_11, [_createElementVNode(\"div\", _hoisted_12, \"提案者：\" + _toDisplayString((_$setup$details$submi = $setup.details.submitUserInfo) === null || _$setup$details$submi === void 0 ? void 0 : _$setup$details$submi.userName), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_13, \"委员证号：\" + _toDisplayString((_$setup$details$submi2 = $setup.details.submitUserInfo) === null || _$setup$details$submi2 === void 0 ? void 0 : _$setup$details$submi2.cardNumber), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_14, [_createElementVNode(\"div\", _hoisted_15, \"界别：\" + _toDisplayString((_$setup$details$secto = $setup.details.sectorType) === null || _$setup$details$secto === void 0 ? void 0 : _$setup$details$secto.label), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_16, \"联系电话：\" + _toDisplayString((_$setup$details$submi3 = $setup.details.submitUserInfo) === null || _$setup$details$submi3 === void 0 ? void 0 : _$setup$details$submi3.mobile), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_17, [_createElementVNode(\"div\", _hoisted_18, \"办公电话：\" + _toDisplayString((_$setup$details$submi4 = $setup.details.submitUserInfo) === null || _$setup$details$submi4 === void 0 ? void 0 : _$setup$details$submi4.officePhone), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_19, \"邮政编码：\" + _toDisplayString((_$setup$details$submi5 = $setup.details.submitUserInfo) === null || _$setup$details$submi5 === void 0 ? void 0 : _$setup$details$submi5.postcode), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_20, \"单位及职务：\" + _toDisplayString((_$setup$details$submi6 = $setup.details.submitUserInfo) === null || _$setup$details$submi6 === void 0 ? void 0 : _$setup$details$submi6.position), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_21, \"通讯地址：\" + _toDisplayString((_$setup$details$submi7 = $setup.details.submitUserInfo) === null || _$setup$details$submi7 === void 0 ? void 0 : _$setup$details$submi7.callAddress), 1 /* TEXT */)], 64 /* STABLE_FRAGMENT */)) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"div\", {\n    class: \"SuggestDetailContent\",\n    innerHTML: $setup.details.content\n  }, null, 8 /* PROPS */, _hoisted_22), _cache[2] || (_cache[2] = _createElementVNode(\"div\", {\n    class: \"SuggestDetailInfoName\"\n  }, \"建议清单\", -1 /* HOISTED */)), _createCommentVNode(\" <div class=\\\"suggestionList\\\" v-for=\\\"(item, index) in details.proposalInventoryList\\\" :key=\\\"index\\\"> \"), _createCommentVNode(\" <div class=\\\"suggestionListIndex\\\">建议{{ index + 1 }}：</div> \"), _createCommentVNode(\" <div class=\\\"suggestionListName\\\">建议：<span>{{ item.content }}</span></div>\\r\\n      <div class=\\\"suggestionListDesc\\\">补充说明：<span>{{ item.replenish }}</span></div> \"), _createCommentVNode(\" </div> \"), _createCommentVNode(\" <div class=\\\"globalTable\\\"> \"), _createVNode(_component_el_table, {\n    ref: \"tableRef\",\n    \"row-key\": \"id\",\n    border: \"\",\n    data: $setup.details.proposalInventoryList\n  }, {\n    default: _withCtx(function () {\n      return [_createCommentVNode(\" <el-table-column label=\\\"序号\\\" min-width=\\\"120\\\" prop=\\\"userName\\\" /> \"), _createVNode(_component_el_table_column, {\n        label: \"建议\",\n        \"min-width\": \"120\",\n        prop: \"content\"\n      }), _createVNode(_component_el_table_column, {\n        label: \"答复\",\n        \"min-width\": \"120\",\n        prop: \"\"\n      })];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"data\"]), _createCommentVNode(\" </div> \"), _createVNode(_component_xyl_global_file, {\n    fileData: $setup.details.attachments\n  }, null, 8 /* PROPS */, [\"fileData\"]), _cache[3] || (_cache[3] = _createElementVNode(\"div\", {\n    class: \"SuggestDetailInfoName\"\n  }, \"提案相关情况\", -1 /* HOISTED */)), $setup.suggestOpenTypeName ? (_openBlock(), _createElementBlock(\"div\", _hoisted_23, [_createTextVNode(_toDisplayString($setup.suggestOpenTypeName) + \"：\", 1 /* TEXT */), _createElementVNode(\"span\", null, _toDisplayString((_$setup$details$sugge = $setup.details.suggestOpenType) === null || _$setup$details$sugge === void 0 ? void 0 : _$setup$details$sugge.label), 1 /* TEXT */)])) : _createCommentVNode(\"v-if\", true), $setup.suggestSurveyTypeName ? (_openBlock(), _createElementBlock(\"div\", _hoisted_24, [_createTextVNode(_toDisplayString($setup.suggestSurveyTypeName) + \"：\", 1 /* TEXT */), _createElementVNode(\"span\", null, _toDisplayString((_$setup$details$sugge2 = $setup.details.suggestSurveyType) === null || _$setup$details$sugge2 === void 0 ? void 0 : _$setup$details$sugge2.label), 1 /* TEXT */)])) : _createCommentVNode(\"v-if\", true), $setup.isMakeMineJobName ? (_openBlock(), _createElementBlock(\"div\", _hoisted_25, [_createTextVNode(_toDisplayString($setup.isMakeMineJobName) + \"：\", 1 /* TEXT */), _createElementVNode(\"span\", null, _toDisplayString((_$setup$details$isMak = $setup.details.isMakeMineJob) === null || _$setup$details$isMak === void 0 ? void 0 : _$setup$details$isMak.label), 1 /* TEXT */)])) : _createCommentVNode(\"v-if\", true), $setup.notHandleTimeTypeName ? (_openBlock(), _createElementBlock(\"div\", _hoisted_26, [_createTextVNode(_toDisplayString($setup.notHandleTimeTypeName) + \"：\", 1 /* TEXT */), _createElementVNode(\"span\", null, _toDisplayString((_$setup$details$notHa = $setup.details.notHandleTimeType) === null || _$setup$details$notHa === void 0 ? void 0 : _$setup$details$notHa.label), 1 /* TEXT */)])) : _createCommentVNode(\"v-if\", true), $setup.isHopeEnhanceTalkName ? (_openBlock(), _createElementBlock(\"div\", _hoisted_27, [_createTextVNode(_toDisplayString($setup.isHopeEnhanceTalkName) + \"：\", 1 /* TEXT */), _createElementVNode(\"span\", null, _toDisplayString((_$setup$details$isHop = $setup.details.isHopeEnhanceTalk) === null || _$setup$details$isHop === void 0 ? void 0 : _$setup$details$isHop.label), 1 /* TEXT */)])) : _createCommentVNode(\"v-if\", true), $setup.isNeedPaperAnswerName ? (_openBlock(), _createElementBlock(\"div\", _hoisted_28, [_createTextVNode(_toDisplayString($setup.isNeedPaperAnswerName) + \"：\", 1 /* TEXT */), _createElementVNode(\"span\", null, _toDisplayString((_$setup$details$isNee = $setup.details.isNeedPaperAnswer) === null || _$setup$details$isNee === void 0 ? void 0 : _$setup$details$isNee.label), 1 /* TEXT */)])) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"div\", _hoisted_29, \"希望送交办单位：\" + _toDisplayString((_$setup$details$hopeH = $setup.details.hopeHandleOfficeIds) === null || _$setup$details$hopeH === void 0 ? void 0 : _$setup$details$hopeH.map(function (v) {\n    return v.officeName;\n  }).join('、')), 1 /* TEXT */), (_$setup$details$joinU = $setup.details.joinUsers) !== null && _$setup$details$joinU !== void 0 && _$setup$details$joinU.length ? (_openBlock(), _createElementBlock(\"div\", _hoisted_30, \"提案联名人\")) : _createCommentVNode(\"v-if\", true), (_$setup$details$joinU2 = $setup.details.joinUsers) !== null && _$setup$details$joinU2 !== void 0 && _$setup$details$joinU2.length ? (_openBlock(), _createElementBlock(\"div\", _hoisted_31, [_cache[0] || (_cache[0] = _createStaticVNode(\"<div class=\\\"SuggestDetailTableHead\\\"><div class=\\\"SuggestDetailTableItem row1\\\">姓名</div><div class=\\\"SuggestDetailTableItem row1\\\">委员证号</div><div class=\\\"SuggestDetailTableItem row1\\\">联系电话</div><div class=\\\"SuggestDetailTableItem row3\\\">通讯地址</div><div class=\\\"SuggestDetailTableItem row1\\\">是否同意</div></div>\", 1)), (_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.details.joinUsers, function (item) {\n    return _openBlock(), _createElementBlock(\"div\", {\n      class: \"SuggestDetailTableBody\",\n      key: item.userId\n    }, [_createElementVNode(\"div\", _hoisted_32, _toDisplayString(item.userName), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_33, _toDisplayString(item.cardNumber), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_34, _toDisplayString(item.mobile), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_35, _toDisplayString(item.callAddress), 1 /* TEXT */), !item.agreeStatus ? (_openBlock(), _createElementBlock(\"div\", _hoisted_36)) : _createCommentVNode(\"v-if\", true), item.agreeStatus === 1 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_37, \"已操作同意\")) : _createCommentVNode(\"v-if\", true), item.agreeStatus === 2 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_38, \"已操作不同意\")) : _createCommentVNode(\"v-if\", true)]);\n  }), 128 /* KEYED_FRAGMENT */))])) : _createCommentVNode(\"v-if\", true), (_$setup$details$conta = $setup.details.contacters) !== null && _$setup$details$conta !== void 0 && _$setup$details$conta.length ? (_openBlock(), _createElementBlock(\"div\", _hoisted_39, \"提案联系人\")) : _createCommentVNode(\"v-if\", true), (_$setup$details$conta2 = $setup.details.contacters) !== null && _$setup$details$conta2 !== void 0 && _$setup$details$conta2.length ? (_openBlock(), _createElementBlock(\"div\", _hoisted_40, [_cache[1] || (_cache[1] = _createElementVNode(\"div\", {\n    class: \"SuggestDetailTableHead\"\n  }, [_createElementVNode(\"div\", {\n    class: \"SuggestDetailTableItem row1\"\n  }, \"联系人姓名\"), _createElementVNode(\"div\", {\n    class: \"SuggestDetailTableItem row1\"\n  }, \"联系人电话\"), _createElementVNode(\"div\", {\n    class: \"SuggestDetailTableItem row4\"\n  }, \"联系人通讯地址\")], -1 /* HOISTED */)), (_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.details.contacters, function (item) {\n    return _openBlock(), _createElementBlock(\"div\", {\n      class: \"SuggestDetailTableBody\",\n      key: item.id\n    }, [_createElementVNode(\"div\", _hoisted_41, _toDisplayString(item.contacterName), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_42, _toDisplayString(item.contacterMobile), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_43, _toDisplayString(item.contacterAddress), 1 /* TEXT */)]);\n  }), 128 /* KEYED_FRAGMENT */))])) : _createCommentVNode(\"v-if\", true)]);\n}", "map": {"version": 3, "names": ["class", "key", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_global_dynamic_title", "templateCode", "params", "businessId", "$setup", "props", "id", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_toDisplayString", "details", "serialNumber", "_hoisted_5", "format", "createDate", "_hoisted_6", "bigThemeName", "_hoisted_7", "_createCommentVNode", "smallThemeName", "_hoisted_8", "_hoisted_9", "title", "suggestSubmitWay", "_hoisted_10", "suggestUserName", "_Fragment", "_hoisted_11", "_hoisted_12", "_$setup$details$submi", "submitUserInfo", "userName", "_hoisted_13", "_$setup$details$submi2", "cardNumber", "_hoisted_14", "_hoisted_15", "_$setup$details$secto", "sectorType", "label", "_hoisted_16", "_$setup$details$submi3", "mobile", "_hoisted_17", "_hoisted_18", "_$setup$details$submi4", "officePhone", "_hoisted_19", "_$setup$details$submi5", "postcode", "_hoisted_20", "_$setup$details$submi6", "position", "_hoisted_21", "_$setup$details$submi7", "call<PERSON>dd<PERSON>", "innerHTML", "content", "_hoisted_22", "_component_el_table", "ref", "border", "data", "proposalInventoryList", "default", "_withCtx", "_component_el_table_column", "prop", "_", "_component_xyl_global_file", "fileData", "attachments", "suggestOpenTypeName", "_hoisted_23", "_createTextVNode", "_$setup$details$sugge", "suggestOpenType", "suggestSurveyTypeName", "_hoisted_24", "_$setup$details$sugge2", "suggestSurveyType", "isMakeMineJobName", "_hoisted_25", "_$setup$details$isMak", "isMakeMineJob", "notHandleTimeTypeName", "_hoisted_26", "_$setup$details$notHa", "notHandleTimeType", "isHopeEnhanceTalkName", "_hoisted_27", "_$setup$details$isHop", "isHopeEnhanceTalk", "isNeedPaperAnswerName", "_hoisted_28", "_$setup$details$isNee", "isNeedPaperAnswer", "_hoisted_29", "_$setup$details$hopeH", "hopeHandleOfficeIds", "map", "v", "officeName", "join", "joinUsers", "_$setup$details$joinU", "length", "_hoisted_30", "_$setup$details$joinU2", "_hoisted_31", "_createStaticVNode", "_renderList", "item", "userId", "_hoisted_32", "_hoisted_33", "_hoisted_34", "_hoisted_35", "agreeS<PERSON>us", "_hoisted_36", "_hoisted_37", "_hoisted_38", "contacters", "_$setup$details$conta", "_hoisted_39", "_$setup$details$conta2", "_hoisted_40", "_hoisted_41", "contacterName", "_hoisted_42", "contacterMobile", "_hoisted_43", "contacter<PERSON><PERSON><PERSON>"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestDetail\\component\\SuggestBasicInfo.vue"], "sourcesContent": ["<template>\r\n  <div class=\"SuggestBasicInfo\">\r\n    <global-dynamic-title templateCode=\"proposal_title\" :params=\"{ businessId: props.id }\"></global-dynamic-title>\r\n    <div class=\"SuggestDetailInfo\">\r\n      <div class=\"SuggestDetailNumberTime\">\r\n        <div class=\"SuggestDetailNumber\">提案编号：{{ details.serialNumber }}</div>\r\n        <div class=\"SuggestDetailTime\">提案时间：{{ format(details.createDate) }}</div>\r\n      </div>\r\n      <div class=\"SuggestDetailType\">\r\n        <div class=\"SuggestDetailBigType\" v-if=\"details.bigThemeName\">{{ details.bigThemeName }}</div>\r\n        <div class=\"SuggestDetailSmallType\" v-if=\"details.smallThemeName\">{{ details.smallThemeName }}</div>\r\n      </div>\r\n    </div>\r\n    <div class=\"SuggestDetailTitle\">{{ details.title }}</div>\r\n    <template v-if=\"details.suggestSubmitWay === 'team'\">\r\n      <div class=\"SuggestDetailInfoItem\">提案者：{{ details.suggestUserName }}</div>\r\n    </template>\r\n    <template v-if=\"details.suggestSubmitWay === 'cppcc_member'\">\r\n      <div class=\"SuggestDetailInfo\">\r\n        <div class=\"SuggestDetailInfoItem\">提案者：{{ details.submitUserInfo?.userName }}</div>\r\n        <div class=\"SuggestDetailInfoItem\">委员证号：{{ details.submitUserInfo?.cardNumber }}</div>\r\n      </div>\r\n      <div class=\"SuggestDetailInfo\">\r\n        <div class=\"SuggestDetailInfoItem\">界别：{{ details.sectorType?.label }}</div>\r\n        <div class=\"SuggestDetailInfoItem\">联系电话：{{ details.submitUserInfo?.mobile }}</div>\r\n      </div>\r\n      <div class=\"SuggestDetailInfo\">\r\n        <div class=\"SuggestDetailInfoItem\">办公电话：{{ details.submitUserInfo?.officePhone }}</div>\r\n        <div class=\"SuggestDetailInfoItem\">邮政编码：{{ details.submitUserInfo?.postcode }}</div>\r\n      </div>\r\n      <div class=\"SuggestDetailInfoItem\">单位及职务：{{ details.submitUserInfo?.position }}</div>\r\n      <div class=\"SuggestDetailInfoItem\">通讯地址：{{ details.submitUserInfo?.callAddress }}</div>\r\n    </template>\r\n    <div class=\"SuggestDetailContent\" v-html=\"details.content\"></div>\r\n    <div class=\"SuggestDetailInfoName\">建议清单</div>\r\n    <!-- <div class=\"suggestionList\" v-for=\"(item, index) in details.proposalInventoryList\" :key=\"index\"> -->\r\n    <!-- <div class=\"suggestionListIndex\">建议{{ index + 1 }}：</div> -->\r\n    <!-- <div class=\"suggestionListName\">建议：<span>{{ item.content }}</span></div>\r\n      <div class=\"suggestionListDesc\">补充说明：<span>{{ item.replenish }}</span></div> -->\r\n    <!-- </div> -->\r\n    <!-- <div class=\"globalTable\"> -->\r\n    <el-table ref=\"tableRef\" row-key=\"id\" border :data=\"details.proposalInventoryList\" >\r\n      <!-- <el-table-column label=\"序号\" min-width=\"120\" prop=\"userName\" /> -->\r\n      <el-table-column label=\"建议\" min-width=\"120\" prop=\"content\" />\r\n      <el-table-column label=\"答复\" min-width=\"120\" prop=\"\" />\r\n    </el-table>\r\n    <!-- </div> -->\r\n    <xyl-global-file :fileData=\"details.attachments\"></xyl-global-file>\r\n    <div class=\"SuggestDetailInfoName\">提案相关情况</div>\r\n    <div class=\"SuggestDetailInfoItem\" v-if=\"suggestOpenTypeName\">{{ suggestOpenTypeName }}：<span>{{\r\n      details.suggestOpenType?.label }}</span></div>\r\n    <div class=\"SuggestDetailInfoItem\" v-if=\"suggestSurveyTypeName\">{{ suggestSurveyTypeName }}：<span>{{\r\n      details.suggestSurveyType?.label }}</span>\r\n    </div>\r\n    <div class=\"SuggestDetailInfoItem\" v-if=\"isMakeMineJobName\">{{ isMakeMineJobName }}：<span>{{\r\n      details.isMakeMineJob?.label }}</span></div>\r\n    <div class=\"SuggestDetailInfoItem\" v-if=\"notHandleTimeTypeName\">{{ notHandleTimeTypeName }}：<span>{{\r\n      details.notHandleTimeType?.label }}</span>\r\n    </div>\r\n    <div class=\"SuggestDetailInfoItem\" v-if=\"isHopeEnhanceTalkName\">{{ isHopeEnhanceTalkName }}：<span>{{\r\n      details.isHopeEnhanceTalk?.label }}</span>\r\n    </div>\r\n    <div class=\"SuggestDetailInfoItem\" v-if=\"isNeedPaperAnswerName\">{{ isNeedPaperAnswerName }}：<span>{{\r\n      details.isNeedPaperAnswer?.label }}</span>\r\n    </div>\r\n    <div class=\"SuggestDetailInfoItem\">希望送交办单位：{{details.hopeHandleOfficeIds?.map(v => v.officeName).join('、')}}</div>\r\n    <div class=\"SuggestDetailInfoName\" v-if=\"details.joinUsers?.length\">提案联名人</div>\r\n    <div class=\"SuggestDetailTable\" v-if=\"details.joinUsers?.length\">\r\n      <div class=\"SuggestDetailTableHead\">\r\n        <div class=\"SuggestDetailTableItem row1\">姓名</div>\r\n        <div class=\"SuggestDetailTableItem row1\">委员证号</div>\r\n        <div class=\"SuggestDetailTableItem row1\">联系电话</div>\r\n        <div class=\"SuggestDetailTableItem row3\">通讯地址</div>\r\n        <div class=\"SuggestDetailTableItem row1\">是否同意</div>\r\n      </div>\r\n      <div class=\"SuggestDetailTableBody\" v-for=\"item in details.joinUsers\" :key=\"item.userId\">\r\n        <div class=\"SuggestDetailTableItem row1\">{{ item.userName }}</div>\r\n        <div class=\"SuggestDetailTableItem row1\">{{ item.cardNumber }}</div>\r\n        <div class=\"SuggestDetailTableItem row1\">{{ item.mobile }}</div>\r\n        <div class=\"SuggestDetailTableItem row3\">{{ item.callAddress }}</div>\r\n        <div class=\"SuggestDetailTableItem row1\" v-if=\"!item.agreeStatus\"></div>\r\n        <div class=\"SuggestDetailTableItem row1\" v-if=\"item.agreeStatus === 1\">已操作同意</div>\r\n        <div class=\"SuggestDetailTableItem row1\" v-if=\"item.agreeStatus === 2\">已操作不同意</div>\r\n      </div>\r\n    </div>\r\n    <div class=\"SuggestDetailInfoName\" v-if=\"details.contacters?.length\">提案联系人</div>\r\n    <div class=\"SuggestDetailTable\" v-if=\"details.contacters?.length\">\r\n      <div class=\"SuggestDetailTableHead\">\r\n        <div class=\"SuggestDetailTableItem row1\">联系人姓名</div>\r\n        <div class=\"SuggestDetailTableItem row1\">联系人电话</div>\r\n        <div class=\"SuggestDetailTableItem row4\">联系人通讯地址</div>\r\n      </div>\r\n      <div class=\"SuggestDetailTableBody\" v-for=\"item in details.contacters\" :key=\"item.id\">\r\n        <div class=\"SuggestDetailTableItem row1\">{{ item.contacterName }}</div>\r\n        <div class=\"SuggestDetailTableItem row1\">{{ item.contacterMobile }}</div>\r\n        <div class=\"SuggestDetailTableItem row4\">{{ item.contacterAddress }}</div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'SuggestBasicInfo' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, computed, onActivated } from 'vue'\r\nimport { format } from 'common/js/time.js'\r\nconst props = defineProps({ id: { type: String, default: '' }, details: { type: Object, default: () => ({}) } })\r\nconst details = computed(() => props.details)\r\nconst suggestOpenTypeName = ref('')\r\nconst suggestSurveyTypeName = ref('')\r\nconst notHandleTimeTypeName = ref('')\r\nconst isHopeEnhanceTalkName = ref('')\r\nconst isMakeMineJobName = ref('')\r\nconst isNeedPaperAnswerName = ref('')\r\nonActivated(() => { dictionaryNameData() })\r\n\r\nconst dictionaryNameData = async () => {\r\n  const { data } = await api.dictionaryNameData({\r\n    dictCodes: ['suggest_open_type', 'suggest_survey_type', 'not_handle_time_type', 'is_hope_enhance_talk', 'is_make_mine_job', 'is_need_paper_answer']\r\n  })\r\n  suggestOpenTypeName.value = data.suggest_open_type\r\n  suggestSurveyTypeName.value = data.suggest_survey_type\r\n  notHandleTimeTypeName.value = data.not_handle_time_type\r\n  isHopeEnhanceTalkName.value = data.is_hope_enhance_talk\r\n  isMakeMineJobName.value = data.is_make_mine_job\r\n  isNeedPaperAnswerName.value = data.is_need_paper_answer\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.SuggestBasicInfo {\r\n  width: 100%;\r\n  padding: var(--zy-distance-one) 0;\r\n\r\n  .SuggestDetailName {\r\n    font-size: var(--zy-title-font-size);\r\n    font-weight: bold;\r\n    border-bottom: 2px solid var(--zy-el-color-primary);\r\n    text-align: center;\r\n    padding: 20px 0;\r\n\r\n    span {\r\n      color: red;\r\n    }\r\n  }\r\n\r\n  .SuggestDetailTitle {\r\n    width: 100%;\r\n    padding: 10px 0;\r\n    font-weight: bold;\r\n    font-size: var(--zy-title-font-size);\r\n    line-height: var(--zy-line-height);\r\n  }\r\n\r\n  .SuggestDetailInfo {\r\n    width: 100%;\r\n    display: flex;\r\n    justify-content: space-between;\r\n\r\n    .SuggestDetailNumberTime {\r\n      display: flex;\r\n      padding-top: 20px;\r\n      padding-bottom: 10px;\r\n\r\n      .SuggestDetailNumber {\r\n        font-weight: bold;\r\n        font-size: var(--zy-text-font-size);\r\n        line-height: var(--zy-line-height);\r\n      }\r\n\r\n      .SuggestDetailTime {\r\n        color: var(--zy-el-text-color-regular);\r\n        font-size: var(--zy-text-font-size);\r\n        line-height: var(--zy-line-height);\r\n        margin-left: 40px;\r\n      }\r\n    }\r\n\r\n    .SuggestDetailType {\r\n      display: flex;\r\n      align-items: center;\r\n\r\n      .SuggestDetailBigType {\r\n        color: var(--zy-el-color-warning);\r\n        background-color: var(--zy-el-color-warning-light-9);\r\n        font-size: var(--zy-text-font-size);\r\n        line-height: var(--zy-line-height);\r\n        padding: 0 10px;\r\n      }\r\n\r\n      .SuggestDetailSmallType {\r\n        color: var(--zy-el-color-success);\r\n        background-color: var(--zy-el-color-success-light-9);\r\n        font-size: var(--zy-text-font-size);\r\n        line-height: var(--zy-line-height);\r\n        padding: 0 10px;\r\n        margin-left: 20px;\r\n      }\r\n    }\r\n\r\n    .SuggestDetailInfoItem {\r\n      width: 50%;\r\n      padding: 10px 0;\r\n    }\r\n  }\r\n\r\n  .SuggestDetailInfoName {\r\n    padding-top: 15px;\r\n    padding-bottom: 5px;\r\n    font-weight: bold;\r\n    font-size: var(--zy-text-font-size);\r\n    line-height: var(--zy-line-height);\r\n  }\r\n\r\n  .SuggestDetailInfoItem {\r\n    width: 100%;\r\n    padding: 5px 0;\r\n    font-size: var(--zy-text-font-size);\r\n    line-height: var(--zy-line-height);\r\n\r\n    span {\r\n      font-weight: bold;\r\n    }\r\n  }\r\n\r\n  .SuggestDetailContent {\r\n    padding: 20px 0;\r\n    overflow: hidden;\r\n    line-height: var(--zy-line-height);\r\n\r\n    img,\r\n    video {\r\n      max-width: 100%;\r\n      height: auto !important;\r\n    }\r\n\r\n    table {\r\n      max-width: 100%;\r\n      border-collapse: collapse;\r\n      border-spacing: 0;\r\n\r\n      tr {\r\n        page-break-inside: avoid;\r\n      }\r\n    }\r\n  }\r\n\r\n  .SuggestDetailTable {\r\n    width: 100%;\r\n    margin: 5px 0;\r\n    border-top: 1px solid var(--zy-el-border-color-lighter);\r\n    border-right: 1px solid var(--zy-el-border-color-lighter);\r\n\r\n    .SuggestDetailTableHead,\r\n    .SuggestDetailTableBody {\r\n      width: 100%;\r\n      display: flex;\r\n      border-bottom: 1px solid var(--zy-el-border-color-lighter);\r\n    }\r\n\r\n    .SuggestDetailTableHead {\r\n      background-color: var(--zy-el-color-info-light-9);\r\n    }\r\n\r\n    .SuggestDetailTableBody {\r\n      border-bottom: 1px solid var(--zy-el-border-color-lighter);\r\n    }\r\n\r\n    .row1 {\r\n      flex: 1;\r\n    }\r\n\r\n    .row2 {\r\n      flex: 2;\r\n    }\r\n\r\n    .row3 {\r\n      flex: 3;\r\n    }\r\n\r\n    .row4 {\r\n      flex: 4;\r\n    }\r\n\r\n    .SuggestDetailTableItem {\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      border-left: 1px solid var(--zy-el-border-color-lighter);\r\n      font-size: var(--zy-text-font-size);\r\n      line-height: var(--zy-line-height);\r\n      padding: 10px;\r\n    }\r\n  }\r\n\r\n  .suggestionList {\r\n    box-shadow: 0 2px 4px rgba(0, 0, 0, .12),\r\n      0 0 6px rgba(0, 0, 0, .04);\r\n    padding: 10px 20px;\r\n    margin-top: 10px;\r\n\r\n    .suggestionListIndex {}\r\n\r\n    .suggestionListName {\r\n      color: #000;\r\n      font-size: 14px;\r\n    }\r\n\r\n    .suggestionListDesc {\r\n      color: #000;\r\n      font-size: 14px;\r\n      margin-top: 5px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAkB;;EAEtBA,KAAK,EAAC;AAAmB;;EACvBA,KAAK,EAAC;AAAyB;;EAC7BA,KAAK,EAAC;AAAqB;;EAC3BA,KAAK,EAAC;AAAmB;;EAE3BA,KAAK,EAAC;AAAmB;;EARpCC,GAAA;EASaD,KAAK,EAAC;;;EATnBC,GAAA;EAUaD,KAAK,EAAC;;;EAGVA,KAAK,EAAC;AAAoB;;EAbnCC,GAAA;EAeWD,KAAK,EAAC;;;EAGNA,KAAK,EAAC;AAAmB;;EACvBA,KAAK,EAAC;AAAuB;;EAC7BA,KAAK,EAAC;AAAuB;;EAE/BA,KAAK,EAAC;AAAmB;;EACvBA,KAAK,EAAC;AAAuB;;EAC7BA,KAAK,EAAC;AAAuB;;EAE/BA,KAAK,EAAC;AAAmB;;EACvBA,KAAK,EAAC;AAAuB;;EAC7BA,KAAK,EAAC;AAAuB;;EAE/BA,KAAK,EAAC;AAAuB;;EAC7BA,KAAK,EAAC;AAAuB;kBA/BxC;;EAAAC,GAAA;EAiDSD,KAAK,EAAC;;;EAjDfC,GAAA;EAmDSD,KAAK,EAAC;;;EAnDfC,GAAA;EAsDSD,KAAK,EAAC;;;EAtDfC,GAAA;EAwDSD,KAAK,EAAC;;;EAxDfC,GAAA;EA2DSD,KAAK,EAAC;;;EA3DfC,GAAA;EA8DSD,KAAK,EAAC;;;EAGNA,KAAK,EAAC;AAAuB;;EAjEtCC,GAAA;EAkESD,KAAK,EAAC;;;EAlEfC,GAAA;EAmESD,KAAK,EAAC;;;EASFA,KAAK,EAAC;AAA6B;;EACnCA,KAAK,EAAC;AAA6B;;EACnCA,KAAK,EAAC;AAA6B;;EACnCA,KAAK,EAAC;AAA6B;;EA/EhDC,GAAA;EAgFaD,KAAK,EAAC;;;EAhFnBC,GAAA;EAiFaD,KAAK,EAAC;;;EAjFnBC,GAAA;EAkFaD,KAAK,EAAC;;;EAlFnBC,GAAA;EAqFSD,KAAK,EAAC;;;EArFfC,GAAA;EAsFSD,KAAK,EAAC;;;EAOFA,KAAK,EAAC;AAA6B;;EACnCA,KAAK,EAAC;AAA6B;;EACnCA,KAAK,EAAC;AAA6B;;;;;;;uBA9F9CE,mBAAA,CAiGM,OAjGNC,UAiGM,GAhGJC,YAAA,CAA8GC,+BAAA;IAAxFC,YAAY,EAAC,gBAAgB;IAAEC,MAAM;MAAAC,UAAA,EAAgBC,MAAA,CAAAC,KAAK,CAACC;IAAE;uCACnFC,mBAAA,CASM,OATNC,UASM,GARJD,mBAAA,CAGM,OAHNE,UAGM,GAFJF,mBAAA,CAAsE,OAAtEG,UAAsE,EAArC,OAAK,GAAAC,gBAAA,CAAGP,MAAA,CAAAQ,OAAO,CAACC,YAAY,kBAC7DN,mBAAA,CAA0E,OAA1EO,UAA0E,EAA3C,OAAK,GAAAH,gBAAA,CAAGP,MAAA,CAAAW,MAAM,CAACX,MAAA,CAAAQ,OAAO,CAACI,UAAU,kB,GAElET,mBAAA,CAGM,OAHNU,UAGM,GAFoCb,MAAA,CAAAQ,OAAO,CAACM,YAAY,I,cAA5DrB,mBAAA,CAA8F,OAA9FsB,UAA8F,EAAAR,gBAAA,CAA7BP,MAAA,CAAAQ,OAAO,CAACM,YAAY,oBAT7FE,mBAAA,gBAUkDhB,MAAA,CAAAQ,OAAO,CAACS,cAAc,I,cAAhExB,mBAAA,CAAoG,OAApGyB,UAAoG,EAAAX,gBAAA,CAA/BP,MAAA,CAAAQ,OAAO,CAACS,cAAc,oBAVnGD,mBAAA,e,KAaIb,mBAAA,CAAyD,OAAzDgB,UAAyD,EAAAZ,gBAAA,CAAtBP,MAAA,CAAAQ,OAAO,CAACY,KAAK,kBAChCpB,MAAA,CAAAQ,OAAO,CAACa,gBAAgB,e,cACtC5B,mBAAA,CAA0E,OAA1E6B,WAA0E,EAAvC,MAAI,GAAAf,gBAAA,CAAGP,MAAA,CAAAQ,OAAO,CAACe,eAAe,oBAfvEP,mBAAA,gBAiBoBhB,MAAA,CAAAQ,OAAO,CAACa,gBAAgB,uB,cAAxC5B,mBAAA,CAeW+B,SAAA;IAhCfhC,GAAA;EAAA,IAkBMW,mBAAA,CAGM,OAHNsB,WAGM,GAFJtB,mBAAA,CAAmF,OAAnFuB,WAAmF,EAAhD,MAAI,GAAAnB,gBAAA,EAAAoB,qBAAA,GAAG3B,MAAA,CAAAQ,OAAO,CAACoB,cAAc,cAAAD,qBAAA,uBAAtBA,qBAAA,CAAwBE,QAAQ,kBAC1E1B,mBAAA,CAAsF,OAAtF2B,WAAsF,EAAnD,OAAK,GAAAvB,gBAAA,EAAAwB,sBAAA,GAAG/B,MAAA,CAAAQ,OAAO,CAACoB,cAAc,cAAAG,sBAAA,uBAAtBA,sBAAA,CAAwBC,UAAU,iB,GAE/E7B,mBAAA,CAGM,OAHN8B,WAGM,GAFJ9B,mBAAA,CAA2E,OAA3E+B,WAA2E,EAAxC,KAAG,GAAA3B,gBAAA,EAAA4B,qBAAA,GAAGnC,MAAA,CAAAQ,OAAO,CAAC4B,UAAU,cAAAD,qBAAA,uBAAlBA,qBAAA,CAAoBE,KAAK,kBAClElC,mBAAA,CAAkF,OAAlFmC,WAAkF,EAA/C,OAAK,GAAA/B,gBAAA,EAAAgC,sBAAA,GAAGvC,MAAA,CAAAQ,OAAO,CAACoB,cAAc,cAAAW,sBAAA,uBAAtBA,sBAAA,CAAwBC,MAAM,iB,GAE3ErC,mBAAA,CAGM,OAHNsC,WAGM,GAFJtC,mBAAA,CAAuF,OAAvFuC,WAAuF,EAApD,OAAK,GAAAnC,gBAAA,EAAAoC,sBAAA,GAAG3C,MAAA,CAAAQ,OAAO,CAACoB,cAAc,cAAAe,sBAAA,uBAAtBA,sBAAA,CAAwBC,WAAW,kBAC9EzC,mBAAA,CAAoF,OAApF0C,WAAoF,EAAjD,OAAK,GAAAtC,gBAAA,EAAAuC,sBAAA,GAAG9C,MAAA,CAAAQ,OAAO,CAACoB,cAAc,cAAAkB,sBAAA,uBAAtBA,sBAAA,CAAwBC,QAAQ,iB,GAE7E5C,mBAAA,CAAqF,OAArF6C,WAAqF,EAAlD,QAAM,GAAAzC,gBAAA,EAAA0C,sBAAA,GAAGjD,MAAA,CAAAQ,OAAO,CAACoB,cAAc,cAAAqB,sBAAA,uBAAtBA,sBAAA,CAAwBC,QAAQ,kBAC5E/C,mBAAA,CAAuF,OAAvFgD,WAAuF,EAApD,OAAK,GAAA5C,gBAAA,EAAA6C,sBAAA,GAAGpD,MAAA,CAAAQ,OAAO,CAACoB,cAAc,cAAAwB,sBAAA,uBAAtBA,sBAAA,CAAwBC,WAAW,iB,+BA/BpFrC,mBAAA,gBAiCIb,mBAAA,CAAiE;IAA5DZ,KAAK,EAAC,sBAAsB;IAAC+D,SAAwB,EAAhBtD,MAAA,CAAAQ,OAAO,CAAC+C;0BAjCtDC,WAAA,G,0BAkCIrD,mBAAA,CAA6C;IAAxCZ,KAAK,EAAC;EAAuB,GAAC,MAAI,sBACvCyB,mBAAA,4GAAyG,EACzGA,mBAAA,iEAAkE,EAClEA,mBAAA,wKACkF,EAClFA,mBAAA,YAAe,EACfA,mBAAA,iCAAkC,EAClCrB,YAAA,CAIW8D,mBAAA;IAJDC,GAAG,EAAC,UAAU;IAAC,SAAO,EAAC,IAAI;IAACC,MAAM,EAAN,EAAM;IAAEC,IAAI,EAAE5D,MAAA,CAAAQ,OAAO,CAACqD;;IAzChEC,OAAA,EAAAC,QAAA,CA0CM;MAAA,OAAuE,CAAvE/C,mBAAA,0EAAuE,EACvErB,YAAA,CAA6DqE,0BAAA;QAA5C3B,KAAK,EAAC,IAAI;QAAC,WAAS,EAAC,KAAK;QAAC4B,IAAI,EAAC;UACjDtE,YAAA,CAAsDqE,0BAAA;QAArC3B,KAAK,EAAC,IAAI;QAAC,WAAS,EAAC,KAAK;QAAC4B,IAAI,EAAC;;;IA5CvDC,CAAA;+BA8CIlD,mBAAA,YAAe,EACfrB,YAAA,CAAmEwE,0BAAA;IAAjDC,QAAQ,EAAEpE,MAAA,CAAAQ,OAAO,CAAC6D;mEACpClE,mBAAA,CAA+C;IAA1CZ,KAAK,EAAC;EAAuB,GAAC,QAAM,sBACAS,MAAA,CAAAsE,mBAAmB,I,cAA5D7E,mBAAA,CACgD,OADhD8E,WACgD,GAlDpDC,gBAAA,CAAAjE,gBAAA,CAiDqEP,MAAA,CAAAsE,mBAAmB,IAAG,GAAC,iBAAAnE,mBAAA,CAC9C,cAAAI,gBAAA,EAAAkE,qBAAA,GAAxCzE,MAAA,CAAAQ,OAAO,CAACkE,eAAe,cAAAD,qBAAA,uBAAvBA,qBAAA,CAAyBpC,KAAK,iB,KAlDpCrB,mBAAA,gBAmD6ChB,MAAA,CAAA2E,qBAAqB,I,cAA9DlF,mBAAA,CAEM,OAFNmF,WAEM,GArDVJ,gBAAA,CAAAjE,gBAAA,CAmDuEP,MAAA,CAAA2E,qBAAqB,IAAG,GAAC,iBAAAxE,mBAAA,CAChD,cAAAI,gBAAA,EAAAsE,sBAAA,GAA1C7E,MAAA,CAAAQ,OAAO,CAACsE,iBAAiB,cAAAD,sBAAA,uBAAzBA,sBAAA,CAA2BxC,KAAK,iB,KApDtCrB,mBAAA,gBAsD6ChB,MAAA,CAAA+E,iBAAiB,I,cAA1DtF,mBAAA,CAC8C,OAD9CuF,WAC8C,GAvDlDR,gBAAA,CAAAjE,gBAAA,CAsDmEP,MAAA,CAAA+E,iBAAiB,IAAG,GAAC,iBAAA5E,mBAAA,CAC5C,cAAAI,gBAAA,EAAA0E,qBAAA,GAAtCjF,MAAA,CAAAQ,OAAO,CAAC0E,aAAa,cAAAD,qBAAA,uBAArBA,qBAAA,CAAuB5C,KAAK,iB,KAvDlCrB,mBAAA,gBAwD6ChB,MAAA,CAAAmF,qBAAqB,I,cAA9D1F,mBAAA,CAEM,OAFN2F,WAEM,GA1DVZ,gBAAA,CAAAjE,gBAAA,CAwDuEP,MAAA,CAAAmF,qBAAqB,IAAG,GAAC,iBAAAhF,mBAAA,CAChD,cAAAI,gBAAA,EAAA8E,qBAAA,GAA1CrF,MAAA,CAAAQ,OAAO,CAAC8E,iBAAiB,cAAAD,qBAAA,uBAAzBA,qBAAA,CAA2BhD,KAAK,iB,KAzDtCrB,mBAAA,gBA2D6ChB,MAAA,CAAAuF,qBAAqB,I,cAA9D9F,mBAAA,CAEM,OAFN+F,WAEM,GA7DVhB,gBAAA,CAAAjE,gBAAA,CA2DuEP,MAAA,CAAAuF,qBAAqB,IAAG,GAAC,iBAAApF,mBAAA,CAChD,cAAAI,gBAAA,EAAAkF,qBAAA,GAA1CzF,MAAA,CAAAQ,OAAO,CAACkF,iBAAiB,cAAAD,qBAAA,uBAAzBA,qBAAA,CAA2BpD,KAAK,iB,KA5DtCrB,mBAAA,gBA8D6ChB,MAAA,CAAA2F,qBAAqB,I,cAA9DlG,mBAAA,CAEM,OAFNmG,WAEM,GAhEVpB,gBAAA,CAAAjE,gBAAA,CA8DuEP,MAAA,CAAA2F,qBAAqB,IAAG,GAAC,iBAAAxF,mBAAA,CAChD,cAAAI,gBAAA,EAAAsF,qBAAA,GAA1C7F,MAAA,CAAAQ,OAAO,CAACsF,iBAAiB,cAAAD,qBAAA,uBAAzBA,qBAAA,CAA2BxD,KAAK,iB,KA/DtCrB,mBAAA,gBAiEIb,mBAAA,CAAkH,OAAlH4F,WAAkH,EAA/E,UAAQ,GAAAxF,gBAAA,EAAAyF,qBAAA,GAAEhG,MAAA,CAAAQ,OAAO,CAACyF,mBAAmB,cAAAD,qBAAA,uBAA3BA,qBAAA,CAA6BE,GAAG,CAAC,UAAAC,CAAC;IAAA,OAAIA,CAAC,CAACC,UAAU;EAAA,GAAEC,IAAI,uB,yBAC5DrG,MAAA,CAAAQ,OAAO,CAAC8F,SAAS,cAAAC,qBAAA,eAAjBA,qBAAA,CAAmBC,MAAM,I,cAAlE/G,mBAAA,CAA+E,OAA/EgH,WAA+E,EAAX,OAAK,KAlE7EzF,mBAAA,gB,0BAmE0ChB,MAAA,CAAAQ,OAAO,CAAC8F,SAAS,cAAAI,sBAAA,eAAjBA,sBAAA,CAAmBF,MAAM,I,cAA/D/G,mBAAA,CAiBM,OAjBNkH,WAiBM,G,0BApFVC,kBAAA,8T,kBA2EMnH,mBAAA,CAQM+B,SAAA,QAnFZqF,WAAA,CA2EyD7G,MAAA,CAAAQ,OAAO,CAAC8F,SAAS,EA3E1E,UA2EiDQ,IAAI;yBAA/CrH,mBAAA,CAQM;MARDF,KAAK,EAAC,wBAAwB;MAAoCC,GAAG,EAAEsH,IAAI,CAACC;QAC/E5G,mBAAA,CAAkE,OAAlE6G,WAAkE,EAAAzG,gBAAA,CAAtBuG,IAAI,CAACjF,QAAQ,kBACzD1B,mBAAA,CAAoE,OAApE8G,WAAoE,EAAA1G,gBAAA,CAAxBuG,IAAI,CAAC9E,UAAU,kBAC3D7B,mBAAA,CAAgE,OAAhE+G,WAAgE,EAAA3G,gBAAA,CAApBuG,IAAI,CAACtE,MAAM,kBACvDrC,mBAAA,CAAqE,OAArEgH,WAAqE,EAAA5G,gBAAA,CAAzBuG,IAAI,CAACzD,WAAW,kB,CACZyD,IAAI,CAACM,WAAW,I,cAAhE3H,mBAAA,CAAwE,OAAxE4H,WAAwE,KAhFhFrG,mBAAA,gBAiFuD8F,IAAI,CAACM,WAAW,U,cAA/D3H,mBAAA,CAAkF,OAAlF6H,WAAkF,EAAX,OAAK,KAjFpFtG,mBAAA,gBAkFuD8F,IAAI,CAACM,WAAW,U,cAA/D3H,mBAAA,CAAmF,OAAnF8H,WAAmF,EAAZ,QAAM,KAlFrFvG,mBAAA,e;sCAAAA,mBAAA,gB,yBAqF6ChB,MAAA,CAAAQ,OAAO,CAACgH,UAAU,cAAAC,qBAAA,eAAlBA,qBAAA,CAAoBjB,MAAM,I,cAAnE/G,mBAAA,CAAgF,OAAhFiI,WAAgF,EAAX,OAAK,KArF9E1G,mBAAA,gB,0BAsF0ChB,MAAA,CAAAQ,OAAO,CAACgH,UAAU,cAAAG,sBAAA,eAAlBA,sBAAA,CAAoBnB,MAAM,I,cAAhE/G,mBAAA,CAWM,OAXNmI,WAWM,G,0BAVJzH,mBAAA,CAIM;IAJDZ,KAAK,EAAC;EAAwB,IACjCY,mBAAA,CAAoD;IAA/CZ,KAAK,EAAC;EAA6B,GAAC,OAAK,GAC9CY,mBAAA,CAAoD;IAA/CZ,KAAK,EAAC;EAA6B,GAAC,OAAK,GAC9CY,mBAAA,CAAsD;IAAjDZ,KAAK,EAAC;EAA6B,GAAC,SAAO,E,yCAElDE,mBAAA,CAIM+B,SAAA,QAhGZqF,WAAA,CA4FyD7G,MAAA,CAAAQ,OAAO,CAACgH,UAAU,EA5F3E,UA4FiDV,IAAI;yBAA/CrH,mBAAA,CAIM;MAJDF,KAAK,EAAC,wBAAwB;MAAqCC,GAAG,EAAEsH,IAAI,CAAC5G;QAChFC,mBAAA,CAAuE,OAAvE0H,WAAuE,EAAAtH,gBAAA,CAA3BuG,IAAI,CAACgB,aAAa,kBAC9D3H,mBAAA,CAAyE,OAAzE4H,WAAyE,EAAAxH,gBAAA,CAA7BuG,IAAI,CAACkB,eAAe,kBAChE7H,mBAAA,CAA0E,OAA1E8H,WAA0E,EAAA1H,gBAAA,CAA9BuG,IAAI,CAACoB,gBAAgB,iB;sCA/FzElH,mBAAA,e", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}