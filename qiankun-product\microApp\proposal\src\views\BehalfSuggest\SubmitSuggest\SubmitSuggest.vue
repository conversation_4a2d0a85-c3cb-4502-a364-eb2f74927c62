<template>
  <el-scrollbar always class="SubmitSuggest" v-loading="loading" :lement-loading-text="loadingText">
    <div class="SubmitSuggestBody">
      <div class="SubmitSuggestNameBody">
        <global-dynamic-title templateCode="proposal_title"></global-dynamic-title>
      </div>
      <el-form ref="formRef" :model="form" :rules="rules" inline :show-message="false" class="globalPaperForm">
        <el-form-item label="提案提交类型" v-if="!typeShow" prop="suggestSubmitWay" class="SubmitSuggestTitle">
          <el-radio-group v-model="form.suggestSubmitWay" @change="submitTypeChange">
            <el-radio label="cppcc_member">委员提案</el-radio>
            <el-radio label="team">集体提案</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="提案标题" prop="title" class="SubmitSuggestTitle">
          <el-input v-model="form.title" placeholder="请输入提案标题" show-word-limit :maxlength="suggestTitleNumber"
            clearable />
        </el-form-item>
        <el-form-item v-show="form.suggestSubmitWay === 'cppcc_member'" label="提案者" prop="suggestUserId"
          class="SubmitSuggestLeft">
          <input-select-person v-model="form.suggestUserId" placeholder="请选择提案者" :disabled="disabled" :tabCode="tabCode"
            @callback="userCallback" />
        </el-form-item>
        <el-form-item v-show="form.suggestSubmitWay === 'cppcc_member'" label="委员证号">
          <el-input v-model="form.cardNumber" disabled />
        </el-form-item>
        <el-form-item v-show="form.suggestSubmitWay === 'cppcc_member'" label="界别" class="SubmitSuggestLeft">
          <el-input v-model="form.sectorType" disabled />
        </el-form-item>
        <el-form-item v-show="form.suggestSubmitWay === 'cppcc_member'" label="联系电话">
          <el-input v-model="form.mobile" disabled />
        </el-form-item>
        <el-form-item v-show="form.suggestSubmitWay === 'cppcc_member'" label="通讯地址" class="SubmitSuggestTitle">
          <el-input v-model="form.callAddress" disabled />
        </el-form-item>
        <el-form-item v-show="form.suggestSubmitWay === 'team'" label="提案者" prop="delegationId"
          class="SubmitSuggestTitle">
          <el-select v-model="form.delegationId" :disabled="isDisabled" placeholder="请选择集体提案单位" clearable>
            <el-option v-for="item in delegationData" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="是否联名提案" prop="isJoinProposal" class="SubmitSuggestTitle">
          <el-radio-group v-model="form.isJoinProposal" @change="JoinChange">
            <el-radio :label="1">是</el-radio>
            <el-radio :label="0">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="提案联名人" prop="joinUsers" v-if="form.isJoinProposal" class="SubmitSuggestTitle">
          <simple-select-person v-model="form.joinUsers" placeholder="请选择提案联名人"
            :filterUser="form.suggestUserId ? [form.suggestUserId] : []"
            :tabCode="['cppccMember']"></simple-select-person>
          <template v-if="whetherUseIntelligentize && queryType !== 'review'">
            <intelligent-assistant v-model:elIsShow="elIsShow" v-model="visibleIsShow">
              <SuggestRecommendUser :params="userParams" @callback="userInitCallback" @select="userSelect">
              </SuggestRecommendUser>
            </intelligent-assistant>
          </template>
        </el-form-item>
        <el-form-item label="提案内容" prop="content" class="SubmitSuggestTitle SubmitSuggestButton">
          <el-button @click="handleSimilarity(false)"
            v-if="whetherUseIntelligentize && queryType === 'review' && reviewShow" type="primary">相似度查询</el-button>
        </el-form-item>
        <TinyMceEditor v-model="form.content" :setting="tinyMceSetting" @count="handleContentCount"
          @blur="handleContentBlur" textRectify />
        <el-form-item class="opinionsSuggestionsList" label="意见建议清单">
          <span style="position: absolute;top: 0;left:-17%;color:#f56c6c;">*</span>
          <div class="opinionsSuggestionsListHead">
            <div class="opinionsSuggestionsListItem row3">建议</div>
            <div class="opinionsSuggestionsListItem row2">补充说明</div>
            <div class="opinionsSuggestionsListItem row1">操作</div>
          </div>
          <div class="opinionsSuggestionsListBody" v-for="item in opinionsSuggestionsList" :key="item.id">
            <div class="opinionsSuggestionsListItem row3">
              <el-input placeholder="请输入建议" v-model="item.suggestion" clearable maxlength="30" show-word-limit>
              </el-input>
            </div>
            <div class="opinionsSuggestionsListItem row2">
              <el-input placeholder="请输入补充说明" v-model="item.explanation" clearable>
              </el-input>
            </div>
            <div class="opinionsSuggestionsListItem row1">
              <el-link @click="newOpinionsSuggestions" v-if="opinionsSuggestionsList.length">
                <el-icon>
                  <CirclePlus />
                </el-icon>
              </el-link>
              <el-link v-if="opinionsSuggestionsList.length > 1" @click="delOpinionsSuggestions(item.id)">
                <el-icon>
                  <Remove />
                </el-icon>
              </el-link>
            </div>
          </div>
          <div style="border-top: 1px solid #3657C0;width: 100%;text-align: center;color: red;">
            提案建议清单为必填项，须高度概括、事权明晰、简洁明了，请勿粘帖建议原文内容</div>
        </el-form-item>
        <el-form-item label="上传附件" class="SubmitSuggestFormUpload">
          <xyl-upload-file :fileData="fileData" @fileUpload="fileUpload"
            :fileType="['jpg', 'png', 'gif', 'jpeg', 'txt', 'doc', 'docx', 'wps', 'ppt', 'pptx', 'pdf', 'ofd', 'xls', 'xlsx', 'zip', 'rar', 'amr', 'mp4', 'avi', 'wav', 'uof', 'rtf', 'eio']" />
        </el-form-item>
        <el-form-item label="提案相关情况" class="SubmitSuggestFormItem">
          <div class="SubmitSuggestFormInfo" v-if="suggestOpenTypeName">
            <div class="SubmitSuggestFormInfoText">{{ suggestOpenTypeName }}：</div>
            <el-radio-group v-model="form.suggestOpenType">
              <el-radio v-for="item in suggestOpenType" :key="item.key" :label="item.key">{{ item.name }}</el-radio>
            </el-radio-group>
          </div>
          <div class="SubmitSuggestFormInfo" v-if="suggestSurveyTypeName">
            <div class="SubmitSuggestFormInfoText">{{ suggestSurveyTypeName }}：</div>
            <el-radio-group v-model="form.suggestSurveyType">
              <el-radio v-for="item in suggestSurveyType" :key="item.key" :label="item.key">{{ item.name }}</el-radio>
            </el-radio-group>
          </div>
          <div class="SubmitSuggestFormInfo" v-if="isMakeMineJobName">
            <div class="SubmitSuggestFormInfoText">{{ isMakeMineJobName }}：</div>
            <el-radio-group v-model="form.isMakeMineJob">
              <el-radio v-for="item in isMakeMineJob" :key="item.key" :label="item.key">{{ item.name }}</el-radio>
            </el-radio-group>
          </div>
          <div class="SubmitSuggestFormInfo" v-if="notHandleTimeTypeName">
            <div class="SubmitSuggestFormInfoText">{{ notHandleTimeTypeName }}：</div>
            <el-radio-group v-model="form.notHandleTimeType">
              <el-radio v-for="item in notHandleTimeType" :key="item.key" :label="item.key">{{ item.name }}</el-radio>
            </el-radio-group>
          </div>
          <div class="SubmitSuggestFormInfo" v-if="isHopeEnhanceTalkName">
            <div class="SubmitSuggestFormInfoText">{{ isHopeEnhanceTalkName }}：</div>
            <el-radio-group v-model="form.isHopeEnhanceTalk">
              <el-radio v-for="item in isHopeEnhanceTalk" :key="item.key" :label="item.key">{{ item.name }}</el-radio>
            </el-radio-group>
          </div>
          <div class="SubmitSuggestFormInfo" v-if="isNeedPaperAnswerName">
            <div class="SubmitSuggestFormInfoText">{{ isNeedPaperAnswerName }}：</div>
            <el-radio-group v-model="form.isNeedPaperAnswer">
              <el-radio v-for="item in isNeedPaperAnswer" :key="item.key" :label="item.key">{{ item.name }}</el-radio>
            </el-radio-group>
          </div>
        </el-form-item>
        <el-form-item label="希望送交办单位" class="SubmitSuggestTitle">
          <suggest-simple-select-unit v-model="form.hopeHandleOfficeIds"></suggest-simple-select-unit>
        </el-form-item>
        <el-form-item class="SubmitSuggestContactPerson" label="提案联系人">
          <div class="SubmitSuggestContactPersonHead">
            <div class="SubmitSuggestContactPersonItem row2">提案联系人姓名</div>
            <div class="SubmitSuggestContactPersonItem row2">提案联系人电话</div>
            <div class="SubmitSuggestContactPersonItem row3">联系人通讯地址</div>
            <div class="SubmitSuggestContactPersonItem row1">操作</div>
          </div>
          <div class="SubmitSuggestContactPersonBody" v-for="item in contactPersonList" :key="item.id">
            <div class="SubmitSuggestContactPersonItem row2">
              <el-input placeholder="请输入联系人姓名" v-model="item.contactName" clearable></el-input>
            </div>
            <div class="SubmitSuggestContactPersonItem row2">
              <el-input placeholder="请输入联系人电话" v-model="item.contactPhone" clearable></el-input>
            </div>
            <div class="SubmitSuggestContactPersonItem row3">
              <el-input placeholder="请输入联系人通讯地址" v-model="item.contactAddress" clearable></el-input>
            </div>
            <div class="SubmitSuggestContactPersonItem row1">
              <el-link @click="newContactPerson" v-if="contactPersonList.length">
                <el-icon>
                  <CirclePlus />
                </el-icon>
              </el-link>
              <el-link v-if="contactPersonList.length > 1" @click="delContactPerson(item.id)">
                <el-icon>
                  <Remove />
                </el-icon>
              </el-link>
            </div>
          </div>
        </el-form-item>
        <div class="globalPaperFormButton" v-if="queryType !== 'review'">
          <el-button type="primary" @click="submitForm(formRef, 0)">提交提案</el-button>
          <el-button @click="submitForm(formRef, 1)"
            v-if="(!route.query.anewId && !route.query.id) || queryType === 'draft'">
            存为草稿
          </el-button>
          <el-button @click="resetForm" v-if="!route.query.id">重置</el-button>
          <el-button @click="resetForm" v-if="route.query.id">取消</el-button>
        </div>
      </el-form>
      <div v-if="queryType === 'review'" class="SuggestSegmentation"></div>
      <keep-alive>
        <SuggestReviewDetail :id="route.query.id" :name="route.query.reviewName" :content="form.content"
          :hopeHandleOfficeIds="form.hopeHandleOfficeIds" v-if="queryType === 'review' && reviewShow"
          @editCallback="editCallback" @callback="resetForm" :queryType="queryType" :signId="route.query.signId"
          :bigThemeId="form.bigThemeId" :smallThemeId="form.smallThemeId" :james="form.james" :jordan="form.jordan"
          :kobe="form.kobe" :duncan="form.duncan" :wade="form.wade">
        </SuggestReviewDetail>
      </keep-alive>
    </div>
    <xyl-popup-window v-model="show" name="相似度查询">
      <SimilarityQuery :type="isShow" :id="route.query.id" :content="form.content" @callback="handleSimilarityCallback">
      </SimilarityQuery>
    </xyl-popup-window>
  </el-scrollbar>
</template>
<script>
export default { name: 'SubmitSuggest' }
</script>
<script setup>
import api from '@/api'
import { reactive, ref, onActivated, onDeactivated, onBeforeUnmount, nextTick } from 'vue'
import { useRoute } from 'vue-router'
import { useStore } from 'vuex'
import { user, whetherUseIntelligentize } from 'common/js/system_var.js'
import { qiankunMicro } from 'common/config/MicroGlobal'
import { ElMessage } from 'element-plus'
import SimilarityQuery from '@/components/SimilarityQuery/SimilarityQuery.vue'
import SuggestRecommendUser from '@/components/SuggestRecommendUser/SuggestRecommendUser.vue'
import SuggestReviewDetail from '@/views/SuggestReview/component/SuggestReviewDetail.vue'

const route = useRoute()
const store = useStore()
const loading = ref(false)
const loadingText = ref('')
const formRef = ref()
const form = reactive({
  suggestSubmitWay: 'cppcc_member',
  title: '', // 提案标题
  SuggestBigType: '', // 提案大类
  SuggestSmallType: '', // 提案小类
  suggestUserId: '',
  cardNumber: '',
  sectorType: '',
  mobile: '',
  callAddress: '',
  delegationId: '',
  isJoinProposal: 0,
  joinUsers: [],
  content: '',
  suggestOpenType: 'open_all',
  suggestSurveyType: '3',
  isMakeMineJob: '1',
  notHandleTimeType: '1',
  isHopeEnhanceTalk: '1',
  isNeedPaperAnswer: '1',
  hopeHandleOfficeIds: [],
  bigThemeId: '',
  smallThemeId: '',
  james: '',
  jordan: '',
  kobe: '',
  duncan: '',
  wade: ''
})
const rules = reactive({
  suggestSubmitWay: [{ required: true, message: '请选择提案提交类型', trigger: ['blur', 'change'] }],
  title: [{ required: true, message: '请输入提案标题', trigger: ['blur', 'change'] }],
  content: [{ required: true, message: '请输入提案内容', trigger: ['blur', 'change'] }],
  suggestUserId: [{ required: true, message: '请选择提案者', trigger: ['blur', 'change'] }],
  delegationId: [{ required: false, message: '请选择集体提案单位', trigger: ['blur', 'change'] }],
  isJoinProposal: [{ required: true, message: '请选择是否联名提案', trigger: ['blur', 'change'] }],
  joinUsers: [{ type: 'array', required: false, message: '请选择提案联名人', trigger: ['blur', 'change'] }]
})

const guid = () => {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
    var r = (Math.random() * 16) | 0,
      v = c == 'x' ? r : (r & 0x3) | 0x8
    return v.toString(16)
  })
}
const tinyMceSetting = {
  tp_layout_options: {
    style: {
      'text-align': 'justify',
      'text-indent': '2em',
      'line-height': '20pt',
      'font-size': '14pt',
      'font-family': '仿宋_GB2312'
    },
    tagsStyle: {
      span: {
        'text-align': 'justify',
        'text-indent': '2em',
        'line-height': '20pt',
        'font-size': '14pt',
        'font-family': '仿宋_GB2312'
      }
    }
  },
  paste_postprocess: (plugin, args) => {
    nextTick(() => {
      args.target.execCommand('mceTpLayout')
      nextTick(() => {
        args.target.selection.collapse()
      })
    })
  },
  import_word_callback: (editor) => {
    nextTick(() => {
      editor.execCommand('mceTpLayout')
      nextTick(() => {
        editor.selection.collapse()
      })
    })
  },
  menubar: `edit insert format table importWord textRectify tpLayout`,
  menu: {
    importWord: { title: '导入Word', items: 'importWord' },
    textRectify: { title: '一键校正', items: 'textRectify' },
    tpLayout: { title: '一键排版', items: 'tpLayout' }
  },
}
const SuggestBigType = ref([])
const SuggestSmallType = ref([])
const suggestTitleNumber = ref(30)
const suggestContentNumber = ref(2000)
const suggestMinSimilar = ref(0)
const suggestContentMinNumber = ref(0)
const termYearId = ref('')
const contentCount = ref(0)
const fileData = ref([])
const delegationData = ref([])
const suggestOpenTypeName = ref('')
const suggestSurveyTypeName = ref('')
const notHandleTimeTypeName = ref('')
const isHopeEnhanceTalkName = ref('')
const isMakeMineJobName = ref('')
const isNeedPaperAnswerName = ref('')
const suggestOpenType = ref([])
const suggestSurveyType = ref([])
const notHandleTimeType = ref([])
const isHopeEnhanceTalk = ref([])
const isMakeMineJob = ref([])
const isNeedPaperAnswer = ref([])
const contactPersonList = ref([{ id: guid(), contactName: '', contactPhone: '', contactAddress: '' }])
const opinionsSuggestionsList = ref([{ id: guid(), suggestion: '', explanation: '' }, { id: guid(), suggestion: '', explanation: '' }, { id: guid(), suggestion: '', explanation: '' }])
const typeShow = ref(false)
const disabled = ref(false)
const isDisabled = ref(false)
const tabCode = ref(['cppccMember'])
const reviewShow = ref(false)
const queryType = ref('')

const show = ref(false)
const isShow = ref(false)
const elIsShow = ref(false)
const visibleIsShow = ref(false)
const userParams = ref({})
let timer = null
// const AiParams = ref({})
// const elAiChatClass = AiChatClass()
// const handleAiParams = (data) => {
//   if (data) {
//     elAiChatClass.AiChatConfig({
//       AiChatCode: 'ai-intelligent-write-chat',
//       AiChatWindow: true,
//       AiChatFile: data.fileData,
//       AiChatParams: { tool: data.toolId, param: { isPage: '1' } }
//     })
//     elAiChatClass.AiChatHistory()
//     elAiChatClass.AiChatSend(data.toolContent)
//   } else {
//     elAiChatClass.AiChatConfig({ AiChatCode: 'ai-intelligent-write-chat', AiChatWindow: true })
//   }
// }
// onActivated(() => {
//   if (route.query.type !== 'review') {
//     const openAiParams = JSON.parse(sessionStorage.getItem('openAiParams')) || ''
//     if (openAiParams) AiParams.value = openAiParams
//     if (JSON.stringify(AiParams.value) !== '{}') {
//       handleAiParams(openAiParams ? AiParams.value : '')
//       sessionStorage.setItem('openAiParams', JSON.stringify(''))
//       timer = setTimeout(() => {
//         handleAiParams('')
//       }, 2000)
//     }
//   }

onActivated(() => {
  qiankunMicro.setGlobalState({ AiChatCode: 'ai-intelligent-write-chat' })
  const openAiParams = JSON.parse(sessionStorage.getItem('openAiParams')) || ''
  if (openAiParams) {
    qiankunMicro.setGlobalState({
      AiChatConfig: {
        AiChatWindow: true,
        AiChatFile: openAiParams.fileData,
        AiChatParams: { tool: openAiParams.toolId, param: { isPage: '1' } },
        AiChatSendMessage: openAiParams.toolContent
      }
    })
    sessionStorage.setItem('openAiParams', JSON.stringify(''))
    timer = setTimeout(() => {
      qiankunMicro.setGlobalState({
        AiChatConfig: {
          AiChatWindow: true,
          AiChatFile: openAiParams.fileData,
          AiChatParams: {}
        }
      })
    }, 2000)
  }
  queryType.value = route.query.type
  if (route.query.clueListId) {
    proposalClueInfo()
  }
  globalReadConfig()
  termYearCurrent()
  dictionaryData()
  dictionaryNameData()
  suggestionThemeSelect()
  if (queryType.value === 'draft' || route.query.anewId) {
    typeShow.value = true
    disabled.value = true
  }
  if (route.query.id || route.query.anewId) {
    typeShow.value = true
    suggestionInfo()
  } else {
    tabCode.value = ['cppccMember']
    if (user.value.specialRoleKeys.includes('team_office_user')) {
      typeShow.value = true
    }
    if (user.value.specialRoleKeys.includes('cppcc_member')) {
      typeShow.value = true
      disabled.value = true
      form.suggestUserId = user.value.id
      cppccMemberInfo(user.value.id)
    } else {
      if (user.value.specialRoleKeys.includes('team_office_user')) {
        form.suggestSubmitWay = 'team'
      }
    }
    if (
      user.value.specialRoleKeys.includes('team_office_user') &&
      user.value.specialRoleKeys.includes('cppcc_member')
    ) {
      typeShow.value = false
    }
    if (user.value.specialRoleKeys.includes('admin')) {
      form.suggestSubmitWay = 'cppcc_member'
      typeShow.value = false
      disabled.value = false
      teamOfficeSelect({})
    } else {
      if (user.value.specialRoleKeys.includes('team_office_user')) {
        teamOfficeSelect({ isSelectMine: 1 })
      } else {
        teamOfficeSelect({})
      }
    }
    submitTypeChange()
  }
})
onDeactivated(() => {
  if (timer) {
    clearTimeout(timer)
    timer = null
  }
  qiankunMicro.setGlobalState({ AiChatCode: 'test_chat' })
  qiankunMicro.setGlobalState({
    AiChatConfig: {
      AiChatWindow: false,
      AiChatFile: [],
      AiChatParams: {}
    }
  })
  // elAiChatClass.AiChatConfig({ AiChatCode: 'test_chat', AiChatWindow: false })
  // elAiChatClass.AiChatHistory()
})
onBeforeUnmount(() => {
  if (timer) {
    clearTimeout(timer)
    timer = null
  }
  qiankunMicro.setGlobalState({ AiChatCode: 'test_chat' })
  qiankunMicro.setGlobalState({
    AiChatConfig: {
      AiChatWindow: false,
      AiChatFile: [],
      AiChatParams: {}
    }
  })
  // elAiChatClass.AiChatConfig({ AiChatCode: 'test_chat', AiChatWindow: false })
  // elAiChatClass.AiChatHistory()
})

const suggestionThemeSelect = async () => {
  const res = await api.suggestionThemeSelect({ query: { isUsing: 1 } })
  var { data } = res
  SuggestBigType.value = data
  SuggestBigTypeChange()
}

const SuggestBigTypeChange = () => {
  if (form.SuggestBigType) {
    for (let index = 0; index < SuggestBigType.value.length; index++) {
      const item = SuggestBigType.value[index]
      if (item.id === form.SuggestBigType) {
        if (!item.children.map((v) => v.id).includes(form.SuggestSmallType)) {
          form.SuggestSmallType = ''
        }
        SuggestSmallType.value = item.children
      }
    }
  } else {
    form.SuggestSmallType = ''
    SuggestSmallType.value = []
  }
}

const handleSimilarity = (isType) => {
  if (!form.content) return ElMessage({ type: 'warning', message: '请输入提案内容进行相似度查询！' })
  sessionStorage.setItem('TextQueryToolTitle', form.title)
  sessionStorage.setItem('TextQueryToolContent', form.content)
  isShow.value = isType
  show.value = true
}
const handleSimilarityCallback = (type, uflag) => {
  if (uflag == 1) {
    if (type) {
      globalJson(0)
    } else {
      ElMessage({ type: 'warning', message: '您的相似度高于55%，请调整后在提交。' })
    }
  }
  show.value = false
}
const handleContentBlur = () => {
  userParams.value = { authorId: form.suggestUserId, content: form.content }
}
const userInitCallback = (isElIsShow, isVisibleIsShow) => {
  elIsShow.value = isElIsShow
  visibleIsShow.value = isVisibleIsShow
}
const userSelect = (item) => {
  if (!form.joinUsers.includes(item.id)) {
    form.joinUsers = [...form.joinUsers, item.id]
  }
}
const globalReadConfig = async () => {
  const { data } = await api.globalReadConfig({ codes: ['suggestTitleNumber', 'suggestContentNumber', 'suggestMinSimilar', 'suggestContentMinNumber'] })
  if (data.suggestTitleNumber) {
    suggestTitleNumber.value = Number(data.suggestTitleNumber)
  }
  if (data.suggestContentNumber) {
    suggestContentNumber.value = Number(data.suggestContentNumber)
  }
  if (data.suggestContentMinNumber) {
    suggestContentMinNumber.value = Number(data.suggestContentMinNumber)
  }
  if (data.suggestMinSimilar) {
    suggestMinSimilar.value = Number(data.suggestMinSimilar)
  }
}
// 获取当前届次
const termYearCurrent = async () => {
  const { data } = await api.termYearCurrent({ termYearType: 'cppcc_member' })
  termYearId.value = data.id
}
const dictionaryData = async () => {
  const { data } = await api.dictionaryData({
    dictCodes: [
      'suggest_open_type',
      'suggest_survey_type',
      'not_handle_time_type',
      'is_hope_enhance_talk',
      'is_make_mine_job',
      'is_need_paper_answer'
    ]
  })
  suggestOpenType.value = data.suggest_open_type
  suggestSurveyType.value = data.suggest_survey_type
  notHandleTimeType.value = data.not_handle_time_type
  isHopeEnhanceTalk.value = data.is_hope_enhance_talk
  isMakeMineJob.value = data.is_make_mine_job
  isNeedPaperAnswer.value = data.is_need_paper_answer
}
const dictionaryNameData = async () => {
  const { data } = await api.dictionaryNameData({
    dictCodes: [
      'suggest_open_type',
      'suggest_survey_type',
      'not_handle_time_type',
      'is_hope_enhance_talk',
      'is_make_mine_job',
      'is_need_paper_answer'
    ]
  })
  suggestOpenTypeName.value = data.suggest_open_type
  suggestSurveyTypeName.value = data.suggest_survey_type
  notHandleTimeTypeName.value = data.not_handle_time_type
  isHopeEnhanceTalkName.value = data.is_hope_enhance_talk
  isMakeMineJobName.value = data.is_make_mine_job
  isNeedPaperAnswerName.value = data.is_need_paper_answer
}
const proposalClueInfo = async () => {
  const { data } = await api.proposalClueInfo({ detailId: route.query.clueListId })
  form.title = data.title
  form.content = data.content
}
const isLock = ref(false)
const lockVo = ref({})
const suggestionInfo = async () => {
  try {
    const res = await api.suggestionInfo({
      detailId: route.query.id || route.query.anewId,
      isOpenWithLock: queryType.value === 'review' ? 1 : null
    })
    var { data } = res
    reviewShow.value = true
    lockVo.value = data.lockVo
    isLock.value = route.query.type == 'review' && data.lockVo.isLock == 1 && data.lockVo.lockUserId != user.value.id
    form.suggestSubmitWay = data.suggestSubmitWay
    if (form.suggestSubmitWay === 'cppcc_member') {
      tabCode.value = ['cppccMember']
      form.suggestUserId = data.suggestUserId
      if (data.suggestUserId) {
        cppccMemberInfo(data.suggestUserId)
      }
    }
    if (form.suggestSubmitWay === 'team') {
      isDisabled.value = true
      form.delegationId = data.delegationId
      delegationData.value = data.delegationId ? [{ id: data.delegationId, name: data.delegationName }] : []
    }
    submitTypeChange()
    form.title = data.title
    form.SuggestBigType = data.bigThemeId
    form.SuggestSmallType = data.smallThemeId
    SuggestBigTypeChange()
    form.content = data.content
    if (data.proposalInventoryList?.length) {
      opinionsSuggestionsList.value = data.proposalInventoryList.map(v => ({ id: v.id, suggestion: v.content, explanation: v.replenish }))
    }
    form.bigThemeId = data.bigThemeId
    form.smallThemeId = data.smallThemeId
    form.james = data.james
    form.jordan = data.jordan
    form.kobe = data.kobe
    form.duncan = data.duncan
    form.wade = data.wade
    handleContentBlur()
    form.isJoinProposal = data.isJoinProposal
    JoinChange()
    form.suggestOpenType = data.suggestOpenType?.value
    form.suggestSurveyType = data.suggestSurveyType?.value
    form.isMakeMineJob = data.isMakeMineJob?.value
    form.notHandleTimeType = data.notHandleTimeType?.value
    form.isHopeEnhanceTalk = data.isHopeEnhanceTalk?.value
    form.isNeedPaperAnswer = data.isNeedPaperAnswer?.value
    fileData.value = data.attachments || []
    form.hopeHandleOfficeIds = data.hopeHandleOfficeIds?.map((v) => v.officeId) || []
    form.joinUsers = data.joinUsers?.map((v) => v.userId) || []
    if (data.contacters?.length) {
      contactPersonList.value = data.contacters.map((v) => ({
        id: v.id,
        contactName: v.contacterName,
        contactPhone: v.contacterMobile,
        contactAddress: v.contacterAddress
      }))
    }
    userParams.value = { authorId: form.suggestUserId, content: form.content }
  } catch (err) {
    if (err.code === 500) {
      if (route.query.id && queryType.value === 'review') {
        reviewShow.value = false
        qiankunMicro.setGlobalState({
          closeOpenRoute: { openId: route.query.oldRouteId, closeId: route.query.routeId }
        })
      }
    }
  }
}

const submitTypeChange = () => {
  if (form.suggestSubmitWay === 'cppcc_member') {
    rules.suggestUserId = [{ required: true, message: '请选择提案者', trigger: ['blur', 'change'] }]
    rules.delegationId = [{ required: false, message: '请选择集体提案单位', trigger: ['blur', 'change'] }]
  } else if (form.suggestSubmitWay === 'team') {
    rules.suggestUserId = [{ required: false, message: '请选择提案者', trigger: ['blur', 'change'] }]
    rules.delegationId = [{ required: true, message: '请选择集体提案单位', trigger: ['blur', 'change'] }]
  }
}
const JoinChange = () => {
  if (form.isJoinProposal) {
    rules.joinUsers = [{ type: 'array', required: true, message: '请选择提案联名人', trigger: ['blur', 'change'] }]
  } else {
    rules.joinUsers = [{ type: 'array', required: false, message: '请选择提案联名人', trigger: ['blur', 'change'] }]
  }
}
const handleContentCount = (count) => {
  contentCount.value = count
}

const userCallback = (data) => {
  if (data) {
    cppccMemberInfo(data.id)
    form.joinUsers = form.joinUsers.filter((v) => v !== data.id)
  } else {
    form.cardNumber = ''
    form.sectorType = ''
    form.mobile = ''
    form.callAddress = ''
  }
  userParams.value = { authorId: form.suggestUserId, content: form.content }
}
const cppccMemberInfo = async (userId) => {
  const { data } = await api.cppccMemberInfo({ detailId: userId })
  form.cardNumber = data.cardNumberCppcc
  form.sectorType = data.sectorType?.label
  form.mobile = data.mobile
  form.callAddress = data.callAddress
}
const teamOfficeSelect = async (params) => {
  const { data } = await api.teamOfficeSelect(params)
  if (data.length) {
    if (user.value.specialRoleKeys.includes('team_office_user')) {
      isDisabled.value = true
      form.delegationId = data[0].id
    }
  }
  delegationData.value = data
}
const fileUpload = (file) => {
  fileData.value = file
}
const newOpinionsSuggestions = () => {
  opinionsSuggestionsList.value.push({ id: guid(), suggestion: '', explanation: '' })
}
const delOpinionsSuggestions = (id) => {
  opinionsSuggestionsList.value = opinionsSuggestionsList.value.filter(v => v.id !== id)
}
const newContactPerson = () => {
  contactPersonList.value.push({ id: guid(), contactName: '', contactPhone: '', contactAddress: '' })
}
const delContactPerson = (id) => {
  contactPersonList.value = contactPersonList.value.filter((v) => v.id !== id)
}
const submitForm = async (formEl, type, cb, _form) => {
  if (!formEl) return
  if (contentCount.value > suggestContentNumber.value && type == '0') {
    ElMessage({ type: 'warning', message: `当前输入的提案内容超过了${suggestContentNumber.value}字，不允许提交！` })
    return
  }
  if (contentCount.value < suggestContentMinNumber.value && route.query.utype == '1' && type == '0') {
    ElMessage({ type: 'warning', message: `当前输入的提案内容少于${suggestContentMinNumber.value}字，不允许提交！` })
    return
  }
  const hasValidSuggestion = opinionsSuggestionsList.value.some(
    (v) => v.suggestion !== null && v.suggestion !== undefined && v.suggestion.trim() !== ""
  );
  if (!hasValidSuggestion) {
    ElMessage({ type: 'warning', message: `意见建议清单中至少需要填写一项建议！` });
    return;
  }

  await formEl.validate((valid, fields) => {
    if (valid) {
      // if (whetherUseIntelligentize.value && !cb) {
      //   if (type) { globalJson(type, cb, _form) } else {
      //     ElMessageBox.confirm('系统将为您进行相似度查询，是否同意执行操作？', '提示', {
      //       closeOnClickModal: false,
      //       confirmButtonText: '同意',
      //       cancelButtonText: '跳过'
      //     }).then(() => { handleSimilarity(true) }).catch(() => { globalJson(type, cb, _form) })
      //   }
      // } else { globalJson(type, cb, _form) }
      globalJson(type, cb, _form)
    } else { ElMessage({ type: 'warning', message: '请根据提示信息完善字段内容！' }) }
  })
}
const editCallback = (cb, _form) => {
  submitForm(formRef.value, 0, cb, _form)
}
const globalJson = async (type, cb, _item) => {
  var formObj = {
    id: route.query.id,
    bigThemeId: _item ? _item.SuggestBigType : '', // 提案大类
    smallThemeId: _item ? _item.SuggestSmallType : '', // 提案小类
    suggestSubmitWay: form.suggestSubmitWay,
    title: form.title, // 提案标题
    suggestUserId: form.suggestSubmitWay === 'cppcc_member' ? form.suggestUserId : null,
    delegationId: form.suggestSubmitWay === 'team' ? form.delegationId : null,
    content: form.content,
    isJoinProposal: form.isJoinProposal,
    suggestOpenType: form.suggestOpenType,
    suggestSurveyType: form.suggestSurveyType,
    isMakeMineJob: form.isMakeMineJob,
    notHandleTimeType: form.notHandleTimeType,
    isHopeEnhanceTalk: form.isHopeEnhanceTalk,
    isNeedPaperAnswer: form.isNeedPaperAnswer,
    attachmentIds: fileData.value.map(v => v.id),
  }
  if (route.query.signId == '2') {
    formObj.james = _item.reviewOpinion || ''
    formObj.jordan = _item.reviewResult || ''
    formObj.kobe = _item.transactType || '' // 办理方式
    formObj.duncan = _item.mainHandleOfficeId.map(v => v).join(',') || '' // 主办单位
    formObj.wade = _item.handleOfficeIds.map(v => v).join(',') || '' // 协办单位、分办
  }
  try {
    const { code } = await api.globalJson(route.query.id ? '/proposal/edit' : '/proposal/add', {
      form: formObj,
      isSaveDraft: type,
      joinUsers: form.isJoinProposal ? form.joinUsers : [],
      hopeHandleOfficeIds: form.hopeHandleOfficeIds,
      contacters: contactPersonList.value.filter(v => v.contactName.replace(/(^\s*)|(\s*$)/g, '') || v.contactPhone.replace(/(^\s*)|(\s*$)/g, '') || v.contactAddress.replace(/(^\s*)|(\s*$)/g, '')).map(v => ({ contacterName: v.contactName, contacterMobile: v.contactPhone, contacterAddress: v.contactAddress })),
      proposalInventories: opinionsSuggestionsList.value.map(v => ({ content: v.suggestion, replenish: v.explanation }))
    })
    if (code === 200) {
      if (route.query.clueListId) {
        proposalClueUse()
      } else {
        if (cb) {
          return cb()
        }
        ElMessage({ type: 'success', message: route.query.id ? '编辑成功' : '提交成功' })
        if (route.query.id || route.query.anewId || route.query.clueListId) {
          qiankunMicro.setGlobalState({
            closeOpenRoute: { openId: route.query.oldRouteId, closeId: route.query.routeId }
          })
        } else {
          qiankunMicro.setGlobalState({
            openRoute: { name: '我的提案', path: '/proposal/MyLedSuggest' }
          })
          store.commit('setRefreshRoute', 'SubmitSuggest')
          setTimeout(() => {
            store.commit('setRefreshRoute', '')
          }, 222)
        }
      }
    }
  } catch (err) {
    loading.value = false
  }
}
const proposalClueUse = async () => {
  const { code } = await api.proposalClueUse({ detailId: route.query.clueListId })
  if (code === 200) {
    ElMessage({ type: 'success', message: '提交成功' })
    qiankunMicro.setGlobalState({ closeOpenRoute: { openId: route.query.oldRouteId, closeId: route.query.routeId } })
  }
}
const resetForm = () => {
  if (route.query.id || route.query.anewId || route.query.clueListId) {
    qiankunMicro.setGlobalState({ closeOpenRoute: { openId: route.query.oldRouteId, closeId: route.query.routeId } })
  } else {
    store.commit('setRefreshRoute', 'SubmitSuggest')
    setTimeout(() => {
      store.commit('setRefreshRoute', '')
    }, 222)
  }
}
</script>
<style lang="scss">
.SubmitSuggest {
  width: 100%;
  height: 100%;

  .SubmitSuggestBody {
    width: 990px;
    margin: 20px auto;
    background-color: #fff;
    box-shadow: 0px 3px 15px 1px rgba(0, 0, 0, 0.16);

    .SubmitSuggestNameBody {
      padding: var(--zy-distance-one);
      padding-bottom: 0;

      .global-dynamic-title {
        border-bottom: 3px solid var(--zy-el-color-primary);
      }
    }

    .globalPaperForm {
      width: 100%;
      padding: var(--zy-distance-one);
      padding-top: 0;

      .zy-el-form-item {
        width: 50%;
        margin: 0;
        border-bottom: 1px solid var(--zy-el-color-primary);

        .zy-el-form-item__label {
          width: 138px;
          justify-content: center;
        }

        .zy-el-form-item__content {
          border-left: 1px solid var(--zy-el-color-primary);
          border-right: 1px solid transparent;

          &>.simple-select-person {
            box-shadow: 0 0 0 0 !important;
          }

          &>.zy-el-input,
          .zy-el-input-number {
            width: 100%;

            .zy-el-input__wrapper {
              box-shadow: 0 0 0 0 !important;
            }
          }

          &>.zy-el-select,
          .zy-el-select-v2 {
            .zy-el-select__wrapper {
              box-shadow: 0 0 0 0 !important;
            }
          }

          &>.zy-el-radio-group {
            padding-left: 15px;
          }

          &>.zy-el-date-editor {
            width: 100%;

            &>.zy-el-input__wrapper {
              width: 100%;
              box-shadow: 0 0 0 0 !important;
            }
          }
        }
      }

      .SubmitSuggestLeft {
        .zy-el-form-item__content {
          border-right-color: var(--zy-el-color-primary);
        }
      }

      .SubmitSuggestTitle {
        width: 100%;

        .zy-el-form-item__content {
          border-right-color: transparent;
        }
      }

      .SubmitSuggestButton {
        .zy-el-form-item__content {
          flex-wrap: nowrap;
          justify-content: space-between;

          .SubmitSuggestContentNumber {
            padding: 0 10px;
            color: var(--zy-el-color-error);
            font-size: var(--zy-text-font-size);
            line-height: var(--zy-line-height);
          }

          .SubmitSuggestUpload {
            margin-left: 12px;
            margin-right: 12px;
          }

          .zy-el-button {
            --zy-el-button-size: var(--zy-height-routine);
          }
        }
      }

      .TinyMceEditor {
        border-bottom: 1px solid var(--zy-el-color-primary);
      }

      .opinionsSuggestionsList {
        width: 100%;

        .opinionsSuggestionsListHead,
        .opinionsSuggestionsListBody {
          width: 100%;
          display: flex;
        }

        .opinionsSuggestionsListBody {
          border-top: 1px solid var(--zy-el-color-primary);
        }

        .row1 {
          flex: 1;
        }

        .row2 {
          flex: 2;
        }

        .row3 {
          flex: 3;
        }

        .opinionsSuggestionsListItem {
          height: 40px;
          line-height: 40px;
          display: flex;
          align-items: center;
          justify-content: center;

          &>.zy-el-input {
            width: 100%;

            .zy-el-input__wrapper {
              box-shadow: 0 0 0 0 !important;
            }
          }

          .zy-el-link {
            font-size: 18px;
            line-height: 24px;
          }

          .zy-el-link+.zy-el-link {
            margin-left: 12px;
          }
        }

        .opinionsSuggestionsListItem+.opinionsSuggestionsListItem {
          border-left: 1px solid var(--zy-el-color-primary);
        }
      }

      .SubmitSuggestFormUpload {
        width: 100%;

        .zy-el-form-item__content {
          padding: 15px;
          border-right-color: transparent;

          .SubmitSuggestFormInfo {
            width: 100%;
            display: flex;
          }
        }
      }

      .SubmitSuggestFormItem {
        width: 100%;

        .zy-el-form-item__content {
          padding: 0 15px;
          border-right-color: transparent;

          .SubmitSuggestFormInfo {
            width: 100%;
            display: flex;
          }
        }
      }

      .SubmitSuggestContactPerson {
        width: 100%;

        .SubmitSuggestContactPersonHead,
        .SubmitSuggestContactPersonBody {
          width: 100%;
          display: flex;
        }

        .SubmitSuggestContactPersonBody {
          border-top: 1px solid var(--zy-el-color-primary);
        }

        .row1 {
          flex: 1;
        }

        .row2 {
          flex: 2;
        }

        .row3 {
          flex: 3;
        }

        .SubmitSuggestContactPersonItem {
          height: 40px;
          line-height: 40px;
          display: flex;
          align-items: center;
          justify-content: center;

          &>.zy-el-input {
            width: 100%;

            .zy-el-input__wrapper {
              box-shadow: 0 0 0 0 !important;
            }
          }

          .zy-el-link {
            font-size: 18px;
            line-height: 24px;
          }

          .zy-el-link+.zy-el-link {
            margin-left: 12px;
          }
        }

        .SubmitSuggestContactPersonItem+.SubmitSuggestContactPersonItem {
          border-left: 1px solid var(--zy-el-color-primary);
        }
      }

      .globalPaperFormButton {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        padding-top: 22px;

        .zy-el-button+.zy-el-button {
          margin-left: var(--zy-distance-two);
        }
      }
    }

    .SuggestSegmentation {
      width: 100%;
      height: 10px;
      background-color: var(--zy-el-color-info-light-9);
    }
  }
}
</style>
