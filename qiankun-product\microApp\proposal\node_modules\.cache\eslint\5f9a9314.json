[{"D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\components\\index.js": "1", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\router\\index.js": "2", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuperEdit\\SuperEdit.vue": "3", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\components\\suggest-simple-select-unit\\suggest-simple-select-unit.vue": "4", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\api\\index.js": "5", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\ProposalWorkbench\\OrganizerWorkBench.vue": "6", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestClue\\SuggestClueMine\\SuggestClueMine.vue": "7", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestClue\\SuggestClueAdd\\SuggestClueAdd.vue": "8", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestClue\\SuggestClueControl\\SuggestClueControl.vue": "9", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\UnitSuggestReply\\UnitSuggestReply.vue": "10", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\UnitSuggestConclude\\UnitSuggestConclude.vue": "11", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestClue\\SuggestClueRegister\\SuggestClueRegister.vue": "12", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\ProposalWorkbench\\ProposalCommitteeWorkBench.vue": "13", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\ProposalWorkbench\\TwoDepartmentsWorkBench.vue": "14", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\ProposalWorkbench\\ProposerWorkBench.vue": "15", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\UnitSuggestTransact\\UnitSuggestTransact.vue": "16", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\UnitAllSuggest\\UnitAllSuggest.vue": "17", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestConclude\\SuggestConclude.vue": "18", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestTrackTransact\\SuggestTrackTransact.vue": "19", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestPendingApproval\\SuggestPendingApproval.vue": "20", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestPendingFinalReview\\SuggestPendingFinalReview.vue": "21", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\UnitSuggestTrackTransact\\UnitSuggestTrackTransact.vue": "22", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestApplyForAdjust\\SuggestApplyForAdjust.vue": "23", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestReply\\SuggestReply.vue": "24", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestTransact\\SuggestTransact.vue": "25", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestAssign\\SuggestAssign.vue": "26", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestApplyForPostpone\\SuggestApplyForPostpone.vue": "27", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestAdvanceAssign\\SuggestAdvanceAssign.vue": "28", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestPreApplyForAdjust\\SuggestPreApplyForAdjust.vue": "29", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\UnitSuggestAdvanceAssign\\UnitSuggestAdvanceAssign.vue": "30", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestReview\\SuggestReview.vue": "31", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\AllSuggest\\AllSuggest.vue": "32", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestStatistics\\SuggestStatistics.vue": "33", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestDetail\\SuggestDetail.vue": "34", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\ProposalClue\\ProposalClueDetail.vue": "35", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestControls\\SuggestControls.vue": "36", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\BehalfSuggest\\SuggestDraftBox\\SuggestDraftBox.vue": "37", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\BehalfSuggest\\MyJointSuggest\\MyJointSuggest.vue": "38", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\BehalfSuggest\\ProposalManagementTool\\TextQueryTool.vue": "39", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\BehalfSuggest\\SubmitSuggest\\SubmitSuggest.vue": "40", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\BehalfSuggest\\MyLedSuggest\\MyLedSuggest.vue": "41", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestConfig\\SuggestNumbering\\SuggestNumbering.vue": "42", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestConfig\\SuggestUnit\\SuggestUnit.vue": "43", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestConfig\\CollectiveProposalUnit\\CollectiveProposalUnit.vue": "44", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\App.vue": "45", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\assets\\js\\suggestExportWord.js": "46", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\UnitSuggestReply\\organizingUnitEvaluationNew.vue": "47", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\MergerProposal\\component\\ManualMergeProposal.vue": "48", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestClue\\SuggestClueMine\\components\\SuggestClueDetails.vue": "49", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestClue\\SuggestClueMine\\components\\UseTimesDetails.vue": "50", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestClue\\SuggestClueAdd\\components\\SuggestClueDetails.vue": "51", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestClue\\SuggestClueControl\\components\\SuggestClueSubmit.vue": "52", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestClue\\SuggestClueControl\\components\\SuggestClueDetails.vue": "53", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestTransact\\component\\SuggestAnswerTime.vue": "54", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestClue\\SuggestClueRegister\\components\\SuggestClueDetails.vue": "55", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestAssign\\component\\SuggestBatchAssignUnit.vue": "56", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuperEdit\\HandUnitSuperList.vue": "57", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestAssign\\component\\SuggestBatchAssign.vue": "58", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestAssign\\component\\SuggestAssignDetail.vue": "59", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestDetail\\component\\SuggestBasicInfo.vue": "60", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\BehalfSuggest\\MyLedSuggest\\component\\QualityAssessmentList.vue": "61", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestDetail\\SuggestReplyDetail\\SuggestReplyDetail.vue": "62", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestAdvanceAssign\\component\\SuggestBatchAdvanceAssignUnit.vue": "63", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestDetail\\ApplyForAdjustRecords\\ApplyForAdjustRecords.vue": "64", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestControls\\ProposalGroup.vue": "65", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestApplyForAdjust\\component\\SuggestAdjustReview.vue": "66", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestControls\\SupervisionProcess.vue": "67", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestTrackTransact\\component\\SuggestTrackTransactDetail.vue": "68", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\UnitSuggestDetail\\UnitSuggestDetail.vue": "69", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\AllSuggest\\component\\SuggestSerialNumber.vue": "70", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\components\\suggestPrint\\suggestPrint.vue": "71", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\ProposalWorkbench\\NewsDetails.vue": "72", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestDetail\\SegreeSatisfactionDetail\\SegreeSatisfactionDetail.vue": "73", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestConfig\\SuggestNumbering\\component\\SuggestNumberingSubmit.vue": "74", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestReview\\component\\SuggestReviewDetail.vue": "75", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\components\\SimilarityQuery\\SimilarityQuery.vue": "76", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestConfig\\CollectiveProposalUnit\\component\\SubmitCollectiveProposalUnit.vue": "77", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\components\\suggest-select-unit\\suggest-select-unit.vue": "78", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\DoNotReceiveSuggest\\DoNotReceiveSuggest.vue": "79", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestedClassification\\SuggestedClassification.vue": "80", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\MergerProposal\\MergerProposalContainer.vue": "81", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\UnitSummaryReport\\UnitSummaryReport.vue": "82", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestStatistics\\SuggestStatisticsList.vue": "83", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\MergerProposal\\HaveMergerProposal.vue": "84", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\ProposalClue\\SubmitProposalClue.vue": "85", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestedClassification\\SuggestedSubdivide.vue": "86", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\ProposalClue\\ProposalClueList.vue": "87", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestClue\\SuggestDocument\\SuggestDocument.vue": "88", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\BehalfSuggest\\MyLedSuggest\\component\\SubmitSegreeSatisfaction.vue": "89", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuperEdit\\HandUnitSuperNew.vue": "90", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\UnitSuggestDetail\\component\\SubmitSuggestReply.vue": "91", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\UnitSuggestReply\\organizingUnitEvaluationInfo.vue": "92", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestControls\\components\\ActivityMaterialDetails.vue": "93", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestControls\\components\\SubmitActivityMaterial.vue": "94", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\components\\SimilarityQuery\\SimilarityDetails.vue": "95", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\components\\SimilarityQuery\\SuggestDetail.vue": "96", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestConfig\\CollectiveProposalUnitUser\\CollectiveProposalUnitUser.vue": "97", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestConfig\\CollectiveProposalUnitUser\\SubmitCollectiveProposalUnitUser.vue": "98", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestConfig\\SuggestType\\SuggestType.vue": "99", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestConfig\\SuggestUnitUser\\SuggestUnitUser.vue": "100", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestConfig\\SuggestUnitUser\\SuggestUnitUserSubmit.vue": "101", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuperEdit\\SegreeSatisfactionList.vue": "102", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestStatistics\\ColumnChart.vue": "103", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuperEdit\\HandWaySuperEdit.vue": "104", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestedClassification\\SelectHandlingUnit.vue": "105", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestStatistics\\PieChart.vue": "106", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\UnitSummaryReport\\component\\UnitSummaryReportDetails.vue": "107", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestDetail\\ApplyForAdjustResult\\ApplyForAdjustResult.vue": "108", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\MergerProposal\\component\\SmartMergeProposal.vue": "109", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestAssign\\component\\SuggestBatchSendBack.vue": "110", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestDetail\\TrackTransactApplyForRecords\\TrackTransactApplyForRecords.vue": "111", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\UnitSummaryReport\\component\\SubmitUnitSummaryReport.vue": "112", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestDetail\\CommunicationSituation\\CommunicationSituation.vue": "113", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\AllSuggest\\component\\JoinUserManage.vue": "114", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\AllSuggest\\component\\SuggestExchange.vue": "115", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestApplyForPostpone\\component\\SuggestPostponeReview.vue": "116", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestClue\\SuggestClueMine\\components\\SuggestClueSubmit.vue": "117", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestClue\\SuggestDocument\\components\\SuggestDocumentSubmit.vue": "118", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\components\\SuggestRecommendUser\\SuggestRecommendUser.vue": "119", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestConfig\\SuggestType\\component\\SuggestBigType.vue": "120", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestConfig\\SuggestType\\component\\SuggestSmallType.vue": "121", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\components\\SuggestRecommendUnit\\SuggestRecommendUnit.vue": "122", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestConfig\\SuggestUnit\\component\\SuggestUnitSubmit.vue": "123", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\MergerProposal\\component\\SubmitMergerProposal.vue": "124", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestDetail\\CommunicationSituation\\CommunicationSituationSubmit.vue": "125", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\UnitSuggestDetail\\component\\ApplyForTrackTransact.vue": "126", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\UnitSuggestDetail\\component\\SubmitSuggestReplyManage.vue": "127", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestDetail\\CommunicationSituation\\CommunicationSituationDetail.vue": "128", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\UnitSuggestDetail\\component\\ApplyForAnswer.vue": "129", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestTrackTransact\\component\\SuggestTrackTransactReview.vue": "130", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\UnitSuggestDetail\\component\\UnitApplyForAdjustRecords.vue": "131", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\UnitSuggestDetail\\component\\ApplyForAdjust.vue": "132", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\UnitSuggestDetail\\component\\UnitApplyForAnswerRecords.vue": "133", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\UnitSuggestDetail\\component\\SubmitSuggestTrackTransactReply.vue": "134", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\components\\SuggestRecommendType\\SuggestRecommendType.vue": "135", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestReview\\component\\ReviewSimilarityQuery.vue": "136"}, {"size": 525, "mtime": 1754876068880, "results": "137", "hashOfConfig": "138"}, {"size": 14730, "mtime": 1754876068889, "results": "139", "hashOfConfig": "138"}, {"size": 27702, "mtime": 1754876069103, "results": "140", "hashOfConfig": "138"}, {"size": 3343, "mtime": 1754876068883, "results": "141", "hashOfConfig": "138"}, {"size": 15643, "mtime": 1754876068830, "results": "142", "hashOfConfig": "138"}, {"size": 13865, "mtime": 1754876068935, "results": "143", "hashOfConfig": "138"}, {"size": 5427, "mtime": 1754876069000, "results": "144", "hashOfConfig": "138"}, {"size": 11172, "mtime": 1754876068979, "results": "145", "hashOfConfig": "138"}, {"size": 8960, "mtime": 1754876068989, "results": "146", "hashOfConfig": "138"}, {"size": 5366, "mtime": 1754876069123, "results": "147", "hashOfConfig": "138"}, {"size": 4307, "mtime": 1754876069111, "results": "148", "hashOfConfig": "138"}, {"size": 3574, "mtime": 1754876069009, "results": "149", "hashOfConfig": "138"}, {"size": 12305, "mtime": 1754876068938, "results": "150", "hashOfConfig": "138"}, {"size": 9932, "mtime": 1754876068945, "results": "151", "hashOfConfig": "138"}, {"size": 15419, "mtime": 1754876068942, "results": "152", "hashOfConfig": "138"}, {"size": 10092, "mtime": 1754876069132, "results": "153", "hashOfConfig": "138"}, {"size": 4277, "mtime": 1754876069106, "results": "154", "hashOfConfig": "138"}, {"size": 4275, "mtime": 1754876069019, "results": "155", "hashOfConfig": "138"}, {"size": 4798, "mtime": 1754876069087, "results": "156", "hashOfConfig": "138"}, {"size": 7531, "mtime": 1754876069066, "results": "157", "hashOfConfig": "138"}, {"size": 7520, "mtime": 1754876069069, "results": "158", "hashOfConfig": "138"}, {"size": 4150, "mtime": 1754876069129, "results": "159", "hashOfConfig": "138"}, {"size": 4105, "mtime": 1754876068960, "results": "160", "hashOfConfig": "138"}, {"size": 7769, "mtime": 1754884931364, "results": "161", "hashOfConfig": "138"}, {"size": 12131, "mtime": 1754876069093, "results": "162", "hashOfConfig": "138"}, {"size": 6192, "mtime": 1754876068968, "results": "163", "hashOfConfig": "138"}, {"size": 4115, "mtime": 1754876068965, "results": "164", "hashOfConfig": "138"}, {"size": 11530, "mtime": 1754876068953, "results": "165", "hashOfConfig": "138"}, {"size": 4362, "mtime": 1754876069072, "results": "166", "hashOfConfig": "138"}, {"size": 10818, "mtime": 1754876069108, "results": "167", "hashOfConfig": "138"}, {"size": 7486, "mtime": 1754876069079, "results": "168", "hashOfConfig": "138"}, {"size": 24670, "mtime": 1754876068892, "results": "169", "hashOfConfig": "138"}, {"size": 16432, "mtime": 1754888606820, "results": "170", "hashOfConfig": "138"}, {"size": 59027, "mtime": 1754883783832, "results": "171", "hashOfConfig": "138"}, {"size": 3038, "mtime": 1754876068926, "results": "172", "hashOfConfig": "138"}, {"size": 8614, "mtime": 1754876069040, "results": "173", "hashOfConfig": "138"}, {"size": 5792, "mtime": 1754876068915, "results": "174", "hashOfConfig": "138"}, {"size": 6107, "mtime": 1754876068897, "results": "175", "hashOfConfig": "138"}, {"size": 2826, "mtime": 1754876068907, "results": "176", "hashOfConfig": "138"}, {"size": 42185, "mtime": 1754901962712, "results": "177", "hashOfConfig": "138"}, {"size": 7457, "mtime": 1754876068900, "results": "178", "hashOfConfig": "138"}, {"size": 4360, "mtime": 1754876069027, "results": "179", "hashOfConfig": "138"}, {"size": 9540, "mtime": 1754876069035, "results": "180", "hashOfConfig": "138"}, {"size": 4565, "mtime": 1754876069022, "results": "181", "hashOfConfig": "138"}, {"size": 949, "mtime": 1752112197348, "results": "182", "hashOfConfig": "138"}, {"size": 7478, "mtime": 1754876068869, "results": "183", "hashOfConfig": "138"}, {"size": 8184, "mtime": 1754876069127, "results": "184", "hashOfConfig": "138"}, {"size": 4119, "mtime": 1754876068921, "results": "185", "hashOfConfig": "138"}, {"size": 3231, "mtime": 1754876069003, "results": "186", "hashOfConfig": "138"}, {"size": 1141, "mtime": 1754876069006, "results": "187", "hashOfConfig": "138"}, {"size": 3776, "mtime": 1754876068986, "results": "188", "hashOfConfig": "138"}, {"size": 4625, "mtime": 1754876068996, "results": "189", "hashOfConfig": "138"}, {"size": 3349, "mtime": 1754876068993, "results": "190", "hashOfConfig": "138"}, {"size": 2085, "mtime": 1754876069096, "results": "191", "hashOfConfig": "138"}, {"size": 3926, "mtime": 1754876069014, "results": "192", "hashOfConfig": "138"}, {"size": 6658, "mtime": 1754876068975, "results": "193", "hashOfConfig": "138"}, {"size": 6591, "mtime": 1754876069099, "results": "194", "hashOfConfig": "138"}, {"size": 2229, "mtime": 1754876068973, "results": "195", "hashOfConfig": "138"}, {"size": 20159, "mtime": 1754889171669, "results": "196", "hashOfConfig": "138"}, {"size": 11499, "mtime": 1754884997531, "results": "197", "hashOfConfig": "138"}, {"size": 3199, "mtime": 1754876068902, "results": "198", "hashOfConfig": "138"}, {"size": 1814, "mtime": 1754876069060, "results": "199", "hashOfConfig": "138"}, {"size": 6270, "mtime": 1754876068957, "results": "200", "hashOfConfig": "138"}, {"size": 2487, "mtime": 1754876069049, "results": "201", "hashOfConfig": "138"}, {"size": 2356, "mtime": 1754876069036, "results": "202", "hashOfConfig": "138"}, {"size": 12186, "mtime": 1754888815753, "results": "203", "hashOfConfig": "138"}, {"size": 8056, "mtime": 1754876069042, "results": "204", "hashOfConfig": "138"}, {"size": 5915, "mtime": 1754876069090, "results": "205", "hashOfConfig": "138"}, {"size": 24833, "mtime": 1754876069115, "results": "206", "hashOfConfig": "138"}, {"size": 6063, "mtime": 1754876068894, "results": "207", "hashOfConfig": "138"}, {"size": 11875, "mtime": 1754888579642, "results": "208", "hashOfConfig": "138"}, {"size": 1625, "mtime": 1754876068929, "results": "209", "hashOfConfig": "138"}, {"size": 5076, "mtime": 1754876069051, "results": "210", "hashOfConfig": "138"}, {"size": 6380, "mtime": 1754876069032, "results": "211", "hashOfConfig": "138"}, {"size": 18529, "mtime": 1754888492094, "results": "212", "hashOfConfig": "138"}, {"size": 5362, "mtime": 1754876068873, "results": "213", "hashOfConfig": "138"}, {"size": 3465, "mtime": 1754876069025, "results": "214", "hashOfConfig": "138"}, {"size": 16417, "mtime": 1752112197442, "results": "215", "hashOfConfig": "138"}, {"size": 4182, "mtime": 1752112197518, "results": "216", "hashOfConfig": "138"}, {"size": 16145, "mtime": 1752112197832, "results": "217", "hashOfConfig": "138"}, {"size": 1275, "mtime": 1752112197527, "results": "218", "hashOfConfig": "138"}, {"size": 7979, "mtime": 1752112197925, "results": "219", "hashOfConfig": "138"}, {"size": 4343, "mtime": 1752112197802, "results": "220", "hashOfConfig": "138"}, {"size": 7441, "mtime": 1752112197523, "results": "221", "hashOfConfig": "138"}, {"size": 5759, "mtime": 1752112197547, "results": "222", "hashOfConfig": "138"}, {"size": 15417, "mtime": 1752112197835, "results": "223", "hashOfConfig": "138"}, {"size": 5035, "mtime": 1752112197545, "results": "224", "hashOfConfig": "138"}, {"size": 3340, "mtime": 1752112197648, "results": "225", "hashOfConfig": "138"}, {"size": 9739, "mtime": 1754876068904, "results": "226", "hashOfConfig": "138"}, {"size": 9503, "mtime": 1754888571726, "results": "227", "hashOfConfig": "138"}, {"size": 18522, "mtime": 1754899614817, "results": "228", "hashOfConfig": "138"}, {"size": 4837, "mtime": 1754876069125, "results": "229", "hashOfConfig": "138"}, {"size": 2207, "mtime": 1754876069044, "results": "230", "hashOfConfig": "138"}, {"size": 4778, "mtime": 1754876069046, "results": "231", "hashOfConfig": "138"}, {"size": 3841, "mtime": 1754876068871, "results": "232", "hashOfConfig": "138"}, {"size": 57087, "mtime": 1754876068878, "results": "233", "hashOfConfig": "138"}, {"size": 7062, "mtime": 1752112197673, "results": "234", "hashOfConfig": "138"}, {"size": 16105, "mtime": 1752112197677, "results": "235", "hashOfConfig": "138"}, {"size": 5080, "mtime": 1752112197690, "results": "236", "hashOfConfig": "138"}, {"size": 7009, "mtime": 1752112197714, "results": "237", "hashOfConfig": "138"}, {"size": 16638, "mtime": 1752112197717, "results": "238", "hashOfConfig": "138"}, {"size": 5721, "mtime": 1752112197849, "results": "239", "hashOfConfig": "138"}, {"size": 4264, "mtime": 1752112197792, "results": "240", "hashOfConfig": "138"}, {"size": 6319, "mtime": 1754888605218, "results": "241", "hashOfConfig": "138"}, {"size": 4616, "mtime": 1754888441785, "results": "242", "hashOfConfig": "138"}, {"size": 2907, "mtime": 1752112197795, "results": "243", "hashOfConfig": "138"}, {"size": 1812, "mtime": 1752112197933, "results": "244", "hashOfConfig": "138"}, {"size": 3319, "mtime": 1754888628898, "results": "245", "hashOfConfig": "138"}, {"size": 7379, "mtime": 1752112197535, "results": "246", "hashOfConfig": "138"}, {"size": 1976, "mtime": 1752112197596, "results": "247", "hashOfConfig": "138"}, {"size": 2311, "mtime": 1752112197761, "results": "248", "hashOfConfig": "138"}, {"size": 2672, "mtime": 1752112197930, "results": "249", "hashOfConfig": "138"}, {"size": 5261, "mtime": 1752112197735, "results": "250", "hashOfConfig": "138"}, {"size": 5325, "mtime": 1752112197479, "results": "251", "hashOfConfig": "138"}, {"size": 5674, "mtime": 1752112197483, "results": "252", "hashOfConfig": "138"}, {"size": 5885, "mtime": 1752112197578, "results": "253", "hashOfConfig": "138"}, {"size": 4556, "mtime": 1752112197634, "results": "254", "hashOfConfig": "138"}, {"size": 3536, "mtime": 1752112197653, "results": "255", "hashOfConfig": "138"}, {"size": 2518, "mtime": 1752112197432, "results": "256", "hashOfConfig": "138"}, {"size": 3298, "mtime": 1752112197695, "results": "257", "hashOfConfig": "138"}, {"size": 4285, "mtime": 1752112197698, "results": "258", "hashOfConfig": "138"}, {"size": 2613, "mtime": 1752112197428, "results": "259", "hashOfConfig": "138"}, {"size": 7589, "mtime": 1752112197709, "results": "260", "hashOfConfig": "138"}, {"size": 6350, "mtime": 1752112197538, "results": "261", "hashOfConfig": "138"}, {"size": 5676, "mtime": 1752112197741, "results": "262", "hashOfConfig": "138"}, {"size": 2122, "mtime": 1752112197879, "results": "263", "hashOfConfig": "138"}, {"size": 6188, "mtime": 1752112197897, "results": "264", "hashOfConfig": "138"}, {"size": 2286, "mtime": 1752112197738, "results": "265", "hashOfConfig": "138"}, {"size": 2817, "mtime": 1752112197877, "results": "266", "hashOfConfig": "138"}, {"size": 5056, "mtime": 1752112197816, "results": "267", "hashOfConfig": "138"}, {"size": 2176, "mtime": 1752112197904, "results": "268", "hashOfConfig": "138"}, {"size": 2701, "mtime": 1752112197875, "results": "269", "hashOfConfig": "138"}, {"size": 2285, "mtime": 1752112197906, "results": "270", "hashOfConfig": "138"}, {"size": 5782, "mtime": 1752112197900, "results": "271", "hashOfConfig": "138"}, {"size": 3761, "mtime": 1752112197424, "results": "272", "hashOfConfig": "138"}, {"size": 5809, "mtime": 1752112197784, "results": "273", "hashOfConfig": "138"}, {"filePath": "274", "messages": "275", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1imlu9k", {"filePath": "276", "messages": "277", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "278", "messages": "279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "280", "messages": "281", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "282", "messages": "283", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "284", "messages": "285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "286", "messages": "287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "288", "messages": "289", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "290", "messages": "291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "292", "messages": "293", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "294", "messages": "295", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "296", "messages": "297", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "298", "messages": "299", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "300", "messages": "301", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "302", "messages": "303", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "304", "messages": "305", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "306", "messages": "307", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "308", "messages": "309", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "310", "messages": "311", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "312", "messages": "313", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "314", "messages": "315", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "316", "messages": "317", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "318", "messages": "319", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "320", "messages": "321", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "322"}, {"filePath": "323", "messages": "324", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "325", "messages": "326", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "327", "messages": "328", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "329", "messages": "330", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "331", "messages": "332", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "333", "messages": "334", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "335", "messages": "336", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "337", "messages": "338", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "339", "messages": "340", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "322"}, {"filePath": "341", "messages": "342", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "322"}, {"filePath": "343", "messages": "344", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "345", "messages": "346", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "347", "messages": "348", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "349", "messages": "350", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "351", "messages": "352", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "353", "messages": "354", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "355", "messages": "356", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "357", "messages": "358", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "359", "messages": "360", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "361", "messages": "362", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "363", "messages": "364", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "365", "messages": "366", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "367", "messages": "368", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "369", "messages": "370", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "371", "messages": "372", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "373", "messages": "374", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "375", "messages": "376", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "377", "messages": "378", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "379", "messages": "380", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "381", "messages": "382", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "383", "messages": "384", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "385", "messages": "386", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "387", "messages": "388", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "389", "messages": "390", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "391", "messages": "392", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "322"}, {"filePath": "393", "messages": "394", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "322"}, {"filePath": "395", "messages": "396", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "397", "messages": "398", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "399", "messages": "400", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "401", "messages": "402", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "403", "messages": "404", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "405", "messages": "406", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "407", "messages": "408", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "409", "messages": "410", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "411", "messages": "412", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "413", "messages": "414", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "415", "messages": "416", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "417", "messages": "418", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "419", "messages": "420", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "421", "messages": "422", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "423", "messages": "424", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "425", "messages": "426", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "427", "messages": "428", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "429", "messages": "430", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "431", "messages": "432", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "433", "messages": "434", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "435", "messages": "436", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "437", "messages": "438", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "439", "messages": "440", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "441", "messages": "442", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "443", "messages": "444", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "445", "messages": "446", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "447", "messages": "448", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "449", "messages": "450", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "451", "messages": "452", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "453", "messages": "454", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "455", "messages": "456", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "457", "messages": "458", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "459", "messages": "460", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "461", "messages": "462", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "463", "messages": "464", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "465", "messages": "466", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "467", "messages": "468", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "469", "messages": "470", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "471", "messages": "472", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "473", "messages": "474", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "475", "messages": "476", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "477", "messages": "478", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "479", "messages": "480", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "481", "messages": "482", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "483", "messages": "484", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "485", "messages": "486", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "487", "messages": "488", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "489", "messages": "490", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "491", "messages": "492", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "493", "messages": "494", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "495", "messages": "496", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "497", "messages": "498", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "499", "messages": "500", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "501", "messages": "502", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "503", "messages": "504", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "505", "messages": "506", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "507", "messages": "508", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "509", "messages": "510", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "511", "messages": "512", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "513", "messages": "514", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "515", "messages": "516", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "517", "messages": "518", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "519", "messages": "520", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "521", "messages": "522", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "523", "messages": "524", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "525", "messages": "526", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "527", "messages": "528", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "529", "messages": "530", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "531", "messages": "532", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "533", "messages": "534", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "535", "messages": "536", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "537", "messages": "538", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "539", "messages": "540", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "541", "messages": "542", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "543", "messages": "544", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "545", "messages": "546", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\components\\index.js", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\router\\index.js", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuperEdit\\SuperEdit.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\components\\suggest-simple-select-unit\\suggest-simple-select-unit.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\api\\index.js", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\ProposalWorkbench\\OrganizerWorkBench.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestClue\\SuggestClueMine\\SuggestClueMine.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestClue\\SuggestClueAdd\\SuggestClueAdd.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestClue\\SuggestClueControl\\SuggestClueControl.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\UnitSuggestReply\\UnitSuggestReply.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\UnitSuggestConclude\\UnitSuggestConclude.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestClue\\SuggestClueRegister\\SuggestClueRegister.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\ProposalWorkbench\\ProposalCommitteeWorkBench.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\ProposalWorkbench\\TwoDepartmentsWorkBench.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\ProposalWorkbench\\ProposerWorkBench.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\UnitSuggestTransact\\UnitSuggestTransact.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\UnitAllSuggest\\UnitAllSuggest.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestConclude\\SuggestConclude.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestTrackTransact\\SuggestTrackTransact.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestPendingApproval\\SuggestPendingApproval.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestPendingFinalReview\\SuggestPendingFinalReview.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\UnitSuggestTrackTransact\\UnitSuggestTrackTransact.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestApplyForAdjust\\SuggestApplyForAdjust.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestReply\\SuggestReply.vue", [], [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestTransact\\SuggestTransact.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestAssign\\SuggestAssign.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestApplyForPostpone\\SuggestApplyForPostpone.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestAdvanceAssign\\SuggestAdvanceAssign.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestPreApplyForAdjust\\SuggestPreApplyForAdjust.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\UnitSuggestAdvanceAssign\\UnitSuggestAdvanceAssign.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestReview\\SuggestReview.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\AllSuggest\\AllSuggest.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestStatistics\\SuggestStatistics.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestDetail\\SuggestDetail.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\ProposalClue\\ProposalClueDetail.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestControls\\SuggestControls.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\BehalfSuggest\\SuggestDraftBox\\SuggestDraftBox.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\BehalfSuggest\\MyJointSuggest\\MyJointSuggest.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\BehalfSuggest\\ProposalManagementTool\\TextQueryTool.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\BehalfSuggest\\SubmitSuggest\\SubmitSuggest.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\BehalfSuggest\\MyLedSuggest\\MyLedSuggest.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestConfig\\SuggestNumbering\\SuggestNumbering.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestConfig\\SuggestUnit\\SuggestUnit.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestConfig\\CollectiveProposalUnit\\CollectiveProposalUnit.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\App.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\assets\\js\\suggestExportWord.js", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\UnitSuggestReply\\organizingUnitEvaluationNew.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\MergerProposal\\component\\ManualMergeProposal.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestClue\\SuggestClueMine\\components\\SuggestClueDetails.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestClue\\SuggestClueMine\\components\\UseTimesDetails.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestClue\\SuggestClueAdd\\components\\SuggestClueDetails.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestClue\\SuggestClueControl\\components\\SuggestClueSubmit.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestClue\\SuggestClueControl\\components\\SuggestClueDetails.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestTransact\\component\\SuggestAnswerTime.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestClue\\SuggestClueRegister\\components\\SuggestClueDetails.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestAssign\\component\\SuggestBatchAssignUnit.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuperEdit\\HandUnitSuperList.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestAssign\\component\\SuggestBatchAssign.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestAssign\\component\\SuggestAssignDetail.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestDetail\\component\\SuggestBasicInfo.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\BehalfSuggest\\MyLedSuggest\\component\\QualityAssessmentList.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestDetail\\SuggestReplyDetail\\SuggestReplyDetail.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestAdvanceAssign\\component\\SuggestBatchAdvanceAssignUnit.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestDetail\\ApplyForAdjustRecords\\ApplyForAdjustRecords.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestControls\\ProposalGroup.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestApplyForAdjust\\component\\SuggestAdjustReview.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestControls\\SupervisionProcess.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestTrackTransact\\component\\SuggestTrackTransactDetail.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\UnitSuggestDetail\\UnitSuggestDetail.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\AllSuggest\\component\\SuggestSerialNumber.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\components\\suggestPrint\\suggestPrint.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\ProposalWorkbench\\NewsDetails.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestDetail\\SegreeSatisfactionDetail\\SegreeSatisfactionDetail.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestConfig\\SuggestNumbering\\component\\SuggestNumberingSubmit.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestReview\\component\\SuggestReviewDetail.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\components\\SimilarityQuery\\SimilarityQuery.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestConfig\\CollectiveProposalUnit\\component\\SubmitCollectiveProposalUnit.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\components\\suggest-select-unit\\suggest-select-unit.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\DoNotReceiveSuggest\\DoNotReceiveSuggest.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestedClassification\\SuggestedClassification.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\MergerProposal\\MergerProposalContainer.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\UnitSummaryReport\\UnitSummaryReport.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestStatistics\\SuggestStatisticsList.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\MergerProposal\\HaveMergerProposal.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\ProposalClue\\SubmitProposalClue.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestedClassification\\SuggestedSubdivide.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\ProposalClue\\ProposalClueList.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestClue\\SuggestDocument\\SuggestDocument.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\BehalfSuggest\\MyLedSuggest\\component\\SubmitSegreeSatisfaction.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuperEdit\\HandUnitSuperNew.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\UnitSuggestDetail\\component\\SubmitSuggestReply.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\UnitSuggestReply\\organizingUnitEvaluationInfo.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestControls\\components\\ActivityMaterialDetails.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestControls\\components\\SubmitActivityMaterial.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\components\\SimilarityQuery\\SimilarityDetails.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\components\\SimilarityQuery\\SuggestDetail.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestConfig\\CollectiveProposalUnitUser\\CollectiveProposalUnitUser.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestConfig\\CollectiveProposalUnitUser\\SubmitCollectiveProposalUnitUser.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestConfig\\SuggestType\\SuggestType.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestConfig\\SuggestUnitUser\\SuggestUnitUser.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestConfig\\SuggestUnitUser\\SuggestUnitUserSubmit.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuperEdit\\SegreeSatisfactionList.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestStatistics\\ColumnChart.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuperEdit\\HandWaySuperEdit.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestedClassification\\SelectHandlingUnit.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestStatistics\\PieChart.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\UnitSummaryReport\\component\\UnitSummaryReportDetails.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestDetail\\ApplyForAdjustResult\\ApplyForAdjustResult.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\MergerProposal\\component\\SmartMergeProposal.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestAssign\\component\\SuggestBatchSendBack.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestDetail\\TrackTransactApplyForRecords\\TrackTransactApplyForRecords.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\UnitSummaryReport\\component\\SubmitUnitSummaryReport.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestDetail\\CommunicationSituation\\CommunicationSituation.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\AllSuggest\\component\\JoinUserManage.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\AllSuggest\\component\\SuggestExchange.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestApplyForPostpone\\component\\SuggestPostponeReview.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestClue\\SuggestClueMine\\components\\SuggestClueSubmit.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestClue\\SuggestDocument\\components\\SuggestDocumentSubmit.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\components\\SuggestRecommendUser\\SuggestRecommendUser.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestConfig\\SuggestType\\component\\SuggestBigType.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestConfig\\SuggestType\\component\\SuggestSmallType.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\components\\SuggestRecommendUnit\\SuggestRecommendUnit.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestConfig\\SuggestUnit\\component\\SuggestUnitSubmit.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\MergerProposal\\component\\SubmitMergerProposal.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestDetail\\CommunicationSituation\\CommunicationSituationSubmit.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\UnitSuggestDetail\\component\\ApplyForTrackTransact.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\UnitSuggestDetail\\component\\SubmitSuggestReplyManage.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestDetail\\CommunicationSituation\\CommunicationSituationDetail.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\UnitSuggestDetail\\component\\ApplyForAnswer.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestTrackTransact\\component\\SuggestTrackTransactReview.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\UnitSuggestDetail\\component\\UnitApplyForAdjustRecords.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\UnitSuggestDetail\\component\\ApplyForAdjust.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\UnitSuggestDetail\\component\\UnitApplyForAnswerRecords.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\UnitSuggestDetail\\component\\SubmitSuggestTrackTransactReply.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\components\\SuggestRecommendType\\SuggestRecommendType.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestReview\\component\\ReviewSimilarityQuery.vue", []]