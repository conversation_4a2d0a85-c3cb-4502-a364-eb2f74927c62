{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, createElementVNode as _createElementVNode, createTextVNode as _createTextVNode, withCtx as _withCtx, openBlock as _openBlock, createBlock as _createBlock, createCommentVNode as _createCommentVNode, vShow as _vShow, withDirectives as _withDirectives, renderList as _renderList, Fragment as _Fragment, createElementBlock as _createElementBlock, toDisplayString as _toDisplayString, KeepAlive as _KeepAlive, resolveDirective as _resolveDirective } from \"vue\";\nvar _hoisted_1 = {\n  class: \"SubmitSuggestBody\"\n};\nvar _hoisted_2 = {\n  class: \"SubmitSuggestNameBody\"\n};\nvar _hoisted_3 = {\n  class: \"opinionsSuggestionsListItem row3\"\n};\nvar _hoisted_4 = {\n  class: \"opinionsSuggestionsListItem row2\"\n};\nvar _hoisted_5 = {\n  class: \"opinionsSuggestionsListItem row1\"\n};\nvar _hoisted_6 = {\n  key: 0,\n  class: \"SubmitSuggestFormInfo\"\n};\nvar _hoisted_7 = {\n  class: \"SubmitSuggestFormInfoText\"\n};\nvar _hoisted_8 = {\n  key: 1,\n  class: \"SubmitSuggestFormInfo\"\n};\nvar _hoisted_9 = {\n  class: \"SubmitSuggestFormInfoText\"\n};\nvar _hoisted_10 = {\n  key: 2,\n  class: \"SubmitSuggestFormInfo\"\n};\nvar _hoisted_11 = {\n  class: \"SubmitSuggestFormInfoText\"\n};\nvar _hoisted_12 = {\n  key: 3,\n  class: \"SubmitSuggestFormInfo\"\n};\nvar _hoisted_13 = {\n  class: \"SubmitSuggestFormInfoText\"\n};\nvar _hoisted_14 = {\n  key: 4,\n  class: \"SubmitSuggestFormInfo\"\n};\nvar _hoisted_15 = {\n  class: \"SubmitSuggestFormInfoText\"\n};\nvar _hoisted_16 = {\n  key: 5,\n  class: \"SubmitSuggestFormInfo\"\n};\nvar _hoisted_17 = {\n  class: \"SubmitSuggestFormInfoText\"\n};\nvar _hoisted_18 = {\n  class: \"SubmitSuggestContactPersonItem row2\"\n};\nvar _hoisted_19 = {\n  class: \"SubmitSuggestContactPersonItem row2\"\n};\nvar _hoisted_20 = {\n  class: \"SubmitSuggestContactPersonItem row3\"\n};\nvar _hoisted_21 = {\n  class: \"SubmitSuggestContactPersonItem row1\"\n};\nvar _hoisted_22 = {\n  key: 2,\n  class: \"globalPaperFormButton\"\n};\nvar _hoisted_23 = {\n  key: 0,\n  class: \"SuggestSegmentation\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_global_dynamic_title = _resolveComponent(\"global-dynamic-title\");\n  var _component_el_radio = _resolveComponent(\"el-radio\");\n  var _component_el_radio_group = _resolveComponent(\"el-radio-group\");\n  var _component_el_form_item = _resolveComponent(\"el-form-item\");\n  var _component_el_input = _resolveComponent(\"el-input\");\n  var _component_input_select_person = _resolveComponent(\"input-select-person\");\n  var _component_el_option = _resolveComponent(\"el-option\");\n  var _component_el_select = _resolveComponent(\"el-select\");\n  var _component_simple_select_person = _resolveComponent(\"simple-select-person\");\n  var _component_intelligent_assistant = _resolveComponent(\"intelligent-assistant\");\n  var _component_el_button = _resolveComponent(\"el-button\");\n  var _component_TinyMceEditor = _resolveComponent(\"TinyMceEditor\");\n  var _component_CirclePlus = _resolveComponent(\"CirclePlus\");\n  var _component_el_icon = _resolveComponent(\"el-icon\");\n  var _component_el_link = _resolveComponent(\"el-link\");\n  var _component_Remove = _resolveComponent(\"Remove\");\n  var _component_xyl_upload_file = _resolveComponent(\"xyl-upload-file\");\n  var _component_suggest_simple_select_unit = _resolveComponent(\"suggest-simple-select-unit\");\n  var _component_el_form = _resolveComponent(\"el-form\");\n  var _component_xyl_popup_window = _resolveComponent(\"xyl-popup-window\");\n  var _component_el_scrollbar = _resolveComponent(\"el-scrollbar\");\n  var _directive_loading = _resolveDirective(\"loading\");\n  return _withDirectives((_openBlock(), _createBlock(_component_el_scrollbar, {\n    always: \"\",\n    class: \"SubmitSuggest\",\n    \"lement-loading-text\": $setup.loadingText\n  }, {\n    default: _withCtx(function () {\n      return [_createElementVNode(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_global_dynamic_title, {\n        templateCode: \"proposal_title\"\n      })]), _createVNode(_component_el_form, {\n        ref: \"formRef\",\n        model: $setup.form,\n        rules: $setup.rules,\n        inline: \"\",\n        \"show-message\": false,\n        class: \"globalPaperForm\"\n      }, {\n        default: _withCtx(function () {\n          return [!$setup.typeShow ? (_openBlock(), _createBlock(_component_el_form_item, {\n            key: 0,\n            label: \"提案提交类型\",\n            prop: \"suggestSubmitWay\",\n            class: \"SubmitSuggestTitle\"\n          }, {\n            default: _withCtx(function () {\n              return [_createVNode(_component_el_radio_group, {\n                modelValue: $setup.form.suggestSubmitWay,\n                \"onUpdate:modelValue\": _cache[0] || (_cache[0] = function ($event) {\n                  return $setup.form.suggestSubmitWay = $event;\n                }),\n                onChange: $setup.submitTypeChange\n              }, {\n                default: _withCtx(function () {\n                  return [_createVNode(_component_el_radio, {\n                    label: \"cppcc_member\"\n                  }, {\n                    default: _withCtx(function () {\n                      return _cache[24] || (_cache[24] = [_createTextVNode(\"委员提案\")]);\n                    }),\n                    _: 1 /* STABLE */\n                  }), _createVNode(_component_el_radio, {\n                    label: \"team\"\n                  }, {\n                    default: _withCtx(function () {\n                      return _cache[25] || (_cache[25] = [_createTextVNode(\"集体提案\")]);\n                    }),\n                    _: 1 /* STABLE */\n                  })];\n                }),\n                _: 1 /* STABLE */\n              }, 8 /* PROPS */, [\"modelValue\"])];\n            }),\n            _: 1 /* STABLE */\n          })) : _createCommentVNode(\"v-if\", true), _createVNode(_component_el_form_item, {\n            label: \"提案标题\",\n            prop: \"title\",\n            class: \"SubmitSuggestTitle\"\n          }, {\n            default: _withCtx(function () {\n              return [_createVNode(_component_el_input, {\n                modelValue: $setup.form.title,\n                \"onUpdate:modelValue\": _cache[1] || (_cache[1] = function ($event) {\n                  return $setup.form.title = $event;\n                }),\n                placeholder: \"请输入提案标题\",\n                \"show-word-limit\": \"\",\n                maxlength: $setup.suggestTitleNumber,\n                clearable: \"\"\n              }, null, 8 /* PROPS */, [\"modelValue\", \"maxlength\"])];\n            }),\n            _: 1 /* STABLE */\n          }), _withDirectives(_createVNode(_component_el_form_item, {\n            label: \"提案者\",\n            prop: \"suggestUserId\",\n            class: \"SubmitSuggestLeft\"\n          }, {\n            default: _withCtx(function () {\n              return [_createVNode(_component_input_select_person, {\n                modelValue: $setup.form.suggestUserId,\n                \"onUpdate:modelValue\": _cache[2] || (_cache[2] = function ($event) {\n                  return $setup.form.suggestUserId = $event;\n                }),\n                placeholder: \"请选择提案者\",\n                disabled: $setup.disabled,\n                tabCode: $setup.tabCode,\n                onCallback: $setup.userCallback\n              }, null, 8 /* PROPS */, [\"modelValue\", \"disabled\", \"tabCode\"])];\n            }),\n            _: 1 /* STABLE */\n          }, 512 /* NEED_PATCH */), [[_vShow, $setup.form.suggestSubmitWay === 'cppcc_member']]), _withDirectives(_createVNode(_component_el_form_item, {\n            label: \"委员证号\"\n          }, {\n            default: _withCtx(function () {\n              return [_createVNode(_component_el_input, {\n                modelValue: $setup.form.cardNumber,\n                \"onUpdate:modelValue\": _cache[3] || (_cache[3] = function ($event) {\n                  return $setup.form.cardNumber = $event;\n                }),\n                disabled: \"\"\n              }, null, 8 /* PROPS */, [\"modelValue\"])];\n            }),\n            _: 1 /* STABLE */\n          }, 512 /* NEED_PATCH */), [[_vShow, $setup.form.suggestSubmitWay === 'cppcc_member']]), _withDirectives(_createVNode(_component_el_form_item, {\n            label: \"界别\",\n            class: \"SubmitSuggestLeft\"\n          }, {\n            default: _withCtx(function () {\n              return [_createVNode(_component_el_input, {\n                modelValue: $setup.form.sectorType,\n                \"onUpdate:modelValue\": _cache[4] || (_cache[4] = function ($event) {\n                  return $setup.form.sectorType = $event;\n                }),\n                disabled: \"\"\n              }, null, 8 /* PROPS */, [\"modelValue\"])];\n            }),\n            _: 1 /* STABLE */\n          }, 512 /* NEED_PATCH */), [[_vShow, $setup.form.suggestSubmitWay === 'cppcc_member']]), _withDirectives(_createVNode(_component_el_form_item, {\n            label: \"联系电话\"\n          }, {\n            default: _withCtx(function () {\n              return [_createVNode(_component_el_input, {\n                modelValue: $setup.form.mobile,\n                \"onUpdate:modelValue\": _cache[5] || (_cache[5] = function ($event) {\n                  return $setup.form.mobile = $event;\n                }),\n                disabled: \"\"\n              }, null, 8 /* PROPS */, [\"modelValue\"])];\n            }),\n            _: 1 /* STABLE */\n          }, 512 /* NEED_PATCH */), [[_vShow, $setup.form.suggestSubmitWay === 'cppcc_member']]), _withDirectives(_createVNode(_component_el_form_item, {\n            label: \"通讯地址\",\n            class: \"SubmitSuggestTitle\"\n          }, {\n            default: _withCtx(function () {\n              return [_createVNode(_component_el_input, {\n                modelValue: $setup.form.callAddress,\n                \"onUpdate:modelValue\": _cache[6] || (_cache[6] = function ($event) {\n                  return $setup.form.callAddress = $event;\n                }),\n                disabled: \"\"\n              }, null, 8 /* PROPS */, [\"modelValue\"])];\n            }),\n            _: 1 /* STABLE */\n          }, 512 /* NEED_PATCH */), [[_vShow, $setup.form.suggestSubmitWay === 'cppcc_member']]), _withDirectives(_createVNode(_component_el_form_item, {\n            label: \"提案者\",\n            prop: \"delegationId\",\n            class: \"SubmitSuggestTitle\"\n          }, {\n            default: _withCtx(function () {\n              return [_createVNode(_component_el_select, {\n                modelValue: $setup.form.delegationId,\n                \"onUpdate:modelValue\": _cache[7] || (_cache[7] = function ($event) {\n                  return $setup.form.delegationId = $event;\n                }),\n                disabled: $setup.isDisabled,\n                placeholder: \"请选择集体提案单位\",\n                clearable: \"\"\n              }, {\n                default: _withCtx(function () {\n                  return [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.delegationData, function (item) {\n                    return _openBlock(), _createBlock(_component_el_option, {\n                      key: item.id,\n                      label: item.name,\n                      value: item.id\n                    }, null, 8 /* PROPS */, [\"label\", \"value\"]);\n                  }), 128 /* KEYED_FRAGMENT */))];\n                }),\n                _: 1 /* STABLE */\n              }, 8 /* PROPS */, [\"modelValue\", \"disabled\"])];\n            }),\n            _: 1 /* STABLE */\n          }, 512 /* NEED_PATCH */), [[_vShow, $setup.form.suggestSubmitWay === 'team']]), _createVNode(_component_el_form_item, {\n            label: \"是否联名提案\",\n            prop: \"isJoinProposal\",\n            class: \"SubmitSuggestTitle\"\n          }, {\n            default: _withCtx(function () {\n              return [_createVNode(_component_el_radio_group, {\n                modelValue: $setup.form.isJoinProposal,\n                \"onUpdate:modelValue\": _cache[8] || (_cache[8] = function ($event) {\n                  return $setup.form.isJoinProposal = $event;\n                }),\n                onChange: $setup.JoinChange\n              }, {\n                default: _withCtx(function () {\n                  return [_createVNode(_component_el_radio, {\n                    label: 1\n                  }, {\n                    default: _withCtx(function () {\n                      return _cache[26] || (_cache[26] = [_createTextVNode(\"是\")]);\n                    }),\n                    _: 1 /* STABLE */\n                  }), _createVNode(_component_el_radio, {\n                    label: 0\n                  }, {\n                    default: _withCtx(function () {\n                      return _cache[27] || (_cache[27] = [_createTextVNode(\"否\")]);\n                    }),\n                    _: 1 /* STABLE */\n                  })];\n                }),\n                _: 1 /* STABLE */\n              }, 8 /* PROPS */, [\"modelValue\"])];\n            }),\n            _: 1 /* STABLE */\n          }), $setup.form.isJoinProposal ? (_openBlock(), _createBlock(_component_el_form_item, {\n            key: 1,\n            label: \"提案联名人\",\n            prop: \"joinUsers\",\n            class: \"SubmitSuggestTitle\"\n          }, {\n            default: _withCtx(function () {\n              return [_createVNode(_component_simple_select_person, {\n                modelValue: $setup.form.joinUsers,\n                \"onUpdate:modelValue\": _cache[9] || (_cache[9] = function ($event) {\n                  return $setup.form.joinUsers = $event;\n                }),\n                placeholder: \"请选择提案联名人\",\n                filterUser: $setup.form.suggestUserId ? [$setup.form.suggestUserId] : [],\n                tabCode: ['cppccMember']\n              }, null, 8 /* PROPS */, [\"modelValue\", \"filterUser\"]), $setup.whetherUseIntelligentize && $setup.queryType !== 'review' ? (_openBlock(), _createBlock(_component_intelligent_assistant, {\n                key: 0,\n                elIsShow: $setup.elIsShow,\n                \"onUpdate:elIsShow\": _cache[10] || (_cache[10] = function ($event) {\n                  return $setup.elIsShow = $event;\n                }),\n                modelValue: $setup.visibleIsShow,\n                \"onUpdate:modelValue\": _cache[11] || (_cache[11] = function ($event) {\n                  return $setup.visibleIsShow = $event;\n                })\n              }, {\n                default: _withCtx(function () {\n                  return [_createVNode($setup[\"SuggestRecommendUser\"], {\n                    params: $setup.userParams,\n                    onCallback: $setup.userInitCallback,\n                    onSelect: $setup.userSelect\n                  }, null, 8 /* PROPS */, [\"params\"])];\n                }),\n                _: 1 /* STABLE */\n              }, 8 /* PROPS */, [\"elIsShow\", \"modelValue\"])) : _createCommentVNode(\"v-if\", true)];\n            }),\n            _: 1 /* STABLE */\n          })) : _createCommentVNode(\"v-if\", true), _createVNode(_component_el_form_item, {\n            label: \"提案内容\",\n            prop: \"content\",\n            class: \"SubmitSuggestTitle SubmitSuggestButton\"\n          }, {\n            default: _withCtx(function () {\n              return [$setup.whetherUseIntelligentize && $setup.queryType === 'review' && $setup.reviewShow ? (_openBlock(), _createBlock(_component_el_button, {\n                key: 0,\n                onClick: _cache[12] || (_cache[12] = function ($event) {\n                  return $setup.handleSimilarity(false);\n                }),\n                type: \"primary\"\n              }, {\n                default: _withCtx(function () {\n                  return _cache[28] || (_cache[28] = [_createTextVNode(\"相似度查询\")]);\n                }),\n                _: 1 /* STABLE */\n              })) : _createCommentVNode(\"v-if\", true)];\n            }),\n            _: 1 /* STABLE */\n          }), _createVNode(_component_TinyMceEditor, {\n            modelValue: $setup.form.content,\n            \"onUpdate:modelValue\": _cache[13] || (_cache[13] = function ($event) {\n              return $setup.form.content = $event;\n            }),\n            setting: $setup.tinyMceSetting,\n            onCount: $setup.handleContentCount,\n            onBlur: $setup.handleContentBlur,\n            textRectify: \"\"\n          }, null, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_el_form_item, {\n            class: \"opinionsSuggestionsList\",\n            label: \"意见建议清单\"\n          }, {\n            default: _withCtx(function () {\n              return [_cache[29] || (_cache[29] = _createElementVNode(\"span\", {\n                style: {\n                  \"position\": \"absolute\",\n                  \"top\": \"0\",\n                  \"left\": \"-17%\",\n                  \"color\": \"#f56c6c\"\n                }\n              }, \"*\", -1 /* HOISTED */)), _cache[30] || (_cache[30] = _createElementVNode(\"div\", {\n                class: \"opinionsSuggestionsListHead\"\n              }, [_createElementVNode(\"div\", {\n                class: \"opinionsSuggestionsListItem row3\"\n              }, \"建议\"), _createElementVNode(\"div\", {\n                class: \"opinionsSuggestionsListItem row2\"\n              }, \"补充说明\"), _createElementVNode(\"div\", {\n                class: \"opinionsSuggestionsListItem row1\"\n              }, \"操作\")], -1 /* HOISTED */)), (_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.opinionsSuggestionsList, function (item) {\n                return _openBlock(), _createElementBlock(\"div\", {\n                  class: \"opinionsSuggestionsListBody\",\n                  key: item.id\n                }, [_createElementVNode(\"div\", _hoisted_3, [_createVNode(_component_el_input, {\n                  placeholder: \"请输入建议\",\n                  modelValue: item.suggestion,\n                  \"onUpdate:modelValue\": function onUpdateModelValue($event) {\n                    return item.suggestion = $event;\n                  },\n                  clearable: \"\",\n                  maxlength: \"30\",\n                  \"show-word-limit\": \"\"\n                }, null, 8 /* PROPS */, [\"modelValue\", \"onUpdate:modelValue\"])]), _createElementVNode(\"div\", _hoisted_4, [_createVNode(_component_el_input, {\n                  placeholder: \"请输入补充说明\",\n                  modelValue: item.explanation,\n                  \"onUpdate:modelValue\": function onUpdateModelValue($event) {\n                    return item.explanation = $event;\n                  },\n                  clearable: \"\"\n                }, null, 8 /* PROPS */, [\"modelValue\", \"onUpdate:modelValue\"])]), _createElementVNode(\"div\", _hoisted_5, [$setup.opinionsSuggestionsList.length ? (_openBlock(), _createBlock(_component_el_link, {\n                  key: 0,\n                  onClick: $setup.newOpinionsSuggestions\n                }, {\n                  default: _withCtx(function () {\n                    return [_createVNode(_component_el_icon, null, {\n                      default: _withCtx(function () {\n                        return [_createVNode(_component_CirclePlus)];\n                      }),\n                      _: 1 /* STABLE */\n                    })];\n                  }),\n                  _: 1 /* STABLE */\n                })) : _createCommentVNode(\"v-if\", true), $setup.opinionsSuggestionsList.length > 1 ? (_openBlock(), _createBlock(_component_el_link, {\n                  key: 1,\n                  onClick: function onClick($event) {\n                    return $setup.delOpinionsSuggestions(item.id);\n                  }\n                }, {\n                  default: _withCtx(function () {\n                    return [_createVNode(_component_el_icon, null, {\n                      default: _withCtx(function () {\n                        return [_createVNode(_component_Remove)];\n                      }),\n                      _: 1 /* STABLE */\n                    })];\n                  }),\n                  _: 2 /* DYNAMIC */\n                }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"])) : _createCommentVNode(\"v-if\", true)])]);\n              }), 128 /* KEYED_FRAGMENT */)), _cache[31] || (_cache[31] = _createElementVNode(\"div\", {\n                style: {\n                  \"border-top\": \"1px solid #3657C0\",\n                  \"width\": \"100%\",\n                  \"text-align\": \"center\",\n                  \"color\": \"red\"\n                }\n              }, \" 提案建议清单为必填项，须高度概括、事权明晰、简洁明了，请勿粘帖建议原文内容\", -1 /* HOISTED */))];\n            }),\n            _: 1 /* STABLE */\n          }), _createVNode(_component_el_form_item, {\n            label: \"上传附件\",\n            class: \"SubmitSuggestFormUpload\"\n          }, {\n            default: _withCtx(function () {\n              return [_createVNode(_component_xyl_upload_file, {\n                fileData: $setup.fileData,\n                onFileUpload: $setup.fileUpload,\n                fileType: ['jpg', 'png', 'gif', 'jpeg', 'txt', 'doc', 'docx', 'wps', 'ppt', 'pptx', 'pdf', 'ofd', 'xls', 'xlsx', 'zip', 'rar', 'amr', 'mp4', 'avi', 'wav', 'uof', 'rtf', 'eio']\n              }, null, 8 /* PROPS */, [\"fileData\"])];\n            }),\n            _: 1 /* STABLE */\n          }), _createVNode(_component_el_form_item, {\n            label: \"提案相关情况\",\n            class: \"SubmitSuggestFormItem\"\n          }, {\n            default: _withCtx(function () {\n              return [$setup.suggestOpenTypeName ? (_openBlock(), _createElementBlock(\"div\", _hoisted_6, [_createElementVNode(\"div\", _hoisted_7, _toDisplayString($setup.suggestOpenTypeName) + \"：\", 1 /* TEXT */), _createVNode(_component_el_radio_group, {\n                modelValue: $setup.form.suggestOpenType,\n                \"onUpdate:modelValue\": _cache[14] || (_cache[14] = function ($event) {\n                  return $setup.form.suggestOpenType = $event;\n                })\n              }, {\n                default: _withCtx(function () {\n                  return [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.suggestOpenType, function (item) {\n                    return _openBlock(), _createBlock(_component_el_radio, {\n                      key: item.key,\n                      label: item.key\n                    }, {\n                      default: _withCtx(function () {\n                        return [_createTextVNode(_toDisplayString(item.name), 1 /* TEXT */)];\n                      }),\n                      _: 2 /* DYNAMIC */\n                    }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"label\"]);\n                  }), 128 /* KEYED_FRAGMENT */))];\n                }),\n                _: 1 /* STABLE */\n              }, 8 /* PROPS */, [\"modelValue\"])])) : _createCommentVNode(\"v-if\", true), $setup.suggestSurveyTypeName ? (_openBlock(), _createElementBlock(\"div\", _hoisted_8, [_createElementVNode(\"div\", _hoisted_9, _toDisplayString($setup.suggestSurveyTypeName) + \"：\", 1 /* TEXT */), _createVNode(_component_el_radio_group, {\n                modelValue: $setup.form.suggestSurveyType,\n                \"onUpdate:modelValue\": _cache[15] || (_cache[15] = function ($event) {\n                  return $setup.form.suggestSurveyType = $event;\n                })\n              }, {\n                default: _withCtx(function () {\n                  return [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.suggestSurveyType, function (item) {\n                    return _openBlock(), _createBlock(_component_el_radio, {\n                      key: item.key,\n                      label: item.key\n                    }, {\n                      default: _withCtx(function () {\n                        return [_createTextVNode(_toDisplayString(item.name), 1 /* TEXT */)];\n                      }),\n                      _: 2 /* DYNAMIC */\n                    }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"label\"]);\n                  }), 128 /* KEYED_FRAGMENT */))];\n                }),\n                _: 1 /* STABLE */\n              }, 8 /* PROPS */, [\"modelValue\"])])) : _createCommentVNode(\"v-if\", true), $setup.isMakeMineJobName ? (_openBlock(), _createElementBlock(\"div\", _hoisted_10, [_createElementVNode(\"div\", _hoisted_11, _toDisplayString($setup.isMakeMineJobName) + \"：\", 1 /* TEXT */), _createVNode(_component_el_radio_group, {\n                modelValue: $setup.form.isMakeMineJob,\n                \"onUpdate:modelValue\": _cache[16] || (_cache[16] = function ($event) {\n                  return $setup.form.isMakeMineJob = $event;\n                })\n              }, {\n                default: _withCtx(function () {\n                  return [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.isMakeMineJob, function (item) {\n                    return _openBlock(), _createBlock(_component_el_radio, {\n                      key: item.key,\n                      label: item.key\n                    }, {\n                      default: _withCtx(function () {\n                        return [_createTextVNode(_toDisplayString(item.name), 1 /* TEXT */)];\n                      }),\n                      _: 2 /* DYNAMIC */\n                    }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"label\"]);\n                  }), 128 /* KEYED_FRAGMENT */))];\n                }),\n                _: 1 /* STABLE */\n              }, 8 /* PROPS */, [\"modelValue\"])])) : _createCommentVNode(\"v-if\", true), $setup.notHandleTimeTypeName ? (_openBlock(), _createElementBlock(\"div\", _hoisted_12, [_createElementVNode(\"div\", _hoisted_13, _toDisplayString($setup.notHandleTimeTypeName) + \"：\", 1 /* TEXT */), _createVNode(_component_el_radio_group, {\n                modelValue: $setup.form.notHandleTimeType,\n                \"onUpdate:modelValue\": _cache[17] || (_cache[17] = function ($event) {\n                  return $setup.form.notHandleTimeType = $event;\n                })\n              }, {\n                default: _withCtx(function () {\n                  return [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.notHandleTimeType, function (item) {\n                    return _openBlock(), _createBlock(_component_el_radio, {\n                      key: item.key,\n                      label: item.key\n                    }, {\n                      default: _withCtx(function () {\n                        return [_createTextVNode(_toDisplayString(item.name), 1 /* TEXT */)];\n                      }),\n                      _: 2 /* DYNAMIC */\n                    }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"label\"]);\n                  }), 128 /* KEYED_FRAGMENT */))];\n                }),\n                _: 1 /* STABLE */\n              }, 8 /* PROPS */, [\"modelValue\"])])) : _createCommentVNode(\"v-if\", true), $setup.isHopeEnhanceTalkName ? (_openBlock(), _createElementBlock(\"div\", _hoisted_14, [_createElementVNode(\"div\", _hoisted_15, _toDisplayString($setup.isHopeEnhanceTalkName) + \"：\", 1 /* TEXT */), _createVNode(_component_el_radio_group, {\n                modelValue: $setup.form.isHopeEnhanceTalk,\n                \"onUpdate:modelValue\": _cache[18] || (_cache[18] = function ($event) {\n                  return $setup.form.isHopeEnhanceTalk = $event;\n                })\n              }, {\n                default: _withCtx(function () {\n                  return [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.isHopeEnhanceTalk, function (item) {\n                    return _openBlock(), _createBlock(_component_el_radio, {\n                      key: item.key,\n                      label: item.key\n                    }, {\n                      default: _withCtx(function () {\n                        return [_createTextVNode(_toDisplayString(item.name), 1 /* TEXT */)];\n                      }),\n                      _: 2 /* DYNAMIC */\n                    }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"label\"]);\n                  }), 128 /* KEYED_FRAGMENT */))];\n                }),\n                _: 1 /* STABLE */\n              }, 8 /* PROPS */, [\"modelValue\"])])) : _createCommentVNode(\"v-if\", true), $setup.isNeedPaperAnswerName ? (_openBlock(), _createElementBlock(\"div\", _hoisted_16, [_createElementVNode(\"div\", _hoisted_17, _toDisplayString($setup.isNeedPaperAnswerName) + \"：\", 1 /* TEXT */), _createVNode(_component_el_radio_group, {\n                modelValue: $setup.form.isNeedPaperAnswer,\n                \"onUpdate:modelValue\": _cache[19] || (_cache[19] = function ($event) {\n                  return $setup.form.isNeedPaperAnswer = $event;\n                })\n              }, {\n                default: _withCtx(function () {\n                  return [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.isNeedPaperAnswer, function (item) {\n                    return _openBlock(), _createBlock(_component_el_radio, {\n                      key: item.key,\n                      label: item.key\n                    }, {\n                      default: _withCtx(function () {\n                        return [_createTextVNode(_toDisplayString(item.name), 1 /* TEXT */)];\n                      }),\n                      _: 2 /* DYNAMIC */\n                    }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"label\"]);\n                  }), 128 /* KEYED_FRAGMENT */))];\n                }),\n                _: 1 /* STABLE */\n              }, 8 /* PROPS */, [\"modelValue\"])])) : _createCommentVNode(\"v-if\", true)];\n            }),\n            _: 1 /* STABLE */\n          }), _createVNode(_component_el_form_item, {\n            label: \"希望送交办单位\",\n            class: \"SubmitSuggestTitle\"\n          }, {\n            default: _withCtx(function () {\n              return [_createVNode(_component_suggest_simple_select_unit, {\n                modelValue: $setup.form.hopeHandleOfficeIds,\n                \"onUpdate:modelValue\": _cache[20] || (_cache[20] = function ($event) {\n                  return $setup.form.hopeHandleOfficeIds = $event;\n                })\n              }, null, 8 /* PROPS */, [\"modelValue\"])];\n            }),\n            _: 1 /* STABLE */\n          }), _createVNode(_component_el_form_item, {\n            class: \"SubmitSuggestContactPerson\",\n            label: \"提案联系人\"\n          }, {\n            default: _withCtx(function () {\n              return [_cache[32] || (_cache[32] = _createElementVNode(\"div\", {\n                class: \"SubmitSuggestContactPersonHead\"\n              }, [_createElementVNode(\"div\", {\n                class: \"SubmitSuggestContactPersonItem row2\"\n              }, \"提案联系人姓名\"), _createElementVNode(\"div\", {\n                class: \"SubmitSuggestContactPersonItem row2\"\n              }, \"提案联系人电话\"), _createElementVNode(\"div\", {\n                class: \"SubmitSuggestContactPersonItem row3\"\n              }, \"联系人通讯地址\"), _createElementVNode(\"div\", {\n                class: \"SubmitSuggestContactPersonItem row1\"\n              }, \"操作\")], -1 /* HOISTED */)), (_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.contactPersonList, function (item) {\n                return _openBlock(), _createElementBlock(\"div\", {\n                  class: \"SubmitSuggestContactPersonBody\",\n                  key: item.id\n                }, [_createElementVNode(\"div\", _hoisted_18, [_createVNode(_component_el_input, {\n                  placeholder: \"请输入联系人姓名\",\n                  modelValue: item.contactName,\n                  \"onUpdate:modelValue\": function onUpdateModelValue($event) {\n                    return item.contactName = $event;\n                  },\n                  clearable: \"\"\n                }, null, 8 /* PROPS */, [\"modelValue\", \"onUpdate:modelValue\"])]), _createElementVNode(\"div\", _hoisted_19, [_createVNode(_component_el_input, {\n                  placeholder: \"请输入联系人电话\",\n                  modelValue: item.contactPhone,\n                  \"onUpdate:modelValue\": function onUpdateModelValue($event) {\n                    return item.contactPhone = $event;\n                  },\n                  clearable: \"\"\n                }, null, 8 /* PROPS */, [\"modelValue\", \"onUpdate:modelValue\"])]), _createElementVNode(\"div\", _hoisted_20, [_createVNode(_component_el_input, {\n                  placeholder: \"请输入联系人通讯地址\",\n                  modelValue: item.contactAddress,\n                  \"onUpdate:modelValue\": function onUpdateModelValue($event) {\n                    return item.contactAddress = $event;\n                  },\n                  clearable: \"\"\n                }, null, 8 /* PROPS */, [\"modelValue\", \"onUpdate:modelValue\"])]), _createElementVNode(\"div\", _hoisted_21, [$setup.contactPersonList.length ? (_openBlock(), _createBlock(_component_el_link, {\n                  key: 0,\n                  onClick: $setup.newContactPerson\n                }, {\n                  default: _withCtx(function () {\n                    return [_createVNode(_component_el_icon, null, {\n                      default: _withCtx(function () {\n                        return [_createVNode(_component_CirclePlus)];\n                      }),\n                      _: 1 /* STABLE */\n                    })];\n                  }),\n                  _: 1 /* STABLE */\n                })) : _createCommentVNode(\"v-if\", true), $setup.contactPersonList.length > 1 ? (_openBlock(), _createBlock(_component_el_link, {\n                  key: 1,\n                  onClick: function onClick($event) {\n                    return $setup.delContactPerson(item.id);\n                  }\n                }, {\n                  default: _withCtx(function () {\n                    return [_createVNode(_component_el_icon, null, {\n                      default: _withCtx(function () {\n                        return [_createVNode(_component_Remove)];\n                      }),\n                      _: 1 /* STABLE */\n                    })];\n                  }),\n                  _: 2 /* DYNAMIC */\n                }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"])) : _createCommentVNode(\"v-if\", true)])]);\n              }), 128 /* KEYED_FRAGMENT */))];\n            }),\n            _: 1 /* STABLE */\n          }), $setup.queryType !== 'review' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_22, [_createVNode(_component_el_button, {\n            type: \"primary\",\n            onClick: _cache[21] || (_cache[21] = function ($event) {\n              return $setup.submitForm($setup.formRef, 0);\n            })\n          }, {\n            default: _withCtx(function () {\n              return _cache[33] || (_cache[33] = [_createTextVNode(\"提交提案\")]);\n            }),\n            _: 1 /* STABLE */\n          }), !$setup.route.query.anewId && !$setup.route.query.id || $setup.queryType === 'draft' ? (_openBlock(), _createBlock(_component_el_button, {\n            key: 0,\n            onClick: _cache[22] || (_cache[22] = function ($event) {\n              return $setup.submitForm($setup.formRef, 1);\n            })\n          }, {\n            default: _withCtx(function () {\n              return _cache[34] || (_cache[34] = [_createTextVNode(\" 存为草稿 \")]);\n            }),\n            _: 1 /* STABLE */\n          })) : _createCommentVNode(\"v-if\", true), !$setup.route.query.id ? (_openBlock(), _createBlock(_component_el_button, {\n            key: 1,\n            onClick: $setup.resetForm\n          }, {\n            default: _withCtx(function () {\n              return _cache[35] || (_cache[35] = [_createTextVNode(\"重置\")]);\n            }),\n            _: 1 /* STABLE */\n          })) : _createCommentVNode(\"v-if\", true), $setup.route.query.id ? (_openBlock(), _createBlock(_component_el_button, {\n            key: 2,\n            onClick: $setup.resetForm\n          }, {\n            default: _withCtx(function () {\n              return _cache[36] || (_cache[36] = [_createTextVNode(\"取消\")]);\n            }),\n            _: 1 /* STABLE */\n          })) : _createCommentVNode(\"v-if\", true)])) : _createCommentVNode(\"v-if\", true)];\n        }),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"model\", \"rules\"]), $setup.queryType === 'review' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_23)) : _createCommentVNode(\"v-if\", true), (_openBlock(), _createBlock(_KeepAlive, null, [$setup.queryType === 'review' && $setup.reviewShow ? (_openBlock(), _createBlock($setup[\"SuggestReviewDetail\"], {\n        key: 0,\n        id: $setup.route.query.id,\n        name: $setup.route.query.reviewName,\n        content: $setup.form.content,\n        hopeHandleOfficeIds: $setup.form.hopeHandleOfficeIds,\n        onEditCallback: $setup.editCallback,\n        onCallback: $setup.resetForm,\n        queryType: $setup.queryType,\n        signId: $setup.route.query.signId,\n        bigThemeId: $setup.form.bigThemeId,\n        smallThemeId: $setup.form.smallThemeId,\n        james: $setup.form.james,\n        jordan: $setup.form.jordan,\n        kobe: $setup.form.kobe,\n        duncan: $setup.form.duncan,\n        wade: $setup.form.wade\n      }, null, 8 /* PROPS */, [\"id\", \"name\", \"content\", \"hopeHandleOfficeIds\", \"queryType\", \"signId\", \"bigThemeId\", \"smallThemeId\", \"james\", \"jordan\", \"kobe\", \"duncan\", \"wade\"])) : _createCommentVNode(\"v-if\", true)], 1024 /* DYNAMIC_SLOTS */))]), _createVNode(_component_xyl_popup_window, {\n        modelValue: $setup.show,\n        \"onUpdate:modelValue\": _cache[23] || (_cache[23] = function ($event) {\n          return $setup.show = $event;\n        }),\n        name: \"相似度查询\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode($setup[\"SimilarityQuery\"], {\n            type: $setup.isShow,\n            id: $setup.route.query.id,\n            content: $setup.form.content,\n            onCallback: $setup.handleSimilarityCallback\n          }, null, 8 /* PROPS */, [\"type\", \"id\", \"content\"])];\n        }),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"modelValue\"])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"lement-loading-text\"])), [[_directive_loading, $setup.loading]]);\n}", "map": {"version": 3, "names": ["class", "key", "_createBlock", "_component_el_scrollbar", "always", "$setup", "loadingText", "default", "_withCtx", "_createElementVNode", "_hoisted_1", "_hoisted_2", "_createVNode", "_component_global_dynamic_title", "templateCode", "_component_el_form", "ref", "model", "form", "rules", "inline", "typeShow", "_component_el_form_item", "label", "prop", "_component_el_radio_group", "modelValue", "suggestSubmitWay", "_cache", "$event", "onChange", "submitTypeChange", "_component_el_radio", "_createTextVNode", "_", "_createCommentVNode", "_component_el_input", "title", "placeholder", "maxlength", "suggestTitleNumber", "clearable", "_component_input_select_person", "suggestUserId", "disabled", "tabCode", "onCallback", "userCallback", "cardNumber", "sectorType", "mobile", "call<PERSON>dd<PERSON>", "_component_el_select", "delegationId", "isDisabled", "_createElementBlock", "_Fragment", "_renderList", "delegationData", "item", "_component_el_option", "id", "name", "value", "isJoinProposal", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_component_simple_select_person", "joinUsers", "filterUser", "whetherUseIntelligentize", "queryType", "_component_intelligent_assistant", "elIsShow", "visibleIsShow", "params", "userParams", "userInitCallback", "onSelect", "userSelect", "reviewShow", "_component_el_button", "onClick", "handleSimilarity", "type", "_component_TinyMceEditor", "content", "setting", "tinyMceSetting", "onCount", "handleContentCount", "onBlur", "handleContentBlur", "textRectify", "style", "opinionsSuggestionsList", "_hoisted_3", "suggestion", "onUpdateModelValue", "_hoisted_4", "explanation", "_hoisted_5", "length", "_component_el_link", "newOpinionsSuggestions", "_component_el_icon", "_component_CirclePlus", "delOpinionsSuggestions", "_component_Remove", "_component_xyl_upload_file", "fileData", "onFileUpload", "fileUpload", "fileType", "suggestOpenTypeName", "_hoisted_6", "_hoisted_7", "_toDisplayString", "suggestOpenType", "suggestSurveyTypeName", "_hoisted_8", "_hoisted_9", "suggestSurveyType", "isMakeMineJobName", "_hoisted_10", "_hoisted_11", "isMakeMineJob", "notHandleTimeTypeName", "_hoisted_12", "_hoisted_13", "notHandleTimeType", "isHopeEnhanceTalkName", "_hoisted_14", "_hoisted_15", "isHopeEnhanceTalk", "isNeedPaperAnswerName", "_hoisted_16", "_hoisted_17", "isNeedPaperAnswer", "_component_suggest_simple_select_unit", "hopeHandleOfficeIds", "contactPersonList", "_hoisted_18", "contactName", "_hoisted_19", "contactPhone", "_hoisted_20", "contactAddress", "_hoisted_21", "newContact<PERSON>erson", "delContact<PERSON>erson", "_hoisted_22", "submitForm", "formRef", "route", "query", "anewId", "resetForm", "_hoisted_23", "_KeepAlive", "reviewName", "onEditCallback", "edit<PERSON>allback", "signId", "bigThemeId", "smallThemeId", "james", "jordan", "kobe", "duncan", "wade", "_component_xyl_popup_window", "show", "isShow", "handleSimilarityCallback", "loading"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\BehalfSuggest\\SubmitSuggest\\SubmitSuggest.vue"], "sourcesContent": ["<template>\r\n  <el-scrollbar always class=\"SubmitSuggest\" v-loading=\"loading\" :lement-loading-text=\"loadingText\">\r\n    <div class=\"SubmitSuggestBody\">\r\n      <div class=\"SubmitSuggestNameBody\">\r\n        <global-dynamic-title templateCode=\"proposal_title\"></global-dynamic-title>\r\n      </div>\r\n      <el-form ref=\"formRef\" :model=\"form\" :rules=\"rules\" inline :show-message=\"false\" class=\"globalPaperForm\">\r\n        <el-form-item label=\"提案提交类型\" v-if=\"!typeShow\" prop=\"suggestSubmitWay\" class=\"SubmitSuggestTitle\">\r\n          <el-radio-group v-model=\"form.suggestSubmitWay\" @change=\"submitTypeChange\">\r\n            <el-radio label=\"cppcc_member\">委员提案</el-radio>\r\n            <el-radio label=\"team\">集体提案</el-radio>\r\n          </el-radio-group>\r\n        </el-form-item>\r\n        <el-form-item label=\"提案标题\" prop=\"title\" class=\"SubmitSuggestTitle\">\r\n          <el-input v-model=\"form.title\" placeholder=\"请输入提案标题\" show-word-limit :maxlength=\"suggestTitleNumber\"\r\n            clearable />\r\n        </el-form-item>\r\n        <el-form-item v-show=\"form.suggestSubmitWay === 'cppcc_member'\" label=\"提案者\" prop=\"suggestUserId\"\r\n          class=\"SubmitSuggestLeft\">\r\n          <input-select-person v-model=\"form.suggestUserId\" placeholder=\"请选择提案者\" :disabled=\"disabled\" :tabCode=\"tabCode\"\r\n            @callback=\"userCallback\" />\r\n        </el-form-item>\r\n        <el-form-item v-show=\"form.suggestSubmitWay === 'cppcc_member'\" label=\"委员证号\">\r\n          <el-input v-model=\"form.cardNumber\" disabled />\r\n        </el-form-item>\r\n        <el-form-item v-show=\"form.suggestSubmitWay === 'cppcc_member'\" label=\"界别\" class=\"SubmitSuggestLeft\">\r\n          <el-input v-model=\"form.sectorType\" disabled />\r\n        </el-form-item>\r\n        <el-form-item v-show=\"form.suggestSubmitWay === 'cppcc_member'\" label=\"联系电话\">\r\n          <el-input v-model=\"form.mobile\" disabled />\r\n        </el-form-item>\r\n        <el-form-item v-show=\"form.suggestSubmitWay === 'cppcc_member'\" label=\"通讯地址\" class=\"SubmitSuggestTitle\">\r\n          <el-input v-model=\"form.callAddress\" disabled />\r\n        </el-form-item>\r\n        <el-form-item v-show=\"form.suggestSubmitWay === 'team'\" label=\"提案者\" prop=\"delegationId\"\r\n          class=\"SubmitSuggestTitle\">\r\n          <el-select v-model=\"form.delegationId\" :disabled=\"isDisabled\" placeholder=\"请选择集体提案单位\" clearable>\r\n            <el-option v-for=\"item in delegationData\" :key=\"item.id\" :label=\"item.name\" :value=\"item.id\" />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"是否联名提案\" prop=\"isJoinProposal\" class=\"SubmitSuggestTitle\">\r\n          <el-radio-group v-model=\"form.isJoinProposal\" @change=\"JoinChange\">\r\n            <el-radio :label=\"1\">是</el-radio>\r\n            <el-radio :label=\"0\">否</el-radio>\r\n          </el-radio-group>\r\n        </el-form-item>\r\n        <el-form-item label=\"提案联名人\" prop=\"joinUsers\" v-if=\"form.isJoinProposal\" class=\"SubmitSuggestTitle\">\r\n          <simple-select-person v-model=\"form.joinUsers\" placeholder=\"请选择提案联名人\"\r\n            :filterUser=\"form.suggestUserId ? [form.suggestUserId] : []\"\r\n            :tabCode=\"['cppccMember']\"></simple-select-person>\r\n          <template v-if=\"whetherUseIntelligentize && queryType !== 'review'\">\r\n            <intelligent-assistant v-model:elIsShow=\"elIsShow\" v-model=\"visibleIsShow\">\r\n              <SuggestRecommendUser :params=\"userParams\" @callback=\"userInitCallback\" @select=\"userSelect\">\r\n              </SuggestRecommendUser>\r\n            </intelligent-assistant>\r\n          </template>\r\n        </el-form-item>\r\n        <el-form-item label=\"提案内容\" prop=\"content\" class=\"SubmitSuggestTitle SubmitSuggestButton\">\r\n          <el-button @click=\"handleSimilarity(false)\"\r\n            v-if=\"whetherUseIntelligentize && queryType === 'review' && reviewShow\" type=\"primary\">相似度查询</el-button>\r\n        </el-form-item>\r\n        <TinyMceEditor v-model=\"form.content\" :setting=\"tinyMceSetting\" @count=\"handleContentCount\"\r\n          @blur=\"handleContentBlur\" textRectify />\r\n        <el-form-item class=\"opinionsSuggestionsList\" label=\"意见建议清单\">\r\n          <span style=\"position: absolute;top: 0;left:-17%;color:#f56c6c;\">*</span>\r\n          <div class=\"opinionsSuggestionsListHead\">\r\n            <div class=\"opinionsSuggestionsListItem row3\">建议</div>\r\n            <div class=\"opinionsSuggestionsListItem row2\">补充说明</div>\r\n            <div class=\"opinionsSuggestionsListItem row1\">操作</div>\r\n          </div>\r\n          <div class=\"opinionsSuggestionsListBody\" v-for=\"item in opinionsSuggestionsList\" :key=\"item.id\">\r\n            <div class=\"opinionsSuggestionsListItem row3\">\r\n              <el-input placeholder=\"请输入建议\" v-model=\"item.suggestion\" clearable maxlength=\"30\" show-word-limit>\r\n              </el-input>\r\n            </div>\r\n            <div class=\"opinionsSuggestionsListItem row2\">\r\n              <el-input placeholder=\"请输入补充说明\" v-model=\"item.explanation\" clearable>\r\n              </el-input>\r\n            </div>\r\n            <div class=\"opinionsSuggestionsListItem row1\">\r\n              <el-link @click=\"newOpinionsSuggestions\" v-if=\"opinionsSuggestionsList.length\">\r\n                <el-icon>\r\n                  <CirclePlus />\r\n                </el-icon>\r\n              </el-link>\r\n              <el-link v-if=\"opinionsSuggestionsList.length > 1\" @click=\"delOpinionsSuggestions(item.id)\">\r\n                <el-icon>\r\n                  <Remove />\r\n                </el-icon>\r\n              </el-link>\r\n            </div>\r\n          </div>\r\n          <div style=\"border-top: 1px solid #3657C0;width: 100%;text-align: center;color: red;\">\r\n            提案建议清单为必填项，须高度概括、事权明晰、简洁明了，请勿粘帖建议原文内容</div>\r\n        </el-form-item>\r\n        <el-form-item label=\"上传附件\" class=\"SubmitSuggestFormUpload\">\r\n          <xyl-upload-file :fileData=\"fileData\" @fileUpload=\"fileUpload\"\r\n            :fileType=\"['jpg', 'png', 'gif', 'jpeg', 'txt', 'doc', 'docx', 'wps', 'ppt', 'pptx', 'pdf', 'ofd', 'xls', 'xlsx', 'zip', 'rar', 'amr', 'mp4', 'avi', 'wav', 'uof', 'rtf', 'eio']\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"提案相关情况\" class=\"SubmitSuggestFormItem\">\r\n          <div class=\"SubmitSuggestFormInfo\" v-if=\"suggestOpenTypeName\">\r\n            <div class=\"SubmitSuggestFormInfoText\">{{ suggestOpenTypeName }}：</div>\r\n            <el-radio-group v-model=\"form.suggestOpenType\">\r\n              <el-radio v-for=\"item in suggestOpenType\" :key=\"item.key\" :label=\"item.key\">{{ item.name }}</el-radio>\r\n            </el-radio-group>\r\n          </div>\r\n          <div class=\"SubmitSuggestFormInfo\" v-if=\"suggestSurveyTypeName\">\r\n            <div class=\"SubmitSuggestFormInfoText\">{{ suggestSurveyTypeName }}：</div>\r\n            <el-radio-group v-model=\"form.suggestSurveyType\">\r\n              <el-radio v-for=\"item in suggestSurveyType\" :key=\"item.key\" :label=\"item.key\">{{ item.name }}</el-radio>\r\n            </el-radio-group>\r\n          </div>\r\n          <div class=\"SubmitSuggestFormInfo\" v-if=\"isMakeMineJobName\">\r\n            <div class=\"SubmitSuggestFormInfoText\">{{ isMakeMineJobName }}：</div>\r\n            <el-radio-group v-model=\"form.isMakeMineJob\">\r\n              <el-radio v-for=\"item in isMakeMineJob\" :key=\"item.key\" :label=\"item.key\">{{ item.name }}</el-radio>\r\n            </el-radio-group>\r\n          </div>\r\n          <div class=\"SubmitSuggestFormInfo\" v-if=\"notHandleTimeTypeName\">\r\n            <div class=\"SubmitSuggestFormInfoText\">{{ notHandleTimeTypeName }}：</div>\r\n            <el-radio-group v-model=\"form.notHandleTimeType\">\r\n              <el-radio v-for=\"item in notHandleTimeType\" :key=\"item.key\" :label=\"item.key\">{{ item.name }}</el-radio>\r\n            </el-radio-group>\r\n          </div>\r\n          <div class=\"SubmitSuggestFormInfo\" v-if=\"isHopeEnhanceTalkName\">\r\n            <div class=\"SubmitSuggestFormInfoText\">{{ isHopeEnhanceTalkName }}：</div>\r\n            <el-radio-group v-model=\"form.isHopeEnhanceTalk\">\r\n              <el-radio v-for=\"item in isHopeEnhanceTalk\" :key=\"item.key\" :label=\"item.key\">{{ item.name }}</el-radio>\r\n            </el-radio-group>\r\n          </div>\r\n          <div class=\"SubmitSuggestFormInfo\" v-if=\"isNeedPaperAnswerName\">\r\n            <div class=\"SubmitSuggestFormInfoText\">{{ isNeedPaperAnswerName }}：</div>\r\n            <el-radio-group v-model=\"form.isNeedPaperAnswer\">\r\n              <el-radio v-for=\"item in isNeedPaperAnswer\" :key=\"item.key\" :label=\"item.key\">{{ item.name }}</el-radio>\r\n            </el-radio-group>\r\n          </div>\r\n        </el-form-item>\r\n        <el-form-item label=\"希望送交办单位\" class=\"SubmitSuggestTitle\">\r\n          <suggest-simple-select-unit v-model=\"form.hopeHandleOfficeIds\"></suggest-simple-select-unit>\r\n        </el-form-item>\r\n        <el-form-item class=\"SubmitSuggestContactPerson\" label=\"提案联系人\">\r\n          <div class=\"SubmitSuggestContactPersonHead\">\r\n            <div class=\"SubmitSuggestContactPersonItem row2\">提案联系人姓名</div>\r\n            <div class=\"SubmitSuggestContactPersonItem row2\">提案联系人电话</div>\r\n            <div class=\"SubmitSuggestContactPersonItem row3\">联系人通讯地址</div>\r\n            <div class=\"SubmitSuggestContactPersonItem row1\">操作</div>\r\n          </div>\r\n          <div class=\"SubmitSuggestContactPersonBody\" v-for=\"item in contactPersonList\" :key=\"item.id\">\r\n            <div class=\"SubmitSuggestContactPersonItem row2\">\r\n              <el-input placeholder=\"请输入联系人姓名\" v-model=\"item.contactName\" clearable></el-input>\r\n            </div>\r\n            <div class=\"SubmitSuggestContactPersonItem row2\">\r\n              <el-input placeholder=\"请输入联系人电话\" v-model=\"item.contactPhone\" clearable></el-input>\r\n            </div>\r\n            <div class=\"SubmitSuggestContactPersonItem row3\">\r\n              <el-input placeholder=\"请输入联系人通讯地址\" v-model=\"item.contactAddress\" clearable></el-input>\r\n            </div>\r\n            <div class=\"SubmitSuggestContactPersonItem row1\">\r\n              <el-link @click=\"newContactPerson\" v-if=\"contactPersonList.length\">\r\n                <el-icon>\r\n                  <CirclePlus />\r\n                </el-icon>\r\n              </el-link>\r\n              <el-link v-if=\"contactPersonList.length > 1\" @click=\"delContactPerson(item.id)\">\r\n                <el-icon>\r\n                  <Remove />\r\n                </el-icon>\r\n              </el-link>\r\n            </div>\r\n          </div>\r\n        </el-form-item>\r\n        <div class=\"globalPaperFormButton\" v-if=\"queryType !== 'review'\">\r\n          <el-button type=\"primary\" @click=\"submitForm(formRef, 0)\">提交提案</el-button>\r\n          <el-button @click=\"submitForm(formRef, 1)\"\r\n            v-if=\"(!route.query.anewId && !route.query.id) || queryType === 'draft'\">\r\n            存为草稿\r\n          </el-button>\r\n          <el-button @click=\"resetForm\" v-if=\"!route.query.id\">重置</el-button>\r\n          <el-button @click=\"resetForm\" v-if=\"route.query.id\">取消</el-button>\r\n        </div>\r\n      </el-form>\r\n      <div v-if=\"queryType === 'review'\" class=\"SuggestSegmentation\"></div>\r\n      <keep-alive>\r\n        <SuggestReviewDetail :id=\"route.query.id\" :name=\"route.query.reviewName\" :content=\"form.content\"\r\n          :hopeHandleOfficeIds=\"form.hopeHandleOfficeIds\" v-if=\"queryType === 'review' && reviewShow\"\r\n          @editCallback=\"editCallback\" @callback=\"resetForm\" :queryType=\"queryType\" :signId=\"route.query.signId\"\r\n          :bigThemeId=\"form.bigThemeId\" :smallThemeId=\"form.smallThemeId\" :james=\"form.james\" :jordan=\"form.jordan\"\r\n          :kobe=\"form.kobe\" :duncan=\"form.duncan\" :wade=\"form.wade\">\r\n        </SuggestReviewDetail>\r\n      </keep-alive>\r\n    </div>\r\n    <xyl-popup-window v-model=\"show\" name=\"相似度查询\">\r\n      <SimilarityQuery :type=\"isShow\" :id=\"route.query.id\" :content=\"form.content\" @callback=\"handleSimilarityCallback\">\r\n      </SimilarityQuery>\r\n    </xyl-popup-window>\r\n  </el-scrollbar>\r\n</template>\r\n<script>\r\nexport default { name: 'SubmitSuggest' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { reactive, ref, onActivated, onDeactivated, onBeforeUnmount, nextTick } from 'vue'\r\nimport { useRoute } from 'vue-router'\r\nimport { useStore } from 'vuex'\r\nimport { user, whetherUseIntelligentize } from 'common/js/system_var.js'\r\nimport { qiankunMicro } from 'common/config/MicroGlobal'\r\nimport { ElMessage } from 'element-plus'\r\nimport SimilarityQuery from '@/components/SimilarityQuery/SimilarityQuery.vue'\r\nimport SuggestRecommendUser from '@/components/SuggestRecommendUser/SuggestRecommendUser.vue'\r\nimport SuggestReviewDetail from '@/views/SuggestReview/component/SuggestReviewDetail.vue'\r\n\r\nconst route = useRoute()\r\nconst store = useStore()\r\nconst loading = ref(false)\r\nconst loadingText = ref('')\r\nconst formRef = ref()\r\nconst form = reactive({\r\n  suggestSubmitWay: 'cppcc_member',\r\n  title: '', // 提案标题\r\n  SuggestBigType: '', // 提案大类\r\n  SuggestSmallType: '', // 提案小类\r\n  suggestUserId: '',\r\n  cardNumber: '',\r\n  sectorType: '',\r\n  mobile: '',\r\n  callAddress: '',\r\n  delegationId: '',\r\n  isJoinProposal: 0,\r\n  joinUsers: [],\r\n  content: '',\r\n  suggestOpenType: 'open_all',\r\n  suggestSurveyType: '3',\r\n  isMakeMineJob: '1',\r\n  notHandleTimeType: '1',\r\n  isHopeEnhanceTalk: '1',\r\n  isNeedPaperAnswer: '1',\r\n  hopeHandleOfficeIds: [],\r\n  bigThemeId: '',\r\n  smallThemeId: '',\r\n  james: '',\r\n  jordan: '',\r\n  kobe: '',\r\n  duncan: '',\r\n  wade: ''\r\n})\r\nconst rules = reactive({\r\n  suggestSubmitWay: [{ required: true, message: '请选择提案提交类型', trigger: ['blur', 'change'] }],\r\n  title: [{ required: true, message: '请输入提案标题', trigger: ['blur', 'change'] }],\r\n  content: [{ required: true, message: '请输入提案内容', trigger: ['blur', 'change'] }],\r\n  suggestUserId: [{ required: true, message: '请选择提案者', trigger: ['blur', 'change'] }],\r\n  delegationId: [{ required: false, message: '请选择集体提案单位', trigger: ['blur', 'change'] }],\r\n  isJoinProposal: [{ required: true, message: '请选择是否联名提案', trigger: ['blur', 'change'] }],\r\n  joinUsers: [{ type: 'array', required: false, message: '请选择提案联名人', trigger: ['blur', 'change'] }]\r\n})\r\n\r\nconst guid = () => {\r\n  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {\r\n    var r = (Math.random() * 16) | 0,\r\n      v = c == 'x' ? r : (r & 0x3) | 0x8\r\n    return v.toString(16)\r\n  })\r\n}\r\nconst tinyMceSetting = {\r\n  tp_layout_options: {\r\n    style: {\r\n      'text-align': 'justify',\r\n      'text-indent': '2em',\r\n      'line-height': '20pt',\r\n      'font-size': '14pt',\r\n      'font-family': '仿宋_GB2312'\r\n    },\r\n    tagsStyle: {\r\n      span: {\r\n        'text-align': 'justify',\r\n        'text-indent': '2em',\r\n        'line-height': '20pt',\r\n        'font-size': '14pt',\r\n        'font-family': '仿宋_GB2312'\r\n      }\r\n    }\r\n  },\r\n  paste_postprocess: (plugin, args) => {\r\n    nextTick(() => {\r\n      args.target.execCommand('mceTpLayout')\r\n      nextTick(() => {\r\n        args.target.selection.collapse()\r\n      })\r\n    })\r\n  },\r\n  import_word_callback: (editor) => {\r\n    nextTick(() => {\r\n      editor.execCommand('mceTpLayout')\r\n      nextTick(() => {\r\n        editor.selection.collapse()\r\n      })\r\n    })\r\n  },\r\n  menubar: `edit insert format table importWord textRectify tpLayout`,\r\n  menu: {\r\n    importWord: { title: '导入Word', items: 'importWord' },\r\n    textRectify: { title: '一键校正', items: 'textRectify' },\r\n    tpLayout: { title: '一键排版', items: 'tpLayout' }\r\n  },\r\n}\r\nconst SuggestBigType = ref([])\r\nconst SuggestSmallType = ref([])\r\nconst suggestTitleNumber = ref(30)\r\nconst suggestContentNumber = ref(2000)\r\nconst suggestMinSimilar = ref(0)\r\nconst suggestContentMinNumber = ref(0)\r\nconst termYearId = ref('')\r\nconst contentCount = ref(0)\r\nconst fileData = ref([])\r\nconst delegationData = ref([])\r\nconst suggestOpenTypeName = ref('')\r\nconst suggestSurveyTypeName = ref('')\r\nconst notHandleTimeTypeName = ref('')\r\nconst isHopeEnhanceTalkName = ref('')\r\nconst isMakeMineJobName = ref('')\r\nconst isNeedPaperAnswerName = ref('')\r\nconst suggestOpenType = ref([])\r\nconst suggestSurveyType = ref([])\r\nconst notHandleTimeType = ref([])\r\nconst isHopeEnhanceTalk = ref([])\r\nconst isMakeMineJob = ref([])\r\nconst isNeedPaperAnswer = ref([])\r\nconst contactPersonList = ref([{ id: guid(), contactName: '', contactPhone: '', contactAddress: '' }])\r\nconst opinionsSuggestionsList = ref([{ id: guid(), suggestion: '', explanation: '' }, { id: guid(), suggestion: '', explanation: '' }, { id: guid(), suggestion: '', explanation: '' }])\r\nconst typeShow = ref(false)\r\nconst disabled = ref(false)\r\nconst isDisabled = ref(false)\r\nconst tabCode = ref(['cppccMember'])\r\nconst reviewShow = ref(false)\r\nconst queryType = ref('')\r\n\r\nconst show = ref(false)\r\nconst isShow = ref(false)\r\nconst elIsShow = ref(false)\r\nconst visibleIsShow = ref(false)\r\nconst userParams = ref({})\r\nlet timer = null\r\n// const AiParams = ref({})\r\n// const elAiChatClass = AiChatClass()\r\n// const handleAiParams = (data) => {\r\n//   if (data) {\r\n//     elAiChatClass.AiChatConfig({\r\n//       AiChatCode: 'ai-intelligent-write-chat',\r\n//       AiChatWindow: true,\r\n//       AiChatFile: data.fileData,\r\n//       AiChatParams: { tool: data.toolId, param: { isPage: '1' } }\r\n//     })\r\n//     elAiChatClass.AiChatHistory()\r\n//     elAiChatClass.AiChatSend(data.toolContent)\r\n//   } else {\r\n//     elAiChatClass.AiChatConfig({ AiChatCode: 'ai-intelligent-write-chat', AiChatWindow: true })\r\n//   }\r\n// }\r\n// onActivated(() => {\r\n//   if (route.query.type !== 'review') {\r\n//     const openAiParams = JSON.parse(sessionStorage.getItem('openAiParams')) || ''\r\n//     if (openAiParams) AiParams.value = openAiParams\r\n//     if (JSON.stringify(AiParams.value) !== '{}') {\r\n//       handleAiParams(openAiParams ? AiParams.value : '')\r\n//       sessionStorage.setItem('openAiParams', JSON.stringify(''))\r\n//       timer = setTimeout(() => {\r\n//         handleAiParams('')\r\n//       }, 2000)\r\n//     }\r\n//   }\r\n\r\nonActivated(() => {\r\n  qiankunMicro.setGlobalState({ AiChatCode: 'ai-intelligent-write-chat' })\r\n  const openAiParams = JSON.parse(sessionStorage.getItem('openAiParams')) || ''\r\n  if (openAiParams) {\r\n    qiankunMicro.setGlobalState({\r\n      AiChatConfig: {\r\n        AiChatWindow: true,\r\n        AiChatFile: openAiParams.fileData,\r\n        AiChatParams: { tool: openAiParams.toolId, param: { isPage: '1' } },\r\n        AiChatSendMessage: openAiParams.toolContent\r\n      }\r\n    })\r\n    sessionStorage.setItem('openAiParams', JSON.stringify(''))\r\n    timer = setTimeout(() => {\r\n      qiankunMicro.setGlobalState({\r\n        AiChatConfig: {\r\n          AiChatWindow: true,\r\n          AiChatFile: openAiParams.fileData,\r\n          AiChatParams: {}\r\n        }\r\n      })\r\n    }, 2000)\r\n  }\r\n  queryType.value = route.query.type\r\n  if (route.query.clueListId) {\r\n    proposalClueInfo()\r\n  }\r\n  globalReadConfig()\r\n  termYearCurrent()\r\n  dictionaryData()\r\n  dictionaryNameData()\r\n  suggestionThemeSelect()\r\n  if (queryType.value === 'draft' || route.query.anewId) {\r\n    typeShow.value = true\r\n    disabled.value = true\r\n  }\r\n  if (route.query.id || route.query.anewId) {\r\n    typeShow.value = true\r\n    suggestionInfo()\r\n  } else {\r\n    tabCode.value = ['cppccMember']\r\n    if (user.value.specialRoleKeys.includes('team_office_user')) {\r\n      typeShow.value = true\r\n    }\r\n    if (user.value.specialRoleKeys.includes('cppcc_member')) {\r\n      typeShow.value = true\r\n      disabled.value = true\r\n      form.suggestUserId = user.value.id\r\n      cppccMemberInfo(user.value.id)\r\n    } else {\r\n      if (user.value.specialRoleKeys.includes('team_office_user')) {\r\n        form.suggestSubmitWay = 'team'\r\n      }\r\n    }\r\n    if (\r\n      user.value.specialRoleKeys.includes('team_office_user') &&\r\n      user.value.specialRoleKeys.includes('cppcc_member')\r\n    ) {\r\n      typeShow.value = false\r\n    }\r\n    if (user.value.specialRoleKeys.includes('admin')) {\r\n      form.suggestSubmitWay = 'cppcc_member'\r\n      typeShow.value = false\r\n      disabled.value = false\r\n      teamOfficeSelect({})\r\n    } else {\r\n      if (user.value.specialRoleKeys.includes('team_office_user')) {\r\n        teamOfficeSelect({ isSelectMine: 1 })\r\n      } else {\r\n        teamOfficeSelect({})\r\n      }\r\n    }\r\n    submitTypeChange()\r\n  }\r\n})\r\nonDeactivated(() => {\r\n  if (timer) {\r\n    clearTimeout(timer)\r\n    timer = null\r\n  }\r\n  qiankunMicro.setGlobalState({ AiChatCode: 'test_chat' })\r\n  qiankunMicro.setGlobalState({\r\n    AiChatConfig: {\r\n      AiChatWindow: false,\r\n      AiChatFile: [],\r\n      AiChatParams: {}\r\n    }\r\n  })\r\n  // elAiChatClass.AiChatConfig({ AiChatCode: 'test_chat', AiChatWindow: false })\r\n  // elAiChatClass.AiChatHistory()\r\n})\r\nonBeforeUnmount(() => {\r\n  if (timer) {\r\n    clearTimeout(timer)\r\n    timer = null\r\n  }\r\n  qiankunMicro.setGlobalState({ AiChatCode: 'test_chat' })\r\n  qiankunMicro.setGlobalState({\r\n    AiChatConfig: {\r\n      AiChatWindow: false,\r\n      AiChatFile: [],\r\n      AiChatParams: {}\r\n    }\r\n  })\r\n  // elAiChatClass.AiChatConfig({ AiChatCode: 'test_chat', AiChatWindow: false })\r\n  // elAiChatClass.AiChatHistory()\r\n})\r\n\r\nconst suggestionThemeSelect = async () => {\r\n  const res = await api.suggestionThemeSelect({ query: { isUsing: 1 } })\r\n  var { data } = res\r\n  SuggestBigType.value = data\r\n  SuggestBigTypeChange()\r\n}\r\n\r\nconst SuggestBigTypeChange = () => {\r\n  if (form.SuggestBigType) {\r\n    for (let index = 0; index < SuggestBigType.value.length; index++) {\r\n      const item = SuggestBigType.value[index]\r\n      if (item.id === form.SuggestBigType) {\r\n        if (!item.children.map((v) => v.id).includes(form.SuggestSmallType)) {\r\n          form.SuggestSmallType = ''\r\n        }\r\n        SuggestSmallType.value = item.children\r\n      }\r\n    }\r\n  } else {\r\n    form.SuggestSmallType = ''\r\n    SuggestSmallType.value = []\r\n  }\r\n}\r\n\r\nconst handleSimilarity = (isType) => {\r\n  if (!form.content) return ElMessage({ type: 'warning', message: '请输入提案内容进行相似度查询！' })\r\n  sessionStorage.setItem('TextQueryToolTitle', form.title)\r\n  sessionStorage.setItem('TextQueryToolContent', form.content)\r\n  isShow.value = isType\r\n  show.value = true\r\n}\r\nconst handleSimilarityCallback = (type, uflag) => {\r\n  if (uflag == 1) {\r\n    if (type) {\r\n      globalJson(0)\r\n    } else {\r\n      ElMessage({ type: 'warning', message: '您的相似度高于55%，请调整后在提交。' })\r\n    }\r\n  }\r\n  show.value = false\r\n}\r\nconst handleContentBlur = () => {\r\n  userParams.value = { authorId: form.suggestUserId, content: form.content }\r\n}\r\nconst userInitCallback = (isElIsShow, isVisibleIsShow) => {\r\n  elIsShow.value = isElIsShow\r\n  visibleIsShow.value = isVisibleIsShow\r\n}\r\nconst userSelect = (item) => {\r\n  if (!form.joinUsers.includes(item.id)) {\r\n    form.joinUsers = [...form.joinUsers, item.id]\r\n  }\r\n}\r\nconst globalReadConfig = async () => {\r\n  const { data } = await api.globalReadConfig({ codes: ['suggestTitleNumber', 'suggestContentNumber', 'suggestMinSimilar', 'suggestContentMinNumber'] })\r\n  if (data.suggestTitleNumber) {\r\n    suggestTitleNumber.value = Number(data.suggestTitleNumber)\r\n  }\r\n  if (data.suggestContentNumber) {\r\n    suggestContentNumber.value = Number(data.suggestContentNumber)\r\n  }\r\n  if (data.suggestContentMinNumber) {\r\n    suggestContentMinNumber.value = Number(data.suggestContentMinNumber)\r\n  }\r\n  if (data.suggestMinSimilar) {\r\n    suggestMinSimilar.value = Number(data.suggestMinSimilar)\r\n  }\r\n}\r\n// 获取当前届次\r\nconst termYearCurrent = async () => {\r\n  const { data } = await api.termYearCurrent({ termYearType: 'cppcc_member' })\r\n  termYearId.value = data.id\r\n}\r\nconst dictionaryData = async () => {\r\n  const { data } = await api.dictionaryData({\r\n    dictCodes: [\r\n      'suggest_open_type',\r\n      'suggest_survey_type',\r\n      'not_handle_time_type',\r\n      'is_hope_enhance_talk',\r\n      'is_make_mine_job',\r\n      'is_need_paper_answer'\r\n    ]\r\n  })\r\n  suggestOpenType.value = data.suggest_open_type\r\n  suggestSurveyType.value = data.suggest_survey_type\r\n  notHandleTimeType.value = data.not_handle_time_type\r\n  isHopeEnhanceTalk.value = data.is_hope_enhance_talk\r\n  isMakeMineJob.value = data.is_make_mine_job\r\n  isNeedPaperAnswer.value = data.is_need_paper_answer\r\n}\r\nconst dictionaryNameData = async () => {\r\n  const { data } = await api.dictionaryNameData({\r\n    dictCodes: [\r\n      'suggest_open_type',\r\n      'suggest_survey_type',\r\n      'not_handle_time_type',\r\n      'is_hope_enhance_talk',\r\n      'is_make_mine_job',\r\n      'is_need_paper_answer'\r\n    ]\r\n  })\r\n  suggestOpenTypeName.value = data.suggest_open_type\r\n  suggestSurveyTypeName.value = data.suggest_survey_type\r\n  notHandleTimeTypeName.value = data.not_handle_time_type\r\n  isHopeEnhanceTalkName.value = data.is_hope_enhance_talk\r\n  isMakeMineJobName.value = data.is_make_mine_job\r\n  isNeedPaperAnswerName.value = data.is_need_paper_answer\r\n}\r\nconst proposalClueInfo = async () => {\r\n  const { data } = await api.proposalClueInfo({ detailId: route.query.clueListId })\r\n  form.title = data.title\r\n  form.content = data.content\r\n}\r\nconst isLock = ref(false)\r\nconst lockVo = ref({})\r\nconst suggestionInfo = async () => {\r\n  try {\r\n    const res = await api.suggestionInfo({\r\n      detailId: route.query.id || route.query.anewId,\r\n      isOpenWithLock: queryType.value === 'review' ? 1 : null\r\n    })\r\n    var { data } = res\r\n    reviewShow.value = true\r\n    lockVo.value = data.lockVo\r\n    isLock.value = route.query.type == 'review' && data.lockVo.isLock == 1 && data.lockVo.lockUserId != user.value.id\r\n    form.suggestSubmitWay = data.suggestSubmitWay\r\n    if (form.suggestSubmitWay === 'cppcc_member') {\r\n      tabCode.value = ['cppccMember']\r\n      form.suggestUserId = data.suggestUserId\r\n      if (data.suggestUserId) {\r\n        cppccMemberInfo(data.suggestUserId)\r\n      }\r\n    }\r\n    if (form.suggestSubmitWay === 'team') {\r\n      isDisabled.value = true\r\n      form.delegationId = data.delegationId\r\n      delegationData.value = data.delegationId ? [{ id: data.delegationId, name: data.delegationName }] : []\r\n    }\r\n    submitTypeChange()\r\n    form.title = data.title\r\n    form.SuggestBigType = data.bigThemeId\r\n    form.SuggestSmallType = data.smallThemeId\r\n    SuggestBigTypeChange()\r\n    form.content = data.content\r\n    if (data.proposalInventoryList?.length) {\r\n      opinionsSuggestionsList.value = data.proposalInventoryList.map(v => ({ id: v.id, suggestion: v.content, explanation: v.replenish }))\r\n    }\r\n    form.bigThemeId = data.bigThemeId\r\n    form.smallThemeId = data.smallThemeId\r\n    form.james = data.james\r\n    form.jordan = data.jordan\r\n    form.kobe = data.kobe\r\n    form.duncan = data.duncan\r\n    form.wade = data.wade\r\n    handleContentBlur()\r\n    form.isJoinProposal = data.isJoinProposal\r\n    JoinChange()\r\n    form.suggestOpenType = data.suggestOpenType?.value\r\n    form.suggestSurveyType = data.suggestSurveyType?.value\r\n    form.isMakeMineJob = data.isMakeMineJob?.value\r\n    form.notHandleTimeType = data.notHandleTimeType?.value\r\n    form.isHopeEnhanceTalk = data.isHopeEnhanceTalk?.value\r\n    form.isNeedPaperAnswer = data.isNeedPaperAnswer?.value\r\n    fileData.value = data.attachments || []\r\n    form.hopeHandleOfficeIds = data.hopeHandleOfficeIds?.map((v) => v.officeId) || []\r\n    form.joinUsers = data.joinUsers?.map((v) => v.userId) || []\r\n    if (data.contacters?.length) {\r\n      contactPersonList.value = data.contacters.map((v) => ({\r\n        id: v.id,\r\n        contactName: v.contacterName,\r\n        contactPhone: v.contacterMobile,\r\n        contactAddress: v.contacterAddress\r\n      }))\r\n    }\r\n    userParams.value = { authorId: form.suggestUserId, content: form.content }\r\n  } catch (err) {\r\n    if (err.code === 500) {\r\n      if (route.query.id && queryType.value === 'review') {\r\n        reviewShow.value = false\r\n        qiankunMicro.setGlobalState({\r\n          closeOpenRoute: { openId: route.query.oldRouteId, closeId: route.query.routeId }\r\n        })\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\nconst submitTypeChange = () => {\r\n  if (form.suggestSubmitWay === 'cppcc_member') {\r\n    rules.suggestUserId = [{ required: true, message: '请选择提案者', trigger: ['blur', 'change'] }]\r\n    rules.delegationId = [{ required: false, message: '请选择集体提案单位', trigger: ['blur', 'change'] }]\r\n  } else if (form.suggestSubmitWay === 'team') {\r\n    rules.suggestUserId = [{ required: false, message: '请选择提案者', trigger: ['blur', 'change'] }]\r\n    rules.delegationId = [{ required: true, message: '请选择集体提案单位', trigger: ['blur', 'change'] }]\r\n  }\r\n}\r\nconst JoinChange = () => {\r\n  if (form.isJoinProposal) {\r\n    rules.joinUsers = [{ type: 'array', required: true, message: '请选择提案联名人', trigger: ['blur', 'change'] }]\r\n  } else {\r\n    rules.joinUsers = [{ type: 'array', required: false, message: '请选择提案联名人', trigger: ['blur', 'change'] }]\r\n  }\r\n}\r\nconst handleContentCount = (count) => {\r\n  contentCount.value = count\r\n}\r\n\r\nconst userCallback = (data) => {\r\n  if (data) {\r\n    cppccMemberInfo(data.id)\r\n    form.joinUsers = form.joinUsers.filter((v) => v !== data.id)\r\n  } else {\r\n    form.cardNumber = ''\r\n    form.sectorType = ''\r\n    form.mobile = ''\r\n    form.callAddress = ''\r\n  }\r\n  userParams.value = { authorId: form.suggestUserId, content: form.content }\r\n}\r\nconst cppccMemberInfo = async (userId) => {\r\n  const { data } = await api.cppccMemberInfo({ detailId: userId })\r\n  form.cardNumber = data.cardNumberCppcc\r\n  form.sectorType = data.sectorType?.label\r\n  form.mobile = data.mobile\r\n  form.callAddress = data.callAddress\r\n}\r\nconst teamOfficeSelect = async (params) => {\r\n  const { data } = await api.teamOfficeSelect(params)\r\n  if (data.length) {\r\n    if (user.value.specialRoleKeys.includes('team_office_user')) {\r\n      isDisabled.value = true\r\n      form.delegationId = data[0].id\r\n    }\r\n  }\r\n  delegationData.value = data\r\n}\r\nconst fileUpload = (file) => {\r\n  fileData.value = file\r\n}\r\nconst newOpinionsSuggestions = () => {\r\n  opinionsSuggestionsList.value.push({ id: guid(), suggestion: '', explanation: '' })\r\n}\r\nconst delOpinionsSuggestions = (id) => {\r\n  opinionsSuggestionsList.value = opinionsSuggestionsList.value.filter(v => v.id !== id)\r\n}\r\nconst newContactPerson = () => {\r\n  contactPersonList.value.push({ id: guid(), contactName: '', contactPhone: '', contactAddress: '' })\r\n}\r\nconst delContactPerson = (id) => {\r\n  contactPersonList.value = contactPersonList.value.filter((v) => v.id !== id)\r\n}\r\nconst submitForm = async (formEl, type, cb, _form) => {\r\n  if (!formEl) return\r\n  if (contentCount.value > suggestContentNumber.value && type == '0') {\r\n    ElMessage({ type: 'warning', message: `当前输入的提案内容超过了${suggestContentNumber.value}字，不允许提交！` })\r\n    return\r\n  }\r\n  if (contentCount.value < suggestContentMinNumber.value && route.query.utype == '1' && type == '0') {\r\n    ElMessage({ type: 'warning', message: `当前输入的提案内容少于${suggestContentMinNumber.value}字，不允许提交！` })\r\n    return\r\n  }\r\n  const hasValidSuggestion = opinionsSuggestionsList.value.some(\r\n    (v) => v.suggestion !== null && v.suggestion !== undefined && v.suggestion.trim() !== \"\"\r\n  );\r\n  if (!hasValidSuggestion) {\r\n    ElMessage({ type: 'warning', message: `意见建议清单中至少需要填写一项建议！` });\r\n    return;\r\n  }\r\n\r\n  await formEl.validate((valid, fields) => {\r\n    if (valid) {\r\n      // if (whetherUseIntelligentize.value && !cb) {\r\n      //   if (type) { globalJson(type, cb, _form) } else {\r\n      //     ElMessageBox.confirm('系统将为您进行相似度查询，是否同意执行操作？', '提示', {\r\n      //       closeOnClickModal: false,\r\n      //       confirmButtonText: '同意',\r\n      //       cancelButtonText: '跳过'\r\n      //     }).then(() => { handleSimilarity(true) }).catch(() => { globalJson(type, cb, _form) })\r\n      //   }\r\n      // } else { globalJson(type, cb, _form) }\r\n      globalJson(type, cb, _form)\r\n    } else { ElMessage({ type: 'warning', message: '请根据提示信息完善字段内容！' }) }\r\n  })\r\n}\r\nconst editCallback = (cb, _form) => {\r\n  submitForm(formRef.value, 0, cb, _form)\r\n}\r\nconst globalJson = async (type, cb, _item) => {\r\n  var formObj = {\r\n    id: route.query.id,\r\n    bigThemeId: _item ? _item.SuggestBigType : '', // 提案大类\r\n    smallThemeId: _item ? _item.SuggestSmallType : '', // 提案小类\r\n    suggestSubmitWay: form.suggestSubmitWay,\r\n    title: form.title, // 提案标题\r\n    suggestUserId: form.suggestSubmitWay === 'cppcc_member' ? form.suggestUserId : null,\r\n    delegationId: form.suggestSubmitWay === 'team' ? form.delegationId : null,\r\n    content: form.content,\r\n    isJoinProposal: form.isJoinProposal,\r\n    suggestOpenType: form.suggestOpenType,\r\n    suggestSurveyType: form.suggestSurveyType,\r\n    isMakeMineJob: form.isMakeMineJob,\r\n    notHandleTimeType: form.notHandleTimeType,\r\n    isHopeEnhanceTalk: form.isHopeEnhanceTalk,\r\n    isNeedPaperAnswer: form.isNeedPaperAnswer,\r\n    attachmentIds: fileData.value.map(v => v.id),\r\n  }\r\n  if (route.query.signId == '2') {\r\n    formObj.james = _item.reviewOpinion || ''\r\n    formObj.jordan = _item.reviewResult || ''\r\n    formObj.kobe = _item.transactType || '' // 办理方式\r\n    formObj.duncan = _item.mainHandleOfficeId.map(v => v).join(',') || '' // 主办单位\r\n    formObj.wade = _item.handleOfficeIds.map(v => v).join(',') || '' // 协办单位、分办\r\n  }\r\n  try {\r\n    const { code } = await api.globalJson(route.query.id ? '/proposal/edit' : '/proposal/add', {\r\n      form: formObj,\r\n      isSaveDraft: type,\r\n      joinUsers: form.isJoinProposal ? form.joinUsers : [],\r\n      hopeHandleOfficeIds: form.hopeHandleOfficeIds,\r\n      contacters: contactPersonList.value.filter(v => v.contactName.replace(/(^\\s*)|(\\s*$)/g, '') || v.contactPhone.replace(/(^\\s*)|(\\s*$)/g, '') || v.contactAddress.replace(/(^\\s*)|(\\s*$)/g, '')).map(v => ({ contacterName: v.contactName, contacterMobile: v.contactPhone, contacterAddress: v.contactAddress })),\r\n      proposalInventories: opinionsSuggestionsList.value.map(v => ({ content: v.suggestion, replenish: v.explanation }))\r\n    })\r\n    if (code === 200) {\r\n      if (route.query.clueListId) {\r\n        proposalClueUse()\r\n      } else {\r\n        if (cb) {\r\n          return cb()\r\n        }\r\n        ElMessage({ type: 'success', message: route.query.id ? '编辑成功' : '提交成功' })\r\n        if (route.query.id || route.query.anewId || route.query.clueListId) {\r\n          qiankunMicro.setGlobalState({\r\n            closeOpenRoute: { openId: route.query.oldRouteId, closeId: route.query.routeId }\r\n          })\r\n        } else {\r\n          qiankunMicro.setGlobalState({\r\n            openRoute: { name: '我的提案', path: '/proposal/MyLedSuggest' }\r\n          })\r\n          // store.commit('setRefreshRoute', 'SubmitSuggest')\r\n          // setTimeout(() => {\r\n          //   store.commit('setRefreshRoute', '')\r\n          // }, 222)\r\n        }\r\n      }\r\n    }\r\n  } catch (err) {\r\n    loading.value = false\r\n  }\r\n}\r\nconst proposalClueUse = async () => {\r\n  const { code } = await api.proposalClueUse({ detailId: route.query.clueListId })\r\n  if (code === 200) {\r\n    ElMessage({ type: 'success', message: '提交成功' })\r\n    qiankunMicro.setGlobalState({ closeOpenRoute: { openId: route.query.oldRouteId, closeId: route.query.routeId } })\r\n  }\r\n}\r\nconst resetForm = () => {\r\n  if (route.query.id || route.query.anewId || route.query.clueListId) {\r\n    qiankunMicro.setGlobalState({ closeOpenRoute: { openId: route.query.oldRouteId, closeId: route.query.routeId } })\r\n  } else {\r\n    store.commit('setRefreshRoute', 'SubmitSuggest')\r\n    setTimeout(() => {\r\n      store.commit('setRefreshRoute', '')\r\n    }, 222)\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.SubmitSuggest {\r\n  width: 100%;\r\n  height: 100%;\r\n\r\n  .SubmitSuggestBody {\r\n    width: 990px;\r\n    margin: 20px auto;\r\n    background-color: #fff;\r\n    box-shadow: 0px 3px 15px 1px rgba(0, 0, 0, 0.16);\r\n\r\n    .SubmitSuggestNameBody {\r\n      padding: var(--zy-distance-one);\r\n      padding-bottom: 0;\r\n\r\n      .global-dynamic-title {\r\n        border-bottom: 3px solid var(--zy-el-color-primary);\r\n      }\r\n    }\r\n\r\n    .globalPaperForm {\r\n      width: 100%;\r\n      padding: var(--zy-distance-one);\r\n      padding-top: 0;\r\n\r\n      .zy-el-form-item {\r\n        width: 50%;\r\n        margin: 0;\r\n        border-bottom: 1px solid var(--zy-el-color-primary);\r\n\r\n        .zy-el-form-item__label {\r\n          width: 138px;\r\n          justify-content: center;\r\n        }\r\n\r\n        .zy-el-form-item__content {\r\n          border-left: 1px solid var(--zy-el-color-primary);\r\n          border-right: 1px solid transparent;\r\n\r\n          &>.simple-select-person {\r\n            box-shadow: 0 0 0 0 !important;\r\n          }\r\n\r\n          &>.zy-el-input,\r\n          .zy-el-input-number {\r\n            width: 100%;\r\n\r\n            .zy-el-input__wrapper {\r\n              box-shadow: 0 0 0 0 !important;\r\n            }\r\n          }\r\n\r\n          &>.zy-el-select,\r\n          .zy-el-select-v2 {\r\n            .zy-el-select__wrapper {\r\n              box-shadow: 0 0 0 0 !important;\r\n            }\r\n          }\r\n\r\n          &>.zy-el-radio-group {\r\n            padding-left: 15px;\r\n          }\r\n\r\n          &>.zy-el-date-editor {\r\n            width: 100%;\r\n\r\n            &>.zy-el-input__wrapper {\r\n              width: 100%;\r\n              box-shadow: 0 0 0 0 !important;\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      .SubmitSuggestLeft {\r\n        .zy-el-form-item__content {\r\n          border-right-color: var(--zy-el-color-primary);\r\n        }\r\n      }\r\n\r\n      .SubmitSuggestTitle {\r\n        width: 100%;\r\n\r\n        .zy-el-form-item__content {\r\n          border-right-color: transparent;\r\n        }\r\n      }\r\n\r\n      .SubmitSuggestButton {\r\n        .zy-el-form-item__content {\r\n          flex-wrap: nowrap;\r\n          justify-content: space-between;\r\n\r\n          .SubmitSuggestContentNumber {\r\n            padding: 0 10px;\r\n            color: var(--zy-el-color-error);\r\n            font-size: var(--zy-text-font-size);\r\n            line-height: var(--zy-line-height);\r\n          }\r\n\r\n          .SubmitSuggestUpload {\r\n            margin-left: 12px;\r\n            margin-right: 12px;\r\n          }\r\n\r\n          .zy-el-button {\r\n            --zy-el-button-size: var(--zy-height-routine);\r\n          }\r\n        }\r\n      }\r\n\r\n      .TinyMceEditor {\r\n        border-bottom: 1px solid var(--zy-el-color-primary);\r\n      }\r\n\r\n      .opinionsSuggestionsList {\r\n        width: 100%;\r\n\r\n        .opinionsSuggestionsListHead,\r\n        .opinionsSuggestionsListBody {\r\n          width: 100%;\r\n          display: flex;\r\n        }\r\n\r\n        .opinionsSuggestionsListBody {\r\n          border-top: 1px solid var(--zy-el-color-primary);\r\n        }\r\n\r\n        .row1 {\r\n          flex: 1;\r\n        }\r\n\r\n        .row2 {\r\n          flex: 2;\r\n        }\r\n\r\n        .row3 {\r\n          flex: 3;\r\n        }\r\n\r\n        .opinionsSuggestionsListItem {\r\n          height: 40px;\r\n          line-height: 40px;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n\r\n          &>.zy-el-input {\r\n            width: 100%;\r\n\r\n            .zy-el-input__wrapper {\r\n              box-shadow: 0 0 0 0 !important;\r\n            }\r\n          }\r\n\r\n          .zy-el-link {\r\n            font-size: 18px;\r\n            line-height: 24px;\r\n          }\r\n\r\n          .zy-el-link+.zy-el-link {\r\n            margin-left: 12px;\r\n          }\r\n        }\r\n\r\n        .opinionsSuggestionsListItem+.opinionsSuggestionsListItem {\r\n          border-left: 1px solid var(--zy-el-color-primary);\r\n        }\r\n      }\r\n\r\n      .SubmitSuggestFormUpload {\r\n        width: 100%;\r\n\r\n        .zy-el-form-item__content {\r\n          padding: 15px;\r\n          border-right-color: transparent;\r\n\r\n          .SubmitSuggestFormInfo {\r\n            width: 100%;\r\n            display: flex;\r\n          }\r\n        }\r\n      }\r\n\r\n      .SubmitSuggestFormItem {\r\n        width: 100%;\r\n\r\n        .zy-el-form-item__content {\r\n          padding: 0 15px;\r\n          border-right-color: transparent;\r\n\r\n          .SubmitSuggestFormInfo {\r\n            width: 100%;\r\n            display: flex;\r\n          }\r\n        }\r\n      }\r\n\r\n      .SubmitSuggestContactPerson {\r\n        width: 100%;\r\n\r\n        .SubmitSuggestContactPersonHead,\r\n        .SubmitSuggestContactPersonBody {\r\n          width: 100%;\r\n          display: flex;\r\n        }\r\n\r\n        .SubmitSuggestContactPersonBody {\r\n          border-top: 1px solid var(--zy-el-color-primary);\r\n        }\r\n\r\n        .row1 {\r\n          flex: 1;\r\n        }\r\n\r\n        .row2 {\r\n          flex: 2;\r\n        }\r\n\r\n        .row3 {\r\n          flex: 3;\r\n        }\r\n\r\n        .SubmitSuggestContactPersonItem {\r\n          height: 40px;\r\n          line-height: 40px;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n\r\n          &>.zy-el-input {\r\n            width: 100%;\r\n\r\n            .zy-el-input__wrapper {\r\n              box-shadow: 0 0 0 0 !important;\r\n            }\r\n          }\r\n\r\n          .zy-el-link {\r\n            font-size: 18px;\r\n            line-height: 24px;\r\n          }\r\n\r\n          .zy-el-link+.zy-el-link {\r\n            margin-left: 12px;\r\n          }\r\n        }\r\n\r\n        .SubmitSuggestContactPersonItem+.SubmitSuggestContactPersonItem {\r\n          border-left: 1px solid var(--zy-el-color-primary);\r\n        }\r\n      }\r\n\r\n      .globalPaperFormButton {\r\n        width: 100%;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        padding-top: 22px;\r\n\r\n        .zy-el-button+.zy-el-button {\r\n          margin-left: var(--zy-distance-two);\r\n        }\r\n      }\r\n    }\r\n\r\n    .SuggestSegmentation {\r\n      width: 100%;\r\n      height: 10px;\r\n      background-color: var(--zy-el-color-info-light-9);\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EAESA,KAAK,EAAC;AAAmB;;EACvBA,KAAK,EAAC;AAAuB;;EAoEvBA,KAAK,EAAC;AAAkC;;EAIxCA,KAAK,EAAC;AAAkC;;EAIxCA,KAAK,EAAC;AAAkC;;EA/EzDC,GAAA;EAoGeD,KAAK,EAAC;;;EACJA,KAAK,EAAC;AAA2B;;EArGlDC,GAAA;EA0GeD,KAAK,EAAC;;;EACJA,KAAK,EAAC;AAA2B;;EA3GlDC,GAAA;EAgHeD,KAAK,EAAC;;;EACJA,KAAK,EAAC;AAA2B;;EAjHlDC,GAAA;EAsHeD,KAAK,EAAC;;;EACJA,KAAK,EAAC;AAA2B;;EAvHlDC,GAAA;EA4HeD,KAAK,EAAC;;;EACJA,KAAK,EAAC;AAA2B;;EA7HlDC,GAAA;EAkIeD,KAAK,EAAC;;;EACJA,KAAK,EAAC;AAA2B;;EAiBjCA,KAAK,EAAC;AAAqC;;EAG3CA,KAAK,EAAC;AAAqC;;EAG3CA,KAAK,EAAC;AAAqC;;EAG3CA,KAAK,EAAC;AAAqC;;EA7J5DC,GAAA;EA2KaD,KAAK,EAAC;;;EA3KnBC,GAAA;EAqLyCD,KAAK,EAAC;;;;;;;;;;;;;;;;;;;;;;;;;wCApL7CE,YAAA,CAkMeC,uBAAA;IAlMDC,MAAM,EAAN,EAAM;IAACJ,KAAK,EAAC,eAAe;IAAsB,qBAAmB,EAAEK,MAAA,CAAAC;;IADvFC,OAAA,EAAAC,QAAA,CAEI;MAAA,OA4LM,CA5LNC,mBAAA,CA4LM,OA5LNC,UA4LM,GA3LJD,mBAAA,CAEM,OAFNE,UAEM,GADJC,YAAA,CAA2EC,+BAAA;QAArDC,YAAY,EAAC;MAAgB,G,GAErDF,YAAA,CA8KUG,kBAAA;QA9KDC,GAAG,EAAC,SAAS;QAAEC,KAAK,EAAEZ,MAAA,CAAAa,IAAI;QAAGC,KAAK,EAAEd,MAAA,CAAAc,KAAK;QAAEC,MAAM,EAAN,EAAM;QAAE,cAAY,EAAE,KAAK;QAAEpB,KAAK,EAAC;;QAN7FO,OAAA,EAAAC,QAAA,CAOQ;UAAA,OAKe,C,CALqBH,MAAA,CAAAgB,QAAQ,I,cAA5CnB,YAAA,CAKeoB,uBAAA;YAZvBrB,GAAA;YAOsBsB,KAAK,EAAC,QAAQ;YAAkBC,IAAI,EAAC,kBAAkB;YAACxB,KAAK,EAAC;;YAPpFO,OAAA,EAAAC,QAAA,CAQU;cAAA,OAGiB,CAHjBI,YAAA,CAGiBa,yBAAA;gBAX3BC,UAAA,EAQmCrB,MAAA,CAAAa,IAAI,CAACS,gBAAgB;gBARxD,uBAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;kBAAA,OAQmCxB,MAAA,CAAAa,IAAI,CAACS,gBAAgB,GAAAE,MAAA;gBAAA;gBAAGC,QAAM,EAAEzB,MAAA,CAAA0B;;gBARnExB,OAAA,EAAAC,QAAA,CASY;kBAAA,OAA8C,CAA9CI,YAAA,CAA8CoB,mBAAA;oBAApCT,KAAK,EAAC;kBAAc;oBAT1ChB,OAAA,EAAAC,QAAA,CAS2C;sBAAA,OAAIoB,MAAA,SAAAA,MAAA,QAT/CK,gBAAA,CAS2C,MAAI,E;;oBAT/CC,CAAA;sBAUYtB,YAAA,CAAsCoB,mBAAA;oBAA5BT,KAAK,EAAC;kBAAM;oBAVlChB,OAAA,EAAAC,QAAA,CAUmC;sBAAA,OAAIoB,MAAA,SAAAA,MAAA,QAVvCK,gBAAA,CAUmC,MAAI,E;;oBAVvCC,CAAA;;;gBAAAA,CAAA;;;YAAAA,CAAA;gBAAAC,mBAAA,gBAaQvB,YAAA,CAGeU,uBAAA;YAHDC,KAAK,EAAC,MAAM;YAACC,IAAI,EAAC,OAAO;YAACxB,KAAK,EAAC;;YAbtDO,OAAA,EAAAC,QAAA,CAcU;cAAA,OACc,CADdI,YAAA,CACcwB,mBAAA;gBAfxBV,UAAA,EAc6BrB,MAAA,CAAAa,IAAI,CAACmB,KAAK;gBAdvC,uBAAAT,MAAA,QAAAA,MAAA,gBAAAC,MAAA;kBAAA,OAc6BxB,MAAA,CAAAa,IAAI,CAACmB,KAAK,GAAAR,MAAA;gBAAA;gBAAES,WAAW,EAAC,SAAS;gBAAC,iBAAe,EAAf,EAAe;gBAAEC,SAAS,EAAElC,MAAA,CAAAmC,kBAAkB;gBACjGC,SAAS,EAAT;;;YAfZP,CAAA;8BAiBQtB,YAAA,CAIeU,uBAAA;YAJiDC,KAAK,EAAC,KAAK;YAACC,IAAI,EAAC,eAAe;YAC9FxB,KAAK,EAAC;;YAlBhBO,OAAA,EAAAC,QAAA,CAmBU;cAAA,OAC6B,CAD7BI,YAAA,CAC6B8B,8BAAA;gBApBvChB,UAAA,EAmBwCrB,MAAA,CAAAa,IAAI,CAACyB,aAAa;gBAnB1D,uBAAAf,MAAA,QAAAA,MAAA,gBAAAC,MAAA;kBAAA,OAmBwCxB,MAAA,CAAAa,IAAI,CAACyB,aAAa,GAAAd,MAAA;gBAAA;gBAAES,WAAW,EAAC,QAAQ;gBAAEM,QAAQ,EAAEvC,MAAA,CAAAuC,QAAQ;gBAAGC,OAAO,EAAExC,MAAA,CAAAwC,OAAO;gBAC1GC,UAAQ,EAAEzC,MAAA,CAAA0C;;;YApBvBb,CAAA;8CAiB8B7B,MAAA,CAAAa,IAAI,CAACS,gBAAgB,qB,mBAK3Cf,YAAA,CAEeU,uBAAA;YAFiDC,KAAK,EAAC;UAAM;YAtBpFhB,OAAA,EAAAC,QAAA,CAuBU;cAAA,OAA+C,CAA/CI,YAAA,CAA+CwB,mBAAA;gBAvBzDV,UAAA,EAuB6BrB,MAAA,CAAAa,IAAI,CAAC8B,UAAU;gBAvB5C,uBAAApB,MAAA,QAAAA,MAAA,gBAAAC,MAAA;kBAAA,OAuB6BxB,MAAA,CAAAa,IAAI,CAAC8B,UAAU,GAAAnB,MAAA;gBAAA;gBAAEe,QAAQ,EAAR;;;YAvB9CV,CAAA;8CAsB8B7B,MAAA,CAAAa,IAAI,CAACS,gBAAgB,qB,mBAG3Cf,YAAA,CAEeU,uBAAA;YAFiDC,KAAK,EAAC,IAAI;YAACvB,KAAK,EAAC;;YAzBzFO,OAAA,EAAAC,QAAA,CA0BU;cAAA,OAA+C,CAA/CI,YAAA,CAA+CwB,mBAAA;gBA1BzDV,UAAA,EA0B6BrB,MAAA,CAAAa,IAAI,CAAC+B,UAAU;gBA1B5C,uBAAArB,MAAA,QAAAA,MAAA,gBAAAC,MAAA;kBAAA,OA0B6BxB,MAAA,CAAAa,IAAI,CAAC+B,UAAU,GAAApB,MAAA;gBAAA;gBAAEe,QAAQ,EAAR;;;YA1B9CV,CAAA;8CAyB8B7B,MAAA,CAAAa,IAAI,CAACS,gBAAgB,qB,mBAG3Cf,YAAA,CAEeU,uBAAA;YAFiDC,KAAK,EAAC;UAAM;YA5BpFhB,OAAA,EAAAC,QAAA,CA6BU;cAAA,OAA2C,CAA3CI,YAAA,CAA2CwB,mBAAA;gBA7BrDV,UAAA,EA6B6BrB,MAAA,CAAAa,IAAI,CAACgC,MAAM;gBA7BxC,uBAAAtB,MAAA,QAAAA,MAAA,gBAAAC,MAAA;kBAAA,OA6B6BxB,MAAA,CAAAa,IAAI,CAACgC,MAAM,GAAArB,MAAA;gBAAA;gBAAEe,QAAQ,EAAR;;;YA7B1CV,CAAA;8CA4B8B7B,MAAA,CAAAa,IAAI,CAACS,gBAAgB,qB,mBAG3Cf,YAAA,CAEeU,uBAAA;YAFiDC,KAAK,EAAC,MAAM;YAACvB,KAAK,EAAC;;YA/B3FO,OAAA,EAAAC,QAAA,CAgCU;cAAA,OAAgD,CAAhDI,YAAA,CAAgDwB,mBAAA;gBAhC1DV,UAAA,EAgC6BrB,MAAA,CAAAa,IAAI,CAACiC,WAAW;gBAhC7C,uBAAAvB,MAAA,QAAAA,MAAA,gBAAAC,MAAA;kBAAA,OAgC6BxB,MAAA,CAAAa,IAAI,CAACiC,WAAW,GAAAtB,MAAA;gBAAA;gBAAEe,QAAQ,EAAR;;;YAhC/CV,CAAA;8CA+B8B7B,MAAA,CAAAa,IAAI,CAACS,gBAAgB,qB,mBAG3Cf,YAAA,CAKeU,uBAAA;YALyCC,KAAK,EAAC,KAAK;YAACC,IAAI,EAAC,cAAc;YACrFxB,KAAK,EAAC;;YAnChBO,OAAA,EAAAC,QAAA,CAoCU;cAAA,OAEY,CAFZI,YAAA,CAEYwC,oBAAA;gBAtCtB1B,UAAA,EAoC8BrB,MAAA,CAAAa,IAAI,CAACmC,YAAY;gBApC/C,uBAAAzB,MAAA,QAAAA,MAAA,gBAAAC,MAAA;kBAAA,OAoC8BxB,MAAA,CAAAa,IAAI,CAACmC,YAAY,GAAAxB,MAAA;gBAAA;gBAAGe,QAAQ,EAAEvC,MAAA,CAAAiD,UAAU;gBAAEhB,WAAW,EAAC,WAAW;gBAACG,SAAS,EAAT;;gBApChGlC,OAAA,EAAAC,QAAA,CAqCuB;kBAAA,OAA8B,E,kBAAzC+C,mBAAA,CAA+FC,SAAA,QArC3GC,WAAA,CAqCsCpD,MAAA,CAAAqD,cAAc,EArCpD,UAqC8BC,IAAI;yCAAtBzD,YAAA,CAA+F0D,oBAAA;sBAApD3D,GAAG,EAAE0D,IAAI,CAACE,EAAE;sBAAGtC,KAAK,EAAEoC,IAAI,CAACG,IAAI;sBAAGC,KAAK,EAAEJ,IAAI,CAACE;;;;gBArCrG3B,CAAA;;;YAAAA,CAAA;8CAkC8B7B,MAAA,CAAAa,IAAI,CAACS,gBAAgB,a,GAM3Cf,YAAA,CAKeU,uBAAA;YALDC,KAAK,EAAC,QAAQ;YAACC,IAAI,EAAC,gBAAgB;YAACxB,KAAK,EAAC;;YAxCjEO,OAAA,EAAAC,QAAA,CAyCU;cAAA,OAGiB,CAHjBI,YAAA,CAGiBa,yBAAA;gBA5C3BC,UAAA,EAyCmCrB,MAAA,CAAAa,IAAI,CAAC8C,cAAc;gBAzCtD,uBAAApC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;kBAAA,OAyCmCxB,MAAA,CAAAa,IAAI,CAAC8C,cAAc,GAAAnC,MAAA;gBAAA;gBAAGC,QAAM,EAAEzB,MAAA,CAAA4D;;gBAzCjE1D,OAAA,EAAAC,QAAA,CA0CY;kBAAA,OAAiC,CAAjCI,YAAA,CAAiCoB,mBAAA;oBAAtBT,KAAK,EAAE;kBAAC;oBA1C/BhB,OAAA,EAAAC,QAAA,CA0CiC;sBAAA,OAACoB,MAAA,SAAAA,MAAA,QA1ClCK,gBAAA,CA0CiC,GAAC,E;;oBA1ClCC,CAAA;sBA2CYtB,YAAA,CAAiCoB,mBAAA;oBAAtBT,KAAK,EAAE;kBAAC;oBA3C/BhB,OAAA,EAAAC,QAAA,CA2CiC;sBAAA,OAACoB,MAAA,SAAAA,MAAA,QA3ClCK,gBAAA,CA2CiC,GAAC,E;;oBA3ClCC,CAAA;;;gBAAAA,CAAA;;;YAAAA,CAAA;cA8C2D7B,MAAA,CAAAa,IAAI,CAAC8C,cAAc,I,cAAtE9D,YAAA,CAUeoB,uBAAA;YAxDvBrB,GAAA;YA8CsBsB,KAAK,EAAC,OAAO;YAACC,IAAI,EAAC,WAAW;YAA4BxB,KAAK,EAAC;;YA9CtFO,OAAA,EAAAC,QAAA,CA+CU;cAAA,OAEoD,CAFpDI,YAAA,CAEoDsD,+BAAA;gBAjD9DxC,UAAA,EA+CyCrB,MAAA,CAAAa,IAAI,CAACiD,SAAS;gBA/CvD,uBAAAvC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;kBAAA,OA+CyCxB,MAAA,CAAAa,IAAI,CAACiD,SAAS,GAAAtC,MAAA;gBAAA;gBAAES,WAAW,EAAC,UAAU;gBAClE8B,UAAU,EAAE/D,MAAA,CAAAa,IAAI,CAACyB,aAAa,IAAItC,MAAA,CAAAa,IAAI,CAACyB,aAAa;gBACpDE,OAAO,EAAE;qEACIxC,MAAA,CAAAgE,wBAAwB,IAAIhE,MAAA,CAAAiE,SAAS,iB,cACnDpE,YAAA,CAGwBqE,gCAAA;gBAtDpCtE,GAAA;gBAmD2CuE,QAAQ,EAAEnE,MAAA,CAAAmE,QAAQ;gBAnD7D,qBAAA5C,MAAA,SAAAA,MAAA,iBAAAC,MAAA;kBAAA,OAmDqDxB,MAAA,CAAAmE,QAAQ,GAAA3C,MAAA;gBAAA;gBAnD7DH,UAAA,EAmDwErB,MAAA,CAAAoE,aAAa;gBAnDrF,uBAAA7C,MAAA,SAAAA,MAAA,iBAAAC,MAAA;kBAAA,OAmDwExB,MAAA,CAAAoE,aAAa,GAAA5C,MAAA;gBAAA;;gBAnDrFtB,OAAA,EAAAC,QAAA,CAoDc;kBAAA,OACuB,CADvBI,YAAA,CACuBP,MAAA;oBADAqE,MAAM,EAAErE,MAAA,CAAAsE,UAAU;oBAAG7B,UAAQ,EAAEzC,MAAA,CAAAuE,gBAAgB;oBAAGC,QAAM,EAAExE,MAAA,CAAAyE;;;gBApD/F5C,CAAA;+DAAAC,mBAAA,e;;YAAAD,CAAA;gBAAAC,mBAAA,gBAyDQvB,YAAA,CAGeU,uBAAA;YAHDC,KAAK,EAAC,MAAM;YAACC,IAAI,EAAC,SAAS;YAACxB,KAAK,EAAC;;YAzDxDO,OAAA,EAAAC,QAAA,CA0DU;cAAA,OAC0G,CAAlGH,MAAA,CAAAgE,wBAAwB,IAAIhE,MAAA,CAAAiE,SAAS,iBAAiBjE,MAAA,CAAA0E,UAAU,I,cADxE7E,YAAA,CAC0G8E,oBAAA;gBA3DpH/E,GAAA;gBA0DsBgF,OAAK,EAAArD,MAAA,SAAAA,MAAA,iBAAAC,MAAA;kBAAA,OAAExB,MAAA,CAAA6E,gBAAgB;gBAAA;gBACuCC,IAAI,EAAC;;gBA3DzF5E,OAAA,EAAAC,QAAA,CA2DmG;kBAAA,OAAKoB,MAAA,SAAAA,MAAA,QA3DxGK,gBAAA,CA2DmG,OAAK,E;;gBA3DxGC,CAAA;oBAAAC,mBAAA,e;;YAAAD,CAAA;cA6DQtB,YAAA,CAC0CwE,wBAAA;YA9DlD1D,UAAA,EA6DgCrB,MAAA,CAAAa,IAAI,CAACmE,OAAO;YA7D5C,uBAAAzD,MAAA,SAAAA,MAAA,iBAAAC,MAAA;cAAA,OA6DgCxB,MAAA,CAAAa,IAAI,CAACmE,OAAO,GAAAxD,MAAA;YAAA;YAAGyD,OAAO,EAAEjF,MAAA,CAAAkF,cAAc;YAAGC,OAAK,EAAEnF,MAAA,CAAAoF,kBAAkB;YACvFC,MAAI,EAAErF,MAAA,CAAAsF,iBAAiB;YAAEC,WAAW,EAAX;mDAC5BhF,YAAA,CA+BeU,uBAAA;YA/BDtB,KAAK,EAAC,yBAAyB;YAACuB,KAAK,EAAC;;YA/D5DhB,OAAA,EAAAC,QAAA,CAgEU;cAAA,OAAyE,C,4BAAzEC,mBAAA,CAAyE;gBAAnEoF,KAA0D,EAA1D;kBAAA;kBAAA;kBAAA;kBAAA;gBAAA;cAA0D,GAAC,GAAC,sB,4BAClEpF,mBAAA,CAIM;gBAJDT,KAAK,EAAC;cAA6B,IACtCS,mBAAA,CAAsD;gBAAjDT,KAAK,EAAC;cAAkC,GAAC,IAAE,GAChDS,mBAAA,CAAwD;gBAAnDT,KAAK,EAAC;cAAkC,GAAC,MAAI,GAClDS,mBAAA,CAAsD;gBAAjDT,KAAK,EAAC;cAAkC,GAAC,IAAE,E,yCAElDuD,mBAAA,CAqBMC,SAAA,QA3FhBC,WAAA,CAsEkEpD,MAAA,CAAAyF,uBAAuB,EAtEzF,UAsE0DnC,IAAI;qCAApDJ,mBAAA,CAqBM;kBArBDvD,KAAK,EAAC,6BAA6B;kBAA0CC,GAAG,EAAE0D,IAAI,CAACE;oBAC1FpD,mBAAA,CAGM,OAHNsF,UAGM,GAFJnF,YAAA,CACWwB,mBAAA;kBADDE,WAAW,EAAC,OAAO;kBAxE3CZ,UAAA,EAwEqDiC,IAAI,CAACqC,UAAU;kBAxEpE,gCAAAC,mBAAApE,MAAA;oBAAA,OAwEqD8B,IAAI,CAACqC,UAAU,GAAAnE,MAAA;kBAAA;kBAAEY,SAAS,EAAT,EAAS;kBAACF,SAAS,EAAC,IAAI;kBAAC,iBAAe,EAAf;kFAGnF9B,mBAAA,CAGM,OAHNyF,UAGM,GAFJtF,YAAA,CACWwB,mBAAA;kBADDE,WAAW,EAAC,SAAS;kBA5E7CZ,UAAA,EA4EuDiC,IAAI,CAACwC,WAAW;kBA5EvE,gCAAAF,mBAAApE,MAAA;oBAAA,OA4EuD8B,IAAI,CAACwC,WAAW,GAAAtE,MAAA;kBAAA;kBAAEY,SAAS,EAAT;kFAG7DhC,mBAAA,CAWM,OAXN2F,UAWM,GAV2C/F,MAAA,CAAAyF,uBAAuB,CAACO,MAAM,I,cAA7EnG,YAAA,CAIUoG,kBAAA;kBApFxBrG,GAAA;kBAgFwBgF,OAAK,EAAE5E,MAAA,CAAAkG;;kBAhF/BhG,OAAA,EAAAC,QAAA,CAiFgB;oBAAA,OAEU,CAFVI,YAAA,CAEU4F,kBAAA;sBAnF1BjG,OAAA,EAAAC,QAAA,CAkFkB;wBAAA,OAAc,CAAdI,YAAA,CAAc6F,qBAAA,E;;sBAlFhCvE,CAAA;;;kBAAAA,CAAA;sBAAAC,mBAAA,gBAqF6B9B,MAAA,CAAAyF,uBAAuB,CAACO,MAAM,Q,cAA7CnG,YAAA,CAIUoG,kBAAA;kBAzFxBrG,GAAA;kBAqFkEgF,OAAK,WAALA,OAAKA,CAAApD,MAAA;oBAAA,OAAExB,MAAA,CAAAqG,sBAAsB,CAAC/C,IAAI,CAACE,EAAE;kBAAA;;kBArFvGtD,OAAA,EAAAC,QAAA,CAsFgB;oBAAA,OAEU,CAFVI,YAAA,CAEU4F,kBAAA;sBAxF1BjG,OAAA,EAAAC,QAAA,CAuFkB;wBAAA,OAAU,CAAVI,YAAA,CAAU+F,iBAAA,E;;sBAvF5BzE,CAAA;;;kBAAAA,CAAA;oEAAAC,mBAAA,e;0EA4FU1B,mBAAA,CAC6C;gBADxCoF,KAAgF,EAAhF;kBAAA;kBAAA;kBAAA;kBAAA;gBAAA;cAAgF,GAAC,wCAC/C,qB;;YA7FjD3D,CAAA;cA+FQtB,YAAA,CAGeU,uBAAA;YAHDC,KAAK,EAAC,MAAM;YAACvB,KAAK,EAAC;;YA/FzCO,OAAA,EAAAC,QAAA,CAgGU;cAAA,OACsL,CADtLI,YAAA,CACsLgG,0BAAA;gBADpKC,QAAQ,EAAExG,MAAA,CAAAwG,QAAQ;gBAAGC,YAAU,EAAEzG,MAAA,CAAA0G,UAAU;gBAC1DC,QAAQ,EAAE;;;YAjGvB9E,CAAA;cAmGQtB,YAAA,CAqCeU,uBAAA;YArCDC,KAAK,EAAC,QAAQ;YAACvB,KAAK,EAAC;;YAnG3CO,OAAA,EAAAC,QAAA,CAoGU;cAAA,OAKM,CALmCH,MAAA,CAAA4G,mBAAmB,I,cAA5D1D,mBAAA,CAKM,OALN2D,UAKM,GAJJzG,mBAAA,CAAuE,OAAvE0G,UAAuE,EAAAC,gBAAA,CAA7B/G,MAAA,CAAA4G,mBAAmB,IAAG,GAAC,iBACjErG,YAAA,CAEiBa,yBAAA;gBAxG7BC,UAAA,EAsGqCrB,MAAA,CAAAa,IAAI,CAACmG,eAAe;gBAtGzD,uBAAAzF,MAAA,SAAAA,MAAA,iBAAAC,MAAA;kBAAA,OAsGqCxB,MAAA,CAAAa,IAAI,CAACmG,eAAe,GAAAxF,MAAA;gBAAA;;gBAtGzDtB,OAAA,EAAAC,QAAA,CAuGwB;kBAAA,OAA+B,E,kBAAzC+C,mBAAA,CAAsGC,SAAA,QAvGpHC,WAAA,CAuGuCpD,MAAA,CAAAgH,eAAe,EAvGtD,UAuG+B1D,IAAI;yCAArBzD,YAAA,CAAsG8B,mBAAA;sBAA3D/B,GAAG,EAAE0D,IAAI,CAAC1D,GAAG;sBAAGsB,KAAK,EAAEoC,IAAI,CAAC1D;;sBAvGrFM,OAAA,EAAAC,QAAA,CAuG0F;wBAAA,OAAe,CAvGzGyB,gBAAA,CAAAmF,gBAAA,CAuG6FzD,IAAI,CAACG,IAAI,iB;;sBAvGtG5B,CAAA;;;;gBAAAA,CAAA;qDAAAC,mBAAA,gBA0GmD9B,MAAA,CAAAiH,qBAAqB,I,cAA9D/D,mBAAA,CAKM,OALNgE,UAKM,GAJJ9G,mBAAA,CAAyE,OAAzE+G,UAAyE,EAAAJ,gBAAA,CAA/B/G,MAAA,CAAAiH,qBAAqB,IAAG,GAAC,iBACnE1G,YAAA,CAEiBa,yBAAA;gBA9G7BC,UAAA,EA4GqCrB,MAAA,CAAAa,IAAI,CAACuG,iBAAiB;gBA5G3D,uBAAA7F,MAAA,SAAAA,MAAA,iBAAAC,MAAA;kBAAA,OA4GqCxB,MAAA,CAAAa,IAAI,CAACuG,iBAAiB,GAAA5F,MAAA;gBAAA;;gBA5G3DtB,OAAA,EAAAC,QAAA,CA6GwB;kBAAA,OAAiC,E,kBAA3C+C,mBAAA,CAAwGC,SAAA,QA7GtHC,WAAA,CA6GuCpD,MAAA,CAAAoH,iBAAiB,EA7GxD,UA6G+B9D,IAAI;yCAArBzD,YAAA,CAAwG8B,mBAAA;sBAA3D/B,GAAG,EAAE0D,IAAI,CAAC1D,GAAG;sBAAGsB,KAAK,EAAEoC,IAAI,CAAC1D;;sBA7GvFM,OAAA,EAAAC,QAAA,CA6G4F;wBAAA,OAAe,CA7G3GyB,gBAAA,CAAAmF,gBAAA,CA6G+FzD,IAAI,CAACG,IAAI,iB;;sBA7GxG5B,CAAA;;;;gBAAAA,CAAA;qDAAAC,mBAAA,gBAgHmD9B,MAAA,CAAAqH,iBAAiB,I,cAA1DnE,mBAAA,CAKM,OALNoE,WAKM,GAJJlH,mBAAA,CAAqE,OAArEmH,WAAqE,EAAAR,gBAAA,CAA3B/G,MAAA,CAAAqH,iBAAiB,IAAG,GAAC,iBAC/D9G,YAAA,CAEiBa,yBAAA;gBApH7BC,UAAA,EAkHqCrB,MAAA,CAAAa,IAAI,CAAC2G,aAAa;gBAlHvD,uBAAAjG,MAAA,SAAAA,MAAA,iBAAAC,MAAA;kBAAA,OAkHqCxB,MAAA,CAAAa,IAAI,CAAC2G,aAAa,GAAAhG,MAAA;gBAAA;;gBAlHvDtB,OAAA,EAAAC,QAAA,CAmHwB;kBAAA,OAA6B,E,kBAAvC+C,mBAAA,CAAoGC,SAAA,QAnHlHC,WAAA,CAmHuCpD,MAAA,CAAAwH,aAAa,EAnHpD,UAmH+BlE,IAAI;yCAArBzD,YAAA,CAAoG8B,mBAAA;sBAA3D/B,GAAG,EAAE0D,IAAI,CAAC1D,GAAG;sBAAGsB,KAAK,EAAEoC,IAAI,CAAC1D;;sBAnHnFM,OAAA,EAAAC,QAAA,CAmHwF;wBAAA,OAAe,CAnHvGyB,gBAAA,CAAAmF,gBAAA,CAmH2FzD,IAAI,CAACG,IAAI,iB;;sBAnHpG5B,CAAA;;;;gBAAAA,CAAA;qDAAAC,mBAAA,gBAsHmD9B,MAAA,CAAAyH,qBAAqB,I,cAA9DvE,mBAAA,CAKM,OALNwE,WAKM,GAJJtH,mBAAA,CAAyE,OAAzEuH,WAAyE,EAAAZ,gBAAA,CAA/B/G,MAAA,CAAAyH,qBAAqB,IAAG,GAAC,iBACnElH,YAAA,CAEiBa,yBAAA;gBA1H7BC,UAAA,EAwHqCrB,MAAA,CAAAa,IAAI,CAAC+G,iBAAiB;gBAxH3D,uBAAArG,MAAA,SAAAA,MAAA,iBAAAC,MAAA;kBAAA,OAwHqCxB,MAAA,CAAAa,IAAI,CAAC+G,iBAAiB,GAAApG,MAAA;gBAAA;;gBAxH3DtB,OAAA,EAAAC,QAAA,CAyHwB;kBAAA,OAAiC,E,kBAA3C+C,mBAAA,CAAwGC,SAAA,QAzHtHC,WAAA,CAyHuCpD,MAAA,CAAA4H,iBAAiB,EAzHxD,UAyH+BtE,IAAI;yCAArBzD,YAAA,CAAwG8B,mBAAA;sBAA3D/B,GAAG,EAAE0D,IAAI,CAAC1D,GAAG;sBAAGsB,KAAK,EAAEoC,IAAI,CAAC1D;;sBAzHvFM,OAAA,EAAAC,QAAA,CAyH4F;wBAAA,OAAe,CAzH3GyB,gBAAA,CAAAmF,gBAAA,CAyH+FzD,IAAI,CAACG,IAAI,iB;;sBAzHxG5B,CAAA;;;;gBAAAA,CAAA;qDAAAC,mBAAA,gBA4HmD9B,MAAA,CAAA6H,qBAAqB,I,cAA9D3E,mBAAA,CAKM,OALN4E,WAKM,GAJJ1H,mBAAA,CAAyE,OAAzE2H,WAAyE,EAAAhB,gBAAA,CAA/B/G,MAAA,CAAA6H,qBAAqB,IAAG,GAAC,iBACnEtH,YAAA,CAEiBa,yBAAA;gBAhI7BC,UAAA,EA8HqCrB,MAAA,CAAAa,IAAI,CAACmH,iBAAiB;gBA9H3D,uBAAAzG,MAAA,SAAAA,MAAA,iBAAAC,MAAA;kBAAA,OA8HqCxB,MAAA,CAAAa,IAAI,CAACmH,iBAAiB,GAAAxG,MAAA;gBAAA;;gBA9H3DtB,OAAA,EAAAC,QAAA,CA+HwB;kBAAA,OAAiC,E,kBAA3C+C,mBAAA,CAAwGC,SAAA,QA/HtHC,WAAA,CA+HuCpD,MAAA,CAAAgI,iBAAiB,EA/HxD,UA+H+B1E,IAAI;yCAArBzD,YAAA,CAAwG8B,mBAAA;sBAA3D/B,GAAG,EAAE0D,IAAI,CAAC1D,GAAG;sBAAGsB,KAAK,EAAEoC,IAAI,CAAC1D;;sBA/HvFM,OAAA,EAAAC,QAAA,CA+H4F;wBAAA,OAAe,CA/H3GyB,gBAAA,CAAAmF,gBAAA,CA+H+FzD,IAAI,CAACG,IAAI,iB;;sBA/HxG5B,CAAA;;;;gBAAAA,CAAA;qDAAAC,mBAAA,gBAkImD9B,MAAA,CAAAiI,qBAAqB,I,cAA9D/E,mBAAA,CAKM,OALNgF,WAKM,GAJJ9H,mBAAA,CAAyE,OAAzE+H,WAAyE,EAAApB,gBAAA,CAA/B/G,MAAA,CAAAiI,qBAAqB,IAAG,GAAC,iBACnE1H,YAAA,CAEiBa,yBAAA;gBAtI7BC,UAAA,EAoIqCrB,MAAA,CAAAa,IAAI,CAACuH,iBAAiB;gBApI3D,uBAAA7G,MAAA,SAAAA,MAAA,iBAAAC,MAAA;kBAAA,OAoIqCxB,MAAA,CAAAa,IAAI,CAACuH,iBAAiB,GAAA5G,MAAA;gBAAA;;gBApI3DtB,OAAA,EAAAC,QAAA,CAqIwB;kBAAA,OAAiC,E,kBAA3C+C,mBAAA,CAAwGC,SAAA,QArItHC,WAAA,CAqIuCpD,MAAA,CAAAoI,iBAAiB,EArIxD,UAqI+B9E,IAAI;yCAArBzD,YAAA,CAAwG8B,mBAAA;sBAA3D/B,GAAG,EAAE0D,IAAI,CAAC1D,GAAG;sBAAGsB,KAAK,EAAEoC,IAAI,CAAC1D;;sBArIvFM,OAAA,EAAAC,QAAA,CAqI4F;wBAAA,OAAe,CArI3GyB,gBAAA,CAAAmF,gBAAA,CAqI+FzD,IAAI,CAACG,IAAI,iB;;sBArIxG5B,CAAA;;;;gBAAAA,CAAA;qDAAAC,mBAAA,e;;YAAAD,CAAA;cAyIQtB,YAAA,CAEeU,uBAAA;YAFDC,KAAK,EAAC,SAAS;YAACvB,KAAK,EAAC;;YAzI5CO,OAAA,EAAAC,QAAA,CA0IU;cAAA,OAA4F,CAA5FI,YAAA,CAA4F8H,qCAAA;gBA1ItGhH,UAAA,EA0I+CrB,MAAA,CAAAa,IAAI,CAACyH,mBAAmB;gBA1IvE,uBAAA/G,MAAA,SAAAA,MAAA,iBAAAC,MAAA;kBAAA,OA0I+CxB,MAAA,CAAAa,IAAI,CAACyH,mBAAmB,GAAA9G,MAAA;gBAAA;;;YA1IvEK,CAAA;cA4IQtB,YAAA,CA8BeU,uBAAA;YA9BDtB,KAAK,EAAC,4BAA4B;YAACuB,KAAK,EAAC;;YA5I/DhB,OAAA,EAAAC,QAAA,CA6IU;cAAA,OAKM,C,4BALNC,mBAAA,CAKM;gBALDT,KAAK,EAAC;cAAgC,IACzCS,mBAAA,CAA8D;gBAAzDT,KAAK,EAAC;cAAqC,GAAC,SAAO,GACxDS,mBAAA,CAA8D;gBAAzDT,KAAK,EAAC;cAAqC,GAAC,SAAO,GACxDS,mBAAA,CAA8D;gBAAzDT,KAAK,EAAC;cAAqC,GAAC,SAAO,GACxDS,mBAAA,CAAyD;gBAApDT,KAAK,EAAC;cAAqC,GAAC,IAAE,E,yCAErDuD,mBAAA,CAsBMC,SAAA,QAzKhBC,WAAA,CAmJqEpD,MAAA,CAAAuI,iBAAiB,EAnJtF,UAmJ6DjF,IAAI;qCAAvDJ,mBAAA,CAsBM;kBAtBDvD,KAAK,EAAC,gCAAgC;kBAAoCC,GAAG,EAAE0D,IAAI,CAACE;oBACvFpD,mBAAA,CAEM,OAFNoI,WAEM,GADJjI,YAAA,CAAiFwB,mBAAA;kBAAvEE,WAAW,EAAC,UAAU;kBArJ9CZ,UAAA,EAqJwDiC,IAAI,CAACmF,WAAW;kBArJxE,gCAAA7C,mBAAApE,MAAA;oBAAA,OAqJwD8B,IAAI,CAACmF,WAAW,GAAAjH,MAAA;kBAAA;kBAAEY,SAAS,EAAT;kFAE9DhC,mBAAA,CAEM,OAFNsI,WAEM,GADJnI,YAAA,CAAkFwB,mBAAA;kBAAxEE,WAAW,EAAC,UAAU;kBAxJ9CZ,UAAA,EAwJwDiC,IAAI,CAACqF,YAAY;kBAxJzE,gCAAA/C,mBAAApE,MAAA;oBAAA,OAwJwD8B,IAAI,CAACqF,YAAY,GAAAnH,MAAA;kBAAA;kBAAEY,SAAS,EAAT;kFAE/DhC,mBAAA,CAEM,OAFNwI,WAEM,GADJrI,YAAA,CAAsFwB,mBAAA;kBAA5EE,WAAW,EAAC,YAAY;kBA3JhDZ,UAAA,EA2J0DiC,IAAI,CAACuF,cAAc;kBA3J7E,gCAAAjD,mBAAApE,MAAA;oBAAA,OA2J0D8B,IAAI,CAACuF,cAAc,GAAArH,MAAA;kBAAA;kBAAEY,SAAS,EAAT;kFAEnEhC,mBAAA,CAWM,OAXN0I,WAWM,GAVqC9I,MAAA,CAAAuI,iBAAiB,CAACvC,MAAM,I,cAAjEnG,YAAA,CAIUoG,kBAAA;kBAlKxBrG,GAAA;kBA8JwBgF,OAAK,EAAE5E,MAAA,CAAA+I;;kBA9J/B7I,OAAA,EAAAC,QAAA,CA+JgB;oBAAA,OAEU,CAFVI,YAAA,CAEU4F,kBAAA;sBAjK1BjG,OAAA,EAAAC,QAAA,CAgKkB;wBAAA,OAAc,CAAdI,YAAA,CAAc6F,qBAAA,E;;sBAhKhCvE,CAAA;;;kBAAAA,CAAA;sBAAAC,mBAAA,gBAmK6B9B,MAAA,CAAAuI,iBAAiB,CAACvC,MAAM,Q,cAAvCnG,YAAA,CAIUoG,kBAAA;kBAvKxBrG,GAAA;kBAmK4DgF,OAAK,WAALA,OAAKA,CAAApD,MAAA;oBAAA,OAAExB,MAAA,CAAAgJ,gBAAgB,CAAC1F,IAAI,CAACE,EAAE;kBAAA;;kBAnK3FtD,OAAA,EAAAC,QAAA,CAoKgB;oBAAA,OAEU,CAFVI,YAAA,CAEU4F,kBAAA;sBAtK1BjG,OAAA,EAAAC,QAAA,CAqKkB;wBAAA,OAAU,CAAVI,YAAA,CAAU+F,iBAAA,E;;sBArK5BzE,CAAA;;;kBAAAA,CAAA;oEAAAC,mBAAA,e;;;YAAAD,CAAA;cA2KiD7B,MAAA,CAAAiE,SAAS,iB,cAAlDf,mBAAA,CAQM,OARN+F,WAQM,GAPJ1I,YAAA,CAA0EoE,oBAAA;YAA/DG,IAAI,EAAC,SAAS;YAAEF,OAAK,EAAArD,MAAA,SAAAA,MAAA,iBAAAC,MAAA;cAAA,OAAExB,MAAA,CAAAkJ,UAAU,CAAClJ,MAAA,CAAAmJ,OAAO;YAAA;;YA5K9DjJ,OAAA,EAAAC,QAAA,CA4KoE;cAAA,OAAIoB,MAAA,SAAAA,MAAA,QA5KxEK,gBAAA,CA4KoE,MAAI,E;;YA5KxEC,CAAA;eA8KoB7B,MAAA,CAAAoJ,KAAK,CAACC,KAAK,CAACC,MAAM,KAAKtJ,MAAA,CAAAoJ,KAAK,CAACC,KAAK,CAAC7F,EAAE,IAAKxD,MAAA,CAAAiE,SAAS,gB,cAD7DpE,YAAA,CAGY8E,oBAAA;YAhLtB/E,GAAA;YA6KsBgF,OAAK,EAAArD,MAAA,SAAAA,MAAA,iBAAAC,MAAA;cAAA,OAAExB,MAAA,CAAAkJ,UAAU,CAAClJ,MAAA,CAAAmJ,OAAO;YAAA;;YA7K/CjJ,OAAA,EAAAC,QAAA,CA8KqF;cAAA,OAE3EoB,MAAA,SAAAA,MAAA,QAhLVK,gBAAA,CA8KqF,QAE3E,E;;YAhLVC,CAAA;gBAAAC,mBAAA,gB,CAiL+C9B,MAAA,CAAAoJ,KAAK,CAACC,KAAK,CAAC7F,EAAE,I,cAAnD3D,YAAA,CAAmE8E,oBAAA;YAjL7E/E,GAAA;YAiLsBgF,OAAK,EAAE5E,MAAA,CAAAuJ;;YAjL7BrJ,OAAA,EAAAC,QAAA,CAiL+D;cAAA,OAAEoB,MAAA,SAAAA,MAAA,QAjLjEK,gBAAA,CAiL+D,IAAE,E;;YAjLjEC,CAAA;gBAAAC,mBAAA,gBAkL8C9B,MAAA,CAAAoJ,KAAK,CAACC,KAAK,CAAC7F,EAAE,I,cAAlD3D,YAAA,CAAkE8E,oBAAA;YAlL5E/E,GAAA;YAkLsBgF,OAAK,EAAE5E,MAAA,CAAAuJ;;YAlL7BrJ,OAAA,EAAAC,QAAA,CAkL8D;cAAA,OAAEoB,MAAA,SAAAA,MAAA,QAlLhEK,gBAAA,CAkL8D,IAAE,E;;YAlLhEC,CAAA;gBAAAC,mBAAA,e,KAAAA,mBAAA,e;;QAAAD,CAAA;6CAqLiB7B,MAAA,CAAAiE,SAAS,iB,cAApBf,mBAAA,CAAqE,OAArEsG,WAAqE,KArL3E1H,mBAAA,iB,cAsLMjC,YAAA,CAOa4J,UAAA,SAL6CzJ,MAAA,CAAAiE,SAAS,iBAAiBjE,MAAA,CAAA0E,UAAU,I,cAD5F7E,YAAA,CAKsBG,MAAA;QA5L9BJ,GAAA;QAuL8B4D,EAAE,EAAExD,MAAA,CAAAoJ,KAAK,CAACC,KAAK,CAAC7F,EAAE;QAAGC,IAAI,EAAEzD,MAAA,CAAAoJ,KAAK,CAACC,KAAK,CAACK,UAAU;QAAG1E,OAAO,EAAEhF,MAAA,CAAAa,IAAI,CAACmE,OAAO;QAC5FsD,mBAAmB,EAAEtI,MAAA,CAAAa,IAAI,CAACyH,mBAAmB;QAC7CqB,cAAY,EAAE3J,MAAA,CAAA4J,YAAY;QAAGnH,UAAQ,EAAEzC,MAAA,CAAAuJ,SAAS;QAAGtF,SAAS,EAAEjE,MAAA,CAAAiE,SAAS;QAAG4F,MAAM,EAAE7J,MAAA,CAAAoJ,KAAK,CAACC,KAAK,CAACQ,MAAM;QACpGC,UAAU,EAAE9J,MAAA,CAAAa,IAAI,CAACiJ,UAAU;QAAGC,YAAY,EAAE/J,MAAA,CAAAa,IAAI,CAACkJ,YAAY;QAAGC,KAAK,EAAEhK,MAAA,CAAAa,IAAI,CAACmJ,KAAK;QAAGC,MAAM,EAAEjK,MAAA,CAAAa,IAAI,CAACoJ,MAAM;QACvGC,IAAI,EAAElK,MAAA,CAAAa,IAAI,CAACqJ,IAAI;QAAGC,MAAM,EAAEnK,MAAA,CAAAa,IAAI,CAACsJ,MAAM;QAAGC,IAAI,EAAEpK,MAAA,CAAAa,IAAI,CAACuJ;qLA3L9DtI,mBAAA,e,gCA+LIvB,YAAA,CAGmB8J,2BAAA;QAlMvBhJ,UAAA,EA+L+BrB,MAAA,CAAAsK,IAAI;QA/LnC,uBAAA/I,MAAA,SAAAA,MAAA,iBAAAC,MAAA;UAAA,OA+L+BxB,MAAA,CAAAsK,IAAI,GAAA9I,MAAA;QAAA;QAAEiC,IAAI,EAAC;;QA/L1CvD,OAAA,EAAAC,QAAA,CAgMM;UAAA,OACkB,CADlBI,YAAA,CACkBP,MAAA;YADA8E,IAAI,EAAE9E,MAAA,CAAAuK,MAAM;YAAG/G,EAAE,EAAExD,MAAA,CAAAoJ,KAAK,CAACC,KAAK,CAAC7F,EAAE;YAAGwB,OAAO,EAAEhF,MAAA,CAAAa,IAAI,CAACmE,OAAO;YAAGvC,UAAQ,EAAEzC,MAAA,CAAAwK;;;QAhM9F3I,CAAA;;;IAAAA,CAAA;qEACwD7B,MAAA,CAAAyK,OAAO,E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}