{"ast": null, "code": "import { renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, resolveComponent as _resolveComponent, createBlock as _createBlock, withCtx as _withCtx, createVNode as _createVNode, createTextVNode as _createTextVNode, vShow as _vShow, withDirectives as _withDirectives, createElementVNode as _createElementVNode, createCommentVNode as _createCommentVNode, toDisplayString as _toDisplayString, withModifiers as _withModifiers } from \"vue\";\nvar _hoisted_1 = {\n  class: \"SubmitSuggestReply\"\n};\nvar _hoisted_2 = {\n  key: 0,\n  class: \"GlobalAiChatProposalReply\"\n};\nvar _hoisted_3 = {\n  class: \"GlobalAiChatProposalHead\"\n};\nvar _hoisted_4 = {\n  key: 0,\n  class: \"GlobalAiChatProposalLoading\"\n};\nvar _hoisted_5 = {\n  key: 0\n};\nvar _hoisted_6 = {\n  class: \"GlobalAiChatProposalPonderContent\"\n};\nvar _hoisted_7 = {\n  class: \"GlobalAiChatProposalContent\"\n};\nvar _hoisted_8 = {\n  style: {\n    \"width\": \"8%\"\n  }\n};\nvar _hoisted_9 = {\n  style: {\n    \"width\": \"42%\"\n  }\n};\nvar _hoisted_10 = {\n  style: {\n    \"width\": \"50%\",\n    \"margin-left\": \"20px\"\n  }\n};\nvar _hoisted_11 = {\n  class: \"globalFormButton\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_option = _resolveComponent(\"el-option\");\n  var _component_el_select = _resolveComponent(\"el-select\");\n  var _component_el_form_item = _resolveComponent(\"el-form-item\");\n  var _component_el_radio = _resolveComponent(\"el-radio\");\n  var _component_el_radio_group = _resolveComponent(\"el-radio-group\");\n  var _component_el_input = _resolveComponent(\"el-input\");\n  var _component_xyl_upload_file = _resolveComponent(\"xyl-upload-file\");\n  var _component_el_image = _resolveComponent(\"el-image\");\n  var _component_ArrowUpBold = _resolveComponent(\"ArrowUpBold\");\n  var _component_ArrowDownBold = _resolveComponent(\"ArrowDownBold\");\n  var _component_el_icon = _resolveComponent(\"el-icon\");\n  var _component_GlobalMarkdown = _resolveComponent(\"GlobalMarkdown\");\n  var _component_TinyMceEditor = _resolveComponent(\"TinyMceEditor\");\n  var _component_el_button = _resolveComponent(\"el-button\");\n  var _component_el_form = _resolveComponent(\"el-form\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_el_form, {\n    ref: \"formRef\",\n    model: $setup.form,\n    rules: $setup.rules,\n    inline: \"\",\n    \"label-position\": \"top\",\n    class: \"globalForm\",\n    onSubmit: _cache[8] || (_cache[8] = _withModifiers(function () {}, [\"prevent\"]))\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_el_form_item, {\n        label: \"答复类型\",\n        prop: \"replyType\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_el_select, {\n            modelValue: $setup.form.replyType,\n            \"onUpdate:modelValue\": _cache[0] || (_cache[0] = function ($event) {\n              return $setup.form.replyType = $event;\n            }),\n            placeholder: \"请选择答复类型\",\n            clearable: \"\"\n          }, {\n            default: _withCtx(function () {\n              return [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.replyType, function (item) {\n                return _openBlock(), _createBlock(_component_el_option, {\n                  key: item.id,\n                  label: item.name,\n                  value: item.id\n                }, null, 8 /* PROPS */, [\"label\", \"value\"]);\n              }), 128 /* KEYED_FRAGMENT */))];\n            }),\n            _: 1 /* STABLE */\n          }, 8 /* PROPS */, [\"modelValue\"])];\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"是否公开\",\n        prop: \"isOpen\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_el_radio_group, {\n            modelValue: $setup.form.isOpen,\n            \"onUpdate:modelValue\": _cache[1] || (_cache[1] = function ($event) {\n              return $setup.form.isOpen = $event;\n            }),\n            onChange: $setup.isOpenChange\n          }, {\n            default: _withCtx(function () {\n              return [_createVNode(_component_el_radio, {\n                label: 1\n              }, {\n                default: _withCtx(function () {\n                  return _cache[9] || (_cache[9] = [_createTextVNode(\"公开\")]);\n                }),\n                _: 1 /* STABLE */\n              }), _createVNode(_component_el_radio, {\n                label: 0\n              }, {\n                default: _withCtx(function () {\n                  return _cache[10] || (_cache[10] = [_createTextVNode(\"不公开\")]);\n                }),\n                _: 1 /* STABLE */\n              })];\n            }),\n            _: 1 /* STABLE */\n          }, 8 /* PROPS */, [\"modelValue\"])];\n        }),\n        _: 1 /* STABLE */\n      }), _withDirectives(_createVNode(_component_el_form_item, {\n        label: \"不公开理由\",\n        prop: \"noOpenReason\",\n        class: \"globalFormTitle\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_el_input, {\n            modelValue: $setup.form.noOpenReason,\n            \"onUpdate:modelValue\": _cache[2] || (_cache[2] = function ($event) {\n              return $setup.form.noOpenReason = $event;\n            }),\n            placeholder: \"请输入不公开理由\",\n            type: \"textarea\",\n            rows: 5,\n            clearable: \"\"\n          }, null, 8 /* PROPS */, [\"modelValue\"])];\n        }),\n        _: 1 /* STABLE */\n      }, 512 /* NEED_PATCH */), [[_vShow, !$setup.form.isOpen]]), _createVNode(_component_el_form_item, {\n        label: \"上传答复件\",\n        prop: \"fileData\",\n        class: \"globalFormTitle\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_xyl_upload_file, {\n            fileData: $setup.fileData,\n            onFileUpload: $setup.fileUpload\n          }, null, 8 /* PROPS */, [\"fileData\"])];\n        }),\n        _: 1 /* STABLE */\n      }), $setup.whetherAiChat && $setup.isAIFunction ? (_openBlock(), _createElementBlock(\"div\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_createVNode(_component_el_image, {\n        src: $setup.IntelligentAssistant,\n        loading: \"lazy\",\n        fit: \"cover\",\n        draggable: \"false\"\n      }, null, 8 /* PROPS */, [\"src\"]), _cache[11] || (_cache[11] = _createElementVNode(\"div\", {\n        class: \"GlobalAiChatProposalName\"\n      }, \"答复件符合性评估\", -1 /* HOISTED */))]), $setup.loading ? (_openBlock(), _createElementBlock(\"div\", _hoisted_4, [_createElementVNode(\"div\", {\n        class: \"answerLoading\",\n        innerHTML: $setup.loadingIcon\n      })])) : _createCommentVNode(\"v-if\", true), $setup.elTime ? (_openBlock(), _createElementBlock(\"div\", {\n        key: 1,\n        class: \"GlobalAiChatProposalPonder forbidSelect\",\n        onClick: _cache[3] || (_cache[3] = function ($event) {\n          return $setup.ponderShow = !$setup.ponderShow;\n        })\n      }, [_createElementVNode(\"div\", {\n        innerHTML: $setup.ponderIcon\n      }), _cache[12] || (_cache[12] = _createTextVNode(\" 已深度思考 \")), $setup.elTime !== '1' ? (_openBlock(), _createElementBlock(\"span\", _hoisted_5, \"（用时 \" + _toDisplayString($setup.elTime) + \"）\", 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true), _createVNode(_component_el_icon, null, {\n        default: _withCtx(function () {\n          return [$setup.ponderShow ? (_openBlock(), _createBlock(_component_ArrowUpBold, {\n            key: 0\n          })) : _createCommentVNode(\"v-if\", true), !$setup.ponderShow ? (_openBlock(), _createBlock(_component_ArrowDownBold, {\n            key: 1\n          })) : _createCommentVNode(\"v-if\", true)];\n        }),\n        _: 1 /* STABLE */\n      })])) : _createCommentVNode(\"v-if\", true), _withDirectives(_createElementVNode(\"div\", _hoisted_6, [_createVNode(_component_GlobalMarkdown, {\n        ref: \"elPonderRef\",\n        modelValue: $setup.elPonderData.content,\n        \"onUpdate:modelValue\": _cache[4] || (_cache[4] = function ($event) {\n          return $setup.elPonderData.content = $event;\n        }),\n        content: $setup.elPonderData.contentOld\n      }, null, 8 /* PROPS */, [\"modelValue\", \"content\"])], 512 /* NEED_PATCH */), [[_vShow, $setup.ponderShow]]), _createElementVNode(\"div\", _hoisted_7, [_createVNode(_component_GlobalMarkdown, {\n        ref: \"elRef\",\n        modelValue: $setup.elData.content,\n        \"onUpdate:modelValue\": _cache[5] || (_cache[5] = function ($event) {\n          return $setup.elData.content = $event;\n        }),\n        content: $setup.elData.contentOld\n      }, null, 8 /* PROPS */, [\"modelValue\", \"content\"])])])) : _createCommentVNode(\"v-if\", true), _createVNode(_component_el_form_item, {\n        label: \"内容\",\n        prop: \"content\",\n        class: \"globalFormTitle\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_TinyMceEditor, {\n            modelValue: $setup.form.content,\n            \"onUpdate:modelValue\": _cache[6] || (_cache[6] = function ($event) {\n              return $setup.form.content = $event;\n            })\n          }, null, 8 /* PROPS */, [\"modelValue\"])];\n        }),\n        _: 1 /* STABLE */\n      }), $setup.form.suggestedReplies && $setup.form.suggestedReplies.length > 0 ? (_openBlock(), _createBlock(_component_el_form_item, {\n        key: 1,\n        label: \"意见建议清单答复\",\n        class: \"globalFormTitle\",\n        prop: \"suggestedReplies\"\n      }, {\n        default: _withCtx(function () {\n          return [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.form.suggestedReplies, function (item, index) {\n            return _openBlock(), _createElementBlock(\"div\", {\n              key: index,\n              class: \"SuggestedReplyList\"\n            }, [_createElementVNode(\"div\", _hoisted_8, \"建议\" + _toDisplayString(index + 1), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_9, _toDisplayString(item.content), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_10, [_createVNode(_component_el_form_item, {\n              label: \"答复类型\",\n              prop: \"SuggestedReplyType\",\n              class: \"globalFormTitle\"\n            }, {\n              default: _withCtx(function () {\n                return [_createVNode(_component_el_select, {\n                  modelValue: item.SuggestedReplyType,\n                  \"onUpdate:modelValue\": function onUpdateModelValue($event) {\n                    return item.SuggestedReplyType = $event;\n                  },\n                  placeholder: \"请选择答复类型\",\n                  clearable: \"\"\n                }, {\n                  default: _withCtx(function () {\n                    return [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.SuggestedReplyType, function (items) {\n                      return _openBlock(), _createBlock(_component_el_option, {\n                        key: items.id,\n                        label: items.name,\n                        value: items.id\n                      }, null, 8 /* PROPS */, [\"label\", \"value\"]);\n                    }), 128 /* KEYED_FRAGMENT */))];\n                  }),\n                  _: 2 /* DYNAMIC */\n                }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"modelValue\", \"onUpdate:modelValue\"])];\n              }),\n              _: 2 /* DYNAMIC */\n            }, 1024 /* DYNAMIC_SLOTS */), _createVNode(_component_el_form_item, {\n              label: \"答复内容\",\n              prop: \"SuggestedReplyContent\",\n              style: {\n                \"margin-top\": \"12px\"\n              },\n              class: \"globalFormTitle\"\n            }, {\n              default: _withCtx(function () {\n                return [_createVNode(_component_el_input, {\n                  modelValue: item.SuggestedReplyContent,\n                  \"onUpdate:modelValue\": function onUpdateModelValue($event) {\n                    return item.SuggestedReplyContent = $event;\n                  },\n                  placeholder: \"请输入答复内容\",\n                  type: \"textarea\",\n                  rows: 3,\n                  clearable: \"\"\n                }, null, 8 /* PROPS */, [\"modelValue\", \"onUpdate:modelValue\"])];\n              }),\n              _: 2 /* DYNAMIC */\n            }, 1024 /* DYNAMIC_SLOTS */)])]);\n          }), 128 /* KEYED_FRAGMENT */))];\n        }),\n        _: 1 /* STABLE */\n      })) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"div\", _hoisted_11, [_createVNode(_component_el_button, {\n        type: \"primary\",\n        onClick: _cache[7] || (_cache[7] = function ($event) {\n          return $setup.submitForm($setup.formRef);\n        })\n      }, {\n        default: _withCtx(function () {\n          return _cache[13] || (_cache[13] = [_createTextVNode(\"提交\")]);\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_button, {\n        onClick: $setup.resetForm\n      }, {\n        default: _withCtx(function () {\n          return _cache[14] || (_cache[14] = [_createTextVNode(\"取消\")]);\n        }),\n        _: 1 /* STABLE */\n      })])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"model\", \"rules\"])]);\n}", "map": {"version": 3, "names": ["class", "key", "style", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_form", "ref", "model", "$setup", "form", "rules", "inline", "onSubmit", "_cache", "_withModifiers", "default", "_withCtx", "_component_el_form_item", "label", "prop", "_component_el_select", "modelValue", "replyType", "$event", "placeholder", "clearable", "_Fragment", "_renderList", "item", "_createBlock", "_component_el_option", "id", "name", "value", "_", "_component_el_radio_group", "isOpen", "onChange", "isOpenChange", "_component_el_radio", "_createTextVNode", "_component_el_input", "noOpenReason", "type", "rows", "_component_xyl_upload_file", "fileData", "onFileUpload", "fileUpload", "whetherAiChat", "isAIFunction", "_hoisted_2", "_createElementVNode", "_hoisted_3", "_component_el_image", "src", "IntelligentAssistant", "loading", "fit", "draggable", "_hoisted_4", "innerHTML", "loadingIcon", "_createCommentVNode", "elTime", "onClick", "ponderShow", "ponderIcon", "_hoisted_5", "_toDisplayString", "_component_el_icon", "_component_ArrowUpBold", "_component_ArrowDownBold", "_hoisted_6", "_component_GlobalMarkdown", "elPonderData", "content", "contentOld", "_hoisted_7", "elData", "_component_TinyMceEditor", "suggestedReplies", "length", "index", "_hoisted_8", "_hoisted_9", "_hoisted_10", "SuggestedReplyType", "onUpdateModelValue", "items", "Suggested<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_hoisted_11", "_component_el_button", "submitForm", "formRef", "resetForm"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\UnitSuggestDetail\\component\\SubmitSuggestReply.vue"], "sourcesContent": ["<template>\r\n  <div class=\"SubmitSuggestReply\">\r\n    <el-form ref=\"formRef\" :model=\"form\" :rules=\"rules\" inline label-position=\"top\" class=\"globalForm\"\r\n      @submit.enter.prevent>\r\n      <el-form-item label=\"答复类型\" prop=\"replyType\">\r\n        <el-select v-model=\"form.replyType\" placeholder=\"请选择答复类型\" clearable>\r\n          <el-option v-for=\"item in replyType\" :key=\"item.id\" :label=\"item.name\" :value=\"item.id\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"是否公开\" prop=\"isOpen\">\r\n        <el-radio-group v-model=\"form.isOpen\" @change=\"isOpenChange\">\r\n          <el-radio :label=\"1\">公开</el-radio>\r\n          <el-radio :label=\"0\">不公开</el-radio>\r\n        </el-radio-group>\r\n      </el-form-item>\r\n      <el-form-item label=\"不公开理由\" prop=\"noOpenReason\" v-show=\"!form.isOpen\" class=\"globalFormTitle\">\r\n        <el-input v-model=\"form.noOpenReason\" placeholder=\"请输入不公开理由\" type=\"textarea\" :rows=\"5\" clearable />\r\n      </el-form-item>\r\n      <el-form-item label=\"上传答复件\" prop=\"fileData\" class=\"globalFormTitle\">\r\n        <xyl-upload-file :fileData=\"fileData\" @fileUpload=\"fileUpload\" />\r\n      </el-form-item>\r\n      <div class=\"GlobalAiChatProposalReply\" v-if=\"whetherAiChat && isAIFunction\">\r\n        <div class=\"GlobalAiChatProposalHead\">\r\n          <el-image :src=\"IntelligentAssistant\" loading=\"lazy\" fit=\"cover\" draggable=\"false\" />\r\n          <div class=\"GlobalAiChatProposalName\">答复件符合性评估</div>\r\n        </div>\r\n        <div class=\"GlobalAiChatProposalLoading\" v-if=\"loading\">\r\n          <div class=\"answerLoading\" v-html=\"loadingIcon\"></div>\r\n        </div>\r\n        <div class=\"GlobalAiChatProposalPonder forbidSelect\" @click=\"ponderShow = !ponderShow\" v-if=\"elTime\">\r\n          <div v-html=\"ponderIcon\"></div>\r\n          已深度思考\r\n          <span v-if=\"elTime !== '1'\">（用时 {{ elTime }}）</span>\r\n          <el-icon>\r\n            <ArrowUpBold v-if=\"ponderShow\" />\r\n            <ArrowDownBold v-if=\"!ponderShow\" />\r\n          </el-icon>\r\n        </div>\r\n        <div class=\"GlobalAiChatProposalPonderContent\" v-show=\"ponderShow\">\r\n          <GlobalMarkdown ref=\"elPonderRef\" v-model=\"elPonderData.content\" :content=\"elPonderData.contentOld\" />\r\n        </div>\r\n        <div class=\"GlobalAiChatProposalContent\">\r\n          <GlobalMarkdown ref=\"elRef\" v-model=\"elData.content\" :content=\"elData.contentOld\" />\r\n        </div>\r\n      </div>\r\n      <el-form-item label=\"内容\" prop=\"content\" class=\"globalFormTitle\">\r\n        <TinyMceEditor v-model=\"form.content\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"意见建议清单答复\" class=\"globalFormTitle\" prop=\"suggestedReplies\"\r\n        v-if=\"form.suggestedReplies && form.suggestedReplies.length > 0\">\r\n        <div v-for=\"(item, index) in form.suggestedReplies\" :key=\"index\" class=\"SuggestedReplyList\">\r\n          <div style=\"width: 8%;\">建议{{ index + 1 }}</div>\r\n          <div style=\"width: 42%;\">{{ item.content }}</div>\r\n          <div style=\"width: 50%;margin-left: 20px;\">\r\n            <el-form-item label=\"答复类型\" prop=\"SuggestedReplyType\" class=\"globalFormTitle\">\r\n              <el-select v-model=\"item.SuggestedReplyType\" placeholder=\"请选择答复类型\" clearable>\r\n                <el-option v-for=\"items in SuggestedReplyType\" :key=\"items.id\" :label=\"items.name\" :value=\"items.id\" />\r\n              </el-select>\r\n            </el-form-item>\r\n            <el-form-item label=\"答复内容\" prop=\"SuggestedReplyContent\" style=\"margin-top: 12px;\" class=\"globalFormTitle\">\r\n              <el-input v-model=\"item.SuggestedReplyContent\" placeholder=\"请输入答复内容\" type=\"textarea\" :rows=\"3\"\r\n                clearable />\r\n            </el-form-item>\r\n          </div>\r\n        </div>\r\n      </el-form-item>\r\n      <div class=\"globalFormButton\">\r\n        <el-button type=\"primary\" @click=\"submitForm(formRef)\">提交</el-button>\r\n        <el-button @click=\"resetForm\">取消</el-button>\r\n      </div>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'SubmitSuggestReply' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport http_stream from 'common/http/stream.js'\r\nimport { reactive, ref, onMounted, nextTick } from 'vue'\r\nimport { IntelligentAssistant, whetherAiChat } from 'common/js/system_var.js'\r\nimport { AIFunctionMethod } from 'common/js/AIFunctionMethod.js'\r\nimport { ElMessage } from 'element-plus'\r\nconst props = defineProps({\r\n  id: { type: String, default: '' },\r\n  unitId: { type: String, default: '' },\r\n  suggestId: { type: String, default: '' },\r\n  detailsObjectType: { type: String, default: 'handlingPortionId' }\r\n})\r\nconst loadingIcon =\r\n  '<svg t=\"1716976607389\" viewBox=\"0 0 1024 1024\" version=\"1.1\" p-id=\"2362\" width=\"60%\" height=\"60%\"><path d=\"M827.211075 221.676536m-54.351151 0a54.351151 54.351151 0 1 0 108.702302 0 54.351151 54.351151 0 1 0-108.702302 0Z\" fill=\"#2c2c2c\" p-id=\"2363\"></path><path d=\"M940.905298 515.399947m-67.086951 0a67.086952 67.086952 0 1 0 134.173903 0 67.086952 67.086952 0 1 0-134.173903 0Z\" fill=\"#2c2c2c\" p-id=\"2364\"></path><path d=\"M829.755035 810.595334m-78.974766 0a78.974766 78.974766 0 1 0 157.949532 0 78.974766 78.974766 0 1 0-157.949532 0Z\" fill=\"#2c2c2c\" p-id=\"2365\"></path><path d=\"M534.831643 928.64149m-91.48657 0a91.486571 91.486571 0 1 0 182.973141 0 91.486571 91.486571 0 1 0-182.973141 0Z\" fill=\"#2c2c2c\" p-id=\"2366\"></path><path d=\"M243.780191 805.955407m-101.902408 0a101.902408 101.902408 0 1 0 203.804816 0 101.902408 101.902408 0 1 0-203.804816 0Z\" fill=\"#2c2c2c\" p-id=\"2367\"></path><path d=\"M536.623615 107.870315m-107.854315 0a107.854315 107.854315 0 1 0 215.70863 0 107.854315 107.854315 0 1 0-215.70863 0Z\" fill=\"#2c2c2c\" p-id=\"2368\"></path><path d=\"M243.780191 224.220497m-107.854315 0a107.854315 107.854315 0 1 0 215.70863 0 107.854315 107.854315 0 1 0-215.70863 0Z\" fill=\"#2c2c2c\" p-id=\"2369\"></path><path d=\"M129.429978 512.008m-102.766395 0a102.766394 102.766394 0 1 0 205.532789 0 102.766394 102.766394 0 1 0-205.532789 0Z\" fill=\"#2c2c2c\" p-id=\"2370\"></path></svg>'\r\nconst ponderIcon =\r\n  '<svg t=\"1741658991857\" class=\"icon\" viewBox=\"0 0 1024 1024\" version=\"1.1\" xmlns=\"http://www.w3.org/2000/svg\" p-id=\"11203\" width=\"16\" height=\"16\"><path d=\"M886.592 369.152c-42.24 89.28-116.48 190.848-211.456 285.76-94.976 94.976-196.48 169.216-285.824 211.52-44.352 20.992-88.96 35.712-130.24 39.168-40.832 3.456-87.68-3.712-122.432-38.4-34.752-34.816-41.92-81.664-38.464-122.496 3.456-41.216 18.176-85.888 39.168-130.24 42.304-89.28 116.544-190.912 211.456-285.824 94.976-94.912 196.544-169.152 285.824-211.456 44.416-21.056 88.96-35.712 130.24-39.232 40.832-3.456 87.68 3.712 122.496 38.464 34.752 34.752 41.92 81.664 38.4 122.496-3.456 41.216-18.112 85.824-39.168 130.24zM629.888 609.664c182.272-182.272 277.312-382.848 212.224-448-65.152-65.088-265.728 29.952-448 212.224-182.336 182.336-277.376 382.912-212.224 448 65.088 65.152 265.664-29.888 448-212.224z\" p-id=\"11204\"></path><path d=\"M137.344 369.152c42.304 89.28 116.544 190.848 211.52 285.76 94.912 94.976 196.48 169.216 285.76 211.52 44.416 20.992 88.96 35.712 130.24 39.168 40.832 3.456 87.68-3.712 122.496-38.4 34.752-34.816 41.92-81.664 38.4-122.496-3.456-41.216-18.112-85.888-39.168-130.24-42.24-89.28-116.48-190.912-211.456-285.824-94.912-94.912-196.48-169.152-285.824-211.456-44.352-21.056-88.96-35.712-130.24-39.232-40.832-3.456-87.68 3.712-122.432 38.464-34.752 34.752-41.92 81.664-38.464 122.496 3.456 41.216 18.176 85.824 39.168 130.24z m256.768 240.512c-182.336-182.272-277.376-382.848-212.224-448 65.088-65.088 265.664 29.952 448 212.224 182.272 182.336 277.312 382.912 212.224 448-65.152 65.152-265.728-29.888-448-212.224z\" p-id=\"11205\"></path><path d=\"M576 512a64 64 0 1 1-128 0 64 64 0 0 1 128 0z\" p-id=\"11206\"></path></svg>'\r\nconst emit = defineEmits(['callback'])\r\n\r\nconst formRef = ref()\r\nconst form = reactive({\r\n  id: '',\r\n  replyType: '',\r\n  isOpen: 1,\r\n  noOpenReason: '',\r\n  fileData: [],\r\n  content: '',\r\n  suggestedReplies: [ // 将 SuggestedReplyList 移到 form 中\r\n    { id: '1', content: '', SuggestedReplyType: '', SuggestedReplyContent: '' },\r\n    { id: '2', content: ' ', SuggestedReplyType: '', SuggestedReplyContent: '' }\r\n  ]\r\n})\r\nconst SuggestedReplyType = ref([\r\n  { id: 'A', name: 'A类' },\r\n  { id: 'B', name: 'B类' },\r\n  { id: 'C', name: 'C类' }\r\n])\r\nconst rules = reactive({\r\n  replyType: [{ required: true, message: '请选择答复类型', trigger: ['blur', 'change'] }],\r\n  isOpen: [{ required: true, message: '请选择是否公开', trigger: ['blur', 'change'] }],\r\n  fileData: [{ required: true, message: '请上传答复件', trigger: ['blur', 'change'] }],\r\n  noOpenReason: [{ required: false, message: '请输入不公开理由', trigger: ['blur', 'change'] }],\r\n  content: [{ required: true, message: '请输入内容', trigger: ['blur', 'change'] }]\r\n})\r\nconst details = ref({})\r\nconst fileData = ref([])\r\nconst replyType = ref([])\r\n\r\nconst ponderShow = ref(true)\r\nconst elTime = ref('')\r\nconst elRef = ref()\r\nconst elPonderRef = ref()\r\nconst elData = ref({ content: '', contentOld: '' })\r\nconst elPonderData = ref({ content: '', contentOld: '' })\r\n\r\nlet currentRequest = null\r\nconst loading = ref(false)\r\nconst isStreaming = ref(false)\r\nlet startTime = null\r\nlet endTime = null\r\n\r\nconst guid = () => {\r\n  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {\r\n    var r = (Math.random() * 16) | 0,\r\n      v = c == 'x' ? r : (r & 0x3) | 0x8\r\n    return v.toString(16)\r\n  })\r\n}\r\nconst { isAIFunction } = AIFunctionMethod('ai-proposal-answer')\r\nonMounted(() => {\r\n  dictionaryData()\r\n  proposalInventoryList()\r\n  if (props.id) { handingPortionAnswerInfo() }\r\n  suggestionInfo()\r\n})\r\n\r\nconst dictionaryData = async () => {\r\n  const res = await api.dictionaryData({ dictCodes: ['suggestion_answer_type'] })\r\n  var { data } = res\r\n  replyType.value = data.suggestion_answer_type\r\n}\r\n\r\nconst proposalInventoryList = async () => {\r\n  const res = await api.proposalInventoryList({ pageNo: '1', pageSize: '999', query: { suggestionId: props.suggestId } })\r\n  form.suggestedReplies = res.data\r\n}\r\n\r\nconst isOpenChange = () => {\r\n  rules.noOpenReason = [{ required: false, message: '请输入不公开理由', trigger: ['blur', 'change'] }]\r\n  if (!form.isOpen) {\r\n    rules.noOpenReason = [{ required: true, message: '请输入不公开理由', trigger: ['blur', 'change'] }]\r\n  }\r\n}\r\nconst fileUpload = (file) => {\r\n  form.fileData = file.map((v) => v.id)\r\n  fileData.value = file\r\n  formRef.value.validateField('fileData')\r\n  nextTick(() => {\r\n    elTime.value = ''\r\n    elData.value = { content: '', contentOld: '' }\r\n    elPonderData.value = { content: '', contentOld: '' }\r\n    elRef.value?.clearContent()\r\n    elPonderRef.value?.clearContent()\r\n    handleStopMessage()\r\n    if (fileData.value.length) {\r\n      handleHttpStream()\r\n    }\r\n  })\r\n}\r\nconst suggestionInfo = async () => {\r\n  const { data } = await api.suggestionInfo({ detailId: props.suggestId })\r\n  details.value = data\r\n}\r\nconst handingPortionAnswerInfo = async () => {\r\n  var params = {}\r\n  params[props.detailsObjectType] = props.id\r\n  const res = await api.handingPortionAnswerInfo(params)\r\n  var { data } = res\r\n  form.id = data.id\r\n  form.replyType = data.suggestionAnswerType?.value\r\n  form.isOpen = data.isOpen\r\n  form.noOpenReason = data.noOpenReason\r\n  form.content = data.content\r\n  fileData.value = data.attachments || []\r\n  form.fileData = data.attachments.map((v) => v.id)\r\n}\r\nconst submitForm = async (formEl) => {\r\n  if (!formEl) return\r\n  await formEl.validate((valid, fields) => {\r\n    if (valid) {\r\n      globalJson()\r\n    } else {\r\n      ElMessage({ type: 'warning', message: '请根据提示信息完善字段内容！' })\r\n    }\r\n  })\r\n}\r\nconst globalJson = async () => {\r\n  const { code } = await api.globalJson(\r\n    props.id ? '/cppcc/handingPortionAnswer/edit' : '/cppcc/handingPortionAnswer/add',\r\n    {\r\n      form: {\r\n        id: form.id,\r\n        handlingPortionId: props.unitId,\r\n        suggestionId: props.suggestId,\r\n        suggestionAnswerType: form.replyType,\r\n        isOpen: form.isOpen,\r\n        noOpenReason: form.isOpen ? '' : form.noOpenReason,\r\n        content: form.content,\r\n        attachmentIds: fileData.value.map((v) => v.id)\r\n      }\r\n    }\r\n  )\r\n  if (code === 200) {\r\n    ElMessage({ type: 'success', message: props.id ? '编辑成功' : '新增成功' })\r\n    emit('callback', true)\r\n  }\r\n}\r\nconst resetForm = () => {\r\n  emit('callback')\r\n}\r\n\r\nconst formatDuring = (mss) => {\r\n  const days = parseInt(mss / (1000 * 60 * 60 * 24))\r\n  const hours = parseInt((mss % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))\r\n  const minutes = parseInt((mss % (1000 * 60 * 60)) / (1000 * 60))\r\n  const seconds = (mss % (1000 * 60)) / 1000\r\n  var time = ''\r\n  if (days > 0) time += `${days} 天 `\r\n  if (hours > 0) time += `${hours} 小时 `\r\n  if (minutes > 0) time += `${minutes} 分钟 `\r\n  if (seconds > 0) time += `${seconds} 秒 `\r\n  return time\r\n}\r\nconst handleHttpStream = async () => {\r\n  if (!whetherAiChat.value) return\r\n  startTime = new Date()\r\n  loading.value = true\r\n  isStreaming.value = true\r\n  try {\r\n    const AiChatParam = {\r\n      chatBusinessScene: 'ai-proposal-answer',\r\n      chatId: guid(),\r\n      question: '提案答复件智能检测',\r\n      businessId: props.suggestId,\r\n      attachmentIds: fileData.value.map((v) => v.id).join(',')\r\n    }\r\n    currentRequest = http_stream('/aigpt/chatStream', {\r\n      body: JSON.stringify(AiChatParam),\r\n      onMessage (event) {\r\n        // if (event.data === '{\\\"status\\\":\\\"running\\\",\\\"name\\\":\\\"AI 对话\\\"}') loading.value = false\r\n        loading.value = false\r\n        if (event.data !== '[DONE]') {\r\n          const data = JSON.parse(event.data)\r\n          if (Array.isArray(data)) {\r\n            console.log('[]', data)\r\n          } else {\r\n            // console.log('{}', data)\r\n            const choice = data?.choices || [{}]\r\n            const details = choice[0]?.delta || {}\r\n            if (Object.prototype.hasOwnProperty.call(details, 'reasoning_content')) {\r\n              elPonderRef.value?.enqueueRender(details.reasoning_content || '')\r\n              if (elTime.value) {\r\n                startTime = null\r\n                endTime = null\r\n              } else {\r\n                endTime = new Date()\r\n                const executionTime = endTime - startTime\r\n                elTime.value = formatDuring(executionTime)\r\n              }\r\n            }\r\n            if (Object.prototype.hasOwnProperty.call(details, 'content')) {\r\n              elRef.value?.enqueueRender(details.content || '')\r\n            }\r\n          }\r\n        } else {\r\n          // console.log(event.data)\r\n          elRef.value?.enqueueRender('')\r\n          isStreaming.value = false\r\n        }\r\n      },\r\n      onError (err) {\r\n        console.log('流式接口错误:', err)\r\n      },\r\n      onClose () {\r\n        loading.value = false\r\n        isStreaming.value = false\r\n        console.log('流式接口关闭')\r\n      }\r\n    })\r\n    await currentRequest.promise\r\n  } catch (error) {\r\n    loading.value = false\r\n    isStreaming.value = false\r\n    elRef.value?.enqueueRender('服务器繁忙，请稍后再试。')\r\n    console.error('启动流式接口失败:', error)\r\n  } finally {\r\n    // currentRequest = null\r\n  }\r\n}\r\nconst handleCloseMessage = () => {\r\n  currentRequest = null\r\n  loading.value = false\r\n  isStreaming.value = false\r\n}\r\nconst handleStopMessage = () => {\r\n  if (currentRequest) {\r\n    currentRequest.abort()\r\n    loading.value = false\r\n    isStreaming.value = false\r\n    console.log('启动流式接口停止')\r\n  }\r\n  handleCloseMessage()\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.SubmitSuggestReply {\r\n  width: 990px;\r\n}\r\n\r\n.SuggestedReplyList {\r\n  display: flex;\r\n  align-items: center;\r\n  width: 100%;\r\n  border-bottom: 1px solid #ccc;\r\n  padding: 6px 20px 20px 20px;\r\n}\r\n\r\n.GlobalAiChatProposalReply {\r\n  width: calc(100% - 20px);\r\n  height: 100%;\r\n  padding: var(--zy-distance-two);\r\n  border-radius: var(--el-border-radius-base);\r\n  border: 1px dashed var(--zy-el-color-primary);\r\n  margin-bottom: 18px !important;\r\n\r\n  .GlobalAiChatProposalHead {\r\n    width: 100%;\r\n    display: flex;\r\n    align-items: flex-end;\r\n    padding-bottom: 12px;\r\n\r\n    .zy-el-image {\r\n      width: 52px;\r\n      height: 52px;\r\n    }\r\n\r\n    .GlobalAiChatProposalName {\r\n      width: 100%;\r\n      font-weight: bold;\r\n      padding: 0 0 10px 12px;\r\n      line-height: var(--zy-line-height);\r\n      font-size: var(--zy-name-font-size);\r\n    }\r\n  }\r\n\r\n  .GlobalAiChatProposalLoading {\r\n    width: 100%;\r\n    height: 32px;\r\n    position: relative;\r\n\r\n    @keyframes circleRoate {\r\n      from {\r\n        transform: translateY(-50%) rotate(0deg);\r\n      }\r\n\r\n      to {\r\n        transform: translateY(-50%) rotate(360deg);\r\n      }\r\n    }\r\n\r\n    .answerLoading {\r\n      width: calc((var(--zy-text-font-size) * var(--zy-line-height)) + 20px);\r\n      height: calc((var(--zy-text-font-size) * var(--zy-line-height)) + 20px);\r\n      position: absolute;\r\n      top: 50%;\r\n      left: 0;\r\n      z-index: 3;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      animation: circleRoate 1s infinite linear;\r\n\r\n      path {\r\n        fill: var(--zy-el-color-primary);\r\n      }\r\n    }\r\n\r\n    .answerLoading+.QuestionsAndAnswersChatText {\r\n      color: var(--zy-el-color-primary);\r\n      padding-left: calc((var(--zy-text-font-size) * var(--zy-line-height)) + 20px);\r\n    }\r\n  }\r\n\r\n  .GlobalAiChatProposalPonder {\r\n    width: 100%;\r\n    height: 32px;\r\n    display: flex;\r\n    align-items: center;\r\n    padding: 0 12px;\r\n    border-radius: var(--el-border-radius-base);\r\n    line-height: var(--zy-line-height);\r\n    font-size: var(--zy-text-font-size);\r\n    color: var(--zy-el-text-color-primary);\r\n    background: var(--zy-el-color-info-light-9);\r\n    margin-bottom: 12px;\r\n    cursor: pointer;\r\n\r\n    div {\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      margin-right: 6px;\r\n    }\r\n\r\n    span {\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      color: var(--zy-el-text-color-primary);\r\n    }\r\n  }\r\n\r\n  .GlobalAiChatProposalPonderContent {\r\n    width: 100%;\r\n    padding-left: 12px;\r\n    border-left: 2px solid var(--zy-el-border-color);\r\n    margin-bottom: 12px;\r\n\r\n    * {\r\n      color: var(--zy-el-text-color-secondary);\r\n    }\r\n  }\r\n\r\n  .GlobalAiChatProposalContent {\r\n    width: 100%;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAoB;;EADjCC,GAAA;EAqBWD,KAAK,EAAC;;;EACJA,KAAK,EAAC;AAA0B;;EAtB7CC,GAAA;EA0BaD,KAAK,EAAC;;;EA1BnBC,GAAA;AAAA;;EAsCaD,KAAK,EAAC;AAAmC;;EAGzCA,KAAK,EAAC;AAA6B;;EAUjCE,KAAkB,EAAlB;IAAA;EAAA;AAAkB;;EAClBA,KAAmB,EAAnB;IAAA;EAAA;AAAmB;;EACnBA,KAAqC,EAArC;IAAA;IAAA;EAAA;AAAqC;;EAazCF,KAAK,EAAC;AAAkB;;;;;;;;;;;;;;;;;uBAjEjCG,mBAAA,CAsEM,OAtENC,UAsEM,GArEJC,YAAA,CAoEUC,kBAAA;IApEDC,GAAG,EAAC,SAAS;IAAEC,KAAK,EAAEC,MAAA,CAAAC,IAAI;IAAGC,KAAK,EAAEF,MAAA,CAAAE,KAAK;IAAEC,MAAM,EAAN,EAAM;IAAC,gBAAc,EAAC,KAAK;IAACZ,KAAK,EAAC,YAAY;IAC/Fa,QAAM,EAAAC,MAAA,QAAAA,MAAA,MAHbC,cAAA,CAGM,cAAqB;;IAH3BC,OAAA,EAAAC,QAAA,CAIM;MAAA,OAIe,CAJfZ,YAAA,CAIea,uBAAA;QAJDC,KAAK,EAAC,MAAM;QAACC,IAAI,EAAC;;QAJtCJ,OAAA,EAAAC,QAAA,CAKQ;UAAA,OAEY,CAFZZ,YAAA,CAEYgB,oBAAA;YAPpBC,UAAA,EAK4Bb,MAAA,CAAAC,IAAI,CAACa,SAAS;YAL1C,uBAAAT,MAAA,QAAAA,MAAA,gBAAAU,MAAA;cAAA,OAK4Bf,MAAA,CAAAC,IAAI,CAACa,SAAS,GAAAC,MAAA;YAAA;YAAEC,WAAW,EAAC,SAAS;YAACC,SAAS,EAAT;;YALlEV,OAAA,EAAAC,QAAA,CAMqB;cAAA,OAAyB,E,kBAApCd,mBAAA,CAA0FwB,SAAA,QANpGC,WAAA,CAMoCnB,MAAA,CAAAc,SAAS,EAN7C,UAM4BM,IAAI;qCAAtBC,YAAA,CAA0FC,oBAAA;kBAApD9B,GAAG,EAAE4B,IAAI,CAACG,EAAE;kBAAGb,KAAK,EAAEU,IAAI,CAACI,IAAI;kBAAGC,KAAK,EAAEL,IAAI,CAACG;;;;YAN9FG,CAAA;;;QAAAA,CAAA;UASM9B,YAAA,CAKea,uBAAA;QALDC,KAAK,EAAC,MAAM;QAACC,IAAI,EAAC;;QATtCJ,OAAA,EAAAC,QAAA,CAUQ;UAAA,OAGiB,CAHjBZ,YAAA,CAGiB+B,yBAAA;YAbzBd,UAAA,EAUiCb,MAAA,CAAAC,IAAI,CAAC2B,MAAM;YAV5C,uBAAAvB,MAAA,QAAAA,MAAA,gBAAAU,MAAA;cAAA,OAUiCf,MAAA,CAAAC,IAAI,CAAC2B,MAAM,GAAAb,MAAA;YAAA;YAAGc,QAAM,EAAE7B,MAAA,CAAA8B;;YAVvDvB,OAAA,EAAAC,QAAA,CAWU;cAAA,OAAkC,CAAlCZ,YAAA,CAAkCmC,mBAAA;gBAAvBrB,KAAK,EAAE;cAAC;gBAX7BH,OAAA,EAAAC,QAAA,CAW+B;kBAAA,OAAEH,MAAA,QAAAA,MAAA,OAXjC2B,gBAAA,CAW+B,IAAE,E;;gBAXjCN,CAAA;kBAYU9B,YAAA,CAAmCmC,mBAAA;gBAAxBrB,KAAK,EAAE;cAAC;gBAZ7BH,OAAA,EAAAC,QAAA,CAY+B;kBAAA,OAAGH,MAAA,SAAAA,MAAA,QAZlC2B,gBAAA,CAY+B,KAAG,E;;gBAZlCN,CAAA;;;YAAAA,CAAA;;;QAAAA,CAAA;0BAeM9B,YAAA,CAEea,uBAAA;QAFDC,KAAK,EAAC,OAAO;QAACC,IAAI,EAAC,cAAc;QAAuBpB,KAAK,EAAC;;QAflFgB,OAAA,EAAAC,QAAA,CAgBQ;UAAA,OAAmG,CAAnGZ,YAAA,CAAmGqC,mBAAA;YAhB3GpB,UAAA,EAgB2Bb,MAAA,CAAAC,IAAI,CAACiC,YAAY;YAhB5C,uBAAA7B,MAAA,QAAAA,MAAA,gBAAAU,MAAA;cAAA,OAgB2Bf,MAAA,CAAAC,IAAI,CAACiC,YAAY,GAAAnB,MAAA;YAAA;YAAEC,WAAW,EAAC,UAAU;YAACmB,IAAI,EAAC,UAAU;YAAEC,IAAI,EAAE,CAAC;YAAEnB,SAAS,EAAT;;;QAhB/FS,CAAA;2CAe+D1B,MAAA,CAAAC,IAAI,CAAC2B,MAAM,E,GAGpEhC,YAAA,CAEea,uBAAA;QAFDC,KAAK,EAAC,OAAO;QAACC,IAAI,EAAC,UAAU;QAACpB,KAAK,EAAC;;QAlBxDgB,OAAA,EAAAC,QAAA,CAmBQ;UAAA,OAAiE,CAAjEZ,YAAA,CAAiEyC,0BAAA;YAA/CC,QAAQ,EAAEtC,MAAA,CAAAsC,QAAQ;YAAGC,YAAU,EAAEvC,MAAA,CAAAwC;;;QAnB3Dd,CAAA;UAqBmD1B,MAAA,CAAAyC,aAAa,IAAIzC,MAAA,CAAA0C,YAAY,I,cAA1EhD,mBAAA,CAuBM,OAvBNiD,UAuBM,GAtBJC,mBAAA,CAGM,OAHNC,UAGM,GAFJjD,YAAA,CAAqFkD,mBAAA;QAA1EC,GAAG,EAAE/C,MAAA,CAAAgD,oBAAoB;QAAEC,OAAO,EAAC,MAAM;QAACC,GAAG,EAAC,OAAO;QAACC,SAAS,EAAC;oEAC3EP,mBAAA,CAAoD;QAA/CrD,KAAK,EAAC;MAA0B,GAAC,UAAQ,qB,GAEDS,MAAA,CAAAiD,OAAO,I,cAAtDvD,mBAAA,CAEM,OAFN0D,UAEM,GADJR,mBAAA,CAAsD;QAAjDrD,KAAK,EAAC,eAAe;QAAC8D,SAAoB,EAAZrD,MAAA,CAAAsD;cA3B7CC,mBAAA,gBA6BqGvD,MAAA,CAAAwD,MAAM,I,cAAnG9D,mBAAA,CAQM;QArCdF,GAAA;QA6BaD,KAAK,EAAC,yCAAyC;QAAEkE,OAAK,EAAApD,MAAA,QAAAA,MAAA,gBAAAU,MAAA;UAAA,OAAEf,MAAA,CAAA0D,UAAU,IAAI1D,MAAA,CAAA0D,UAAU;QAAA;UACnFd,mBAAA,CAA+B;QAA1BS,SAAmB,EAAXrD,MAAA,CAAA2D;MAAU,I,4BA9BjC3B,gBAAA,CA8ByC,SAE/B,IAAYhC,MAAA,CAAAwD,MAAM,Y,cAAlB9D,mBAAA,CAAoD,QAhC9DkE,UAAA,EAgCsC,MAAI,GAAAC,gBAAA,CAAG7D,MAAA,CAAAwD,MAAM,IAAG,GAAC,mBAhCvDD,mBAAA,gBAiCU3D,YAAA,CAGUkE,kBAAA;QApCpBvD,OAAA,EAAAC,QAAA,CAkCY;UAAA,OAAiC,CAAdR,MAAA,CAAA0D,UAAU,I,cAA7BrC,YAAA,CAAiC0C,sBAAA;YAlC7CvE,GAAA;UAAA,MAAA+D,mBAAA,gB,CAmCkCvD,MAAA,CAAA0D,UAAU,I,cAAhCrC,YAAA,CAAoC2C,wBAAA;YAnChDxE,GAAA;UAAA,MAAA+D,mBAAA,e;;QAAA7B,CAAA;cAAA6B,mBAAA,gB,gBAsCQX,mBAAA,CAEM,OAFNqB,UAEM,GADJrE,YAAA,CAAsGsE,yBAAA;QAAtFpE,GAAG,EAAC,aAAa;QAvC3Ce,UAAA,EAuCqDb,MAAA,CAAAmE,YAAY,CAACC,OAAO;QAvCzE,uBAAA/D,MAAA,QAAAA,MAAA,gBAAAU,MAAA;UAAA,OAuCqDf,MAAA,CAAAmE,YAAY,CAACC,OAAO,GAAArD,MAAA;QAAA;QAAGqD,OAAO,EAAEpE,MAAA,CAAAmE,YAAY,CAACE;4FADnCrE,MAAA,CAAA0D,UAAU,E,GAGjEd,mBAAA,CAEM,OAFN0B,UAEM,GADJ1E,YAAA,CAAoFsE,yBAAA;QAApEpE,GAAG,EAAC,OAAO;QA1CrCe,UAAA,EA0C+Cb,MAAA,CAAAuE,MAAM,CAACH,OAAO;QA1C7D,uBAAA/D,MAAA,QAAAA,MAAA,gBAAAU,MAAA;UAAA,OA0C+Cf,MAAA,CAAAuE,MAAM,CAACH,OAAO,GAAArD,MAAA;QAAA;QAAGqD,OAAO,EAAEpE,MAAA,CAAAuE,MAAM,CAACF;gEA1ChFd,mBAAA,gBA6CM3D,YAAA,CAEea,uBAAA;QAFDC,KAAK,EAAC,IAAI;QAACC,IAAI,EAAC,SAAS;QAACpB,KAAK,EAAC;;QA7CpDgB,OAAA,EAAAC,QAAA,CA8CQ;UAAA,OAAwC,CAAxCZ,YAAA,CAAwC4E,wBAAA;YA9ChD3D,UAAA,EA8CgCb,MAAA,CAAAC,IAAI,CAACmE,OAAO;YA9C5C,uBAAA/D,MAAA,QAAAA,MAAA,gBAAAU,MAAA;cAAA,OA8CgCf,MAAA,CAAAC,IAAI,CAACmE,OAAO,GAAArD,MAAA;YAAA;;;QA9C5CW,CAAA;UAiDc1B,MAAA,CAAAC,IAAI,CAACwE,gBAAgB,IAAIzE,MAAA,CAAAC,IAAI,CAACwE,gBAAgB,CAACC,MAAM,Q,cAD7DrD,YAAA,CAiBeZ,uBAAA;QAjErBjB,GAAA;QAgDoBkB,KAAK,EAAC,UAAU;QAACnB,KAAK,EAAC,iBAAiB;QAACoB,IAAI,EAAC;;QAhDlEJ,OAAA,EAAAC,QAAA,CAkDa;UAAA,OAA8C,E,kBAAnDd,mBAAA,CAcMwB,SAAA,QAhEdC,WAAA,CAkDqCnB,MAAA,CAAAC,IAAI,CAACwE,gBAAgB,EAlD1D,UAkDqBrD,IAAI,EAAEuD,KAAK;iCAAxBjF,mBAAA,CAcM;cAd+CF,GAAG,EAAEmF,KAAK;cAAEpF,KAAK,EAAC;gBACrEqD,mBAAA,CAA+C,OAA/CgC,UAA+C,EAAvB,IAAE,GAAAf,gBAAA,CAAGc,KAAK,sBAClC/B,mBAAA,CAAiD,OAAjDiC,UAAiD,EAAAhB,gBAAA,CAArBzC,IAAI,CAACgD,OAAO,kBACxCxB,mBAAA,CAUM,OAVNkC,WAUM,GATJlF,YAAA,CAIea,uBAAA;cAJDC,KAAK,EAAC,MAAM;cAACC,IAAI,EAAC,oBAAoB;cAACpB,KAAK,EAAC;;cAtDvEgB,OAAA,EAAAC,QAAA,CAuDc;gBAAA,OAEY,CAFZZ,YAAA,CAEYgB,oBAAA;kBAzD1BC,UAAA,EAuDkCO,IAAI,CAAC2D,kBAAkB;kBAvDzD,gCAAAC,mBAAAjE,MAAA;oBAAA,OAuDkCK,IAAI,CAAC2D,kBAAkB,GAAAhE,MAAA;kBAAA;kBAAEC,WAAW,EAAC,SAAS;kBAACC,SAAS,EAAT;;kBAvDjFV,OAAA,EAAAC,QAAA,CAwD2B;oBAAA,OAAmC,E,kBAA9Cd,mBAAA,CAAuGwB,SAAA,QAxDvHC,WAAA,CAwD2CnB,MAAA,CAAA+E,kBAAkB,EAxD7D,UAwDkCE,KAAK;2CAAvB5D,YAAA,CAAuGC,oBAAA;wBAAvD9B,GAAG,EAAEyF,KAAK,CAAC1D,EAAE;wBAAGb,KAAK,EAAEuE,KAAK,CAACzD,IAAI;wBAAGC,KAAK,EAAEwD,KAAK,CAAC1D;;;;kBAxDjHG,CAAA;;;cAAAA,CAAA;0CA2DY9B,YAAA,CAGea,uBAAA;cAHDC,KAAK,EAAC,MAAM;cAACC,IAAI,EAAC,uBAAuB;cAAClB,KAAyB,EAAzB;gBAAA;cAAA,CAAyB;cAACF,KAAK,EAAC;;cA3DpGgB,OAAA,EAAAC,QAAA,CA4Dc;gBAAA,OACc,CADdZ,YAAA,CACcqC,mBAAA;kBA7D5BpB,UAAA,EA4DiCO,IAAI,CAAC8D,qBAAqB;kBA5D3D,gCAAAF,mBAAAjE,MAAA;oBAAA,OA4DiCK,IAAI,CAAC8D,qBAAqB,GAAAnE,MAAA;kBAAA;kBAAEC,WAAW,EAAC,SAAS;kBAACmB,IAAI,EAAC,UAAU;kBAAEC,IAAI,EAAE,CAAC;kBAC3FnB,SAAS,EAAT;;;cA7DhBS,CAAA;;;;QAAAA,CAAA;YAAA6B,mBAAA,gBAkEMX,mBAAA,CAGM,OAHNuC,WAGM,GAFJvF,YAAA,CAAqEwF,oBAAA;QAA1DjD,IAAI,EAAC,SAAS;QAAEsB,OAAK,EAAApD,MAAA,QAAAA,MAAA,gBAAAU,MAAA;UAAA,OAAEf,MAAA,CAAAqF,UAAU,CAACrF,MAAA,CAAAsF,OAAO;QAAA;;QAnE5D/E,OAAA,EAAAC,QAAA,CAmE+D;UAAA,OAAEH,MAAA,SAAAA,MAAA,QAnEjE2B,gBAAA,CAmE+D,IAAE,E;;QAnEjEN,CAAA;UAoEQ9B,YAAA,CAA4CwF,oBAAA;QAAhC3B,OAAK,EAAEzD,MAAA,CAAAuF;MAAS;QApEpChF,OAAA,EAAAC,QAAA,CAoEsC;UAAA,OAAEH,MAAA,SAAAA,MAAA,QApExC2B,gBAAA,CAoEsC,IAAE,E;;QApExCN,CAAA;;;IAAAA,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}