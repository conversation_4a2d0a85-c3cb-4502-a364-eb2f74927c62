{"ast": null, "code": "import { toDisplayString as _toDisplayString, createElementVNode as _createElementVNode, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createBlock as _createBlock, createVNode as _createVNode, createCommentVNode as _createCommentVNode, vShow as _vShow, withDirectives as _withDirectives } from \"vue\";\nvar _hoisted_1 = {\n  class: \"SuggestReviewDetail\"\n};\nvar _hoisted_2 = {\n  class: \"SuggestReviewDetailNameBody\"\n};\nvar _hoisted_3 = {\n  class: \"SuggestReviewDetailName\"\n};\nvar _hoisted_4 = {\n  class: \"globalFormButton\"\n};\nvar _hoisted_5 = {\n  key: 4,\n  class: \"globalFormButton\",\n  style: {\n    \"margin-top\": \"10px\"\n  }\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_radio = _resolveComponent(\"el-radio\");\n  var _component_el_radio_group = _resolveComponent(\"el-radio-group\");\n  var _component_intelligent_assistant = _resolveComponent(\"intelligent-assistant\");\n  var _component_el_form_item = _resolveComponent(\"el-form-item\");\n  var _component_el_option = _resolveComponent(\"el-option\");\n  var _component_el_select = _resolveComponent(\"el-select\");\n  var _component_el_input = _resolveComponent(\"el-input\");\n  var _component_suggest_simple_select_unit = _resolveComponent(\"suggest-simple-select-unit\");\n  var _component_el_button = _resolveComponent(\"el-button\");\n  var _component_el_form = _resolveComponent(\"el-form\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"div\", null, _toDisplayString($setup.props.name), 1 /* TEXT */)])]), _createVNode(_component_el_form, {\n    ref: \"formRef\",\n    model: $setup.form,\n    rules: $setup.rules,\n    inline: \"\",\n    \"label-position\": \"top\",\n    class: \"globalForm\"\n  }, {\n    default: _withCtx(function () {\n      return [_withDirectives(_createVNode(_component_el_form_item, {\n        label: \"审查结果\",\n        prop: \"reviewResult\",\n        class: \"globalFormTitle\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_el_radio_group, {\n            modelValue: $setup.form.reviewResult,\n            \"onUpdate:modelValue\": _cache[0] || (_cache[0] = function ($event) {\n              return $setup.form.reviewResult = $event;\n            }),\n            onChange: $setup.reviewResultChange\n          }, {\n            default: _withCtx(function () {\n              return [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.reviewResult, function (item) {\n                return _openBlock(), _createBlock(_component_el_radio, {\n                  disabled: $setup.props.isLock,\n                  key: item.nodeId,\n                  label: item.nodeId\n                }, {\n                  default: _withCtx(function () {\n                    return [_createTextVNode(_toDisplayString(item.nodeName), 1 /* TEXT */)];\n                  }),\n                  _: 2 /* DYNAMIC */\n                }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"disabled\", \"label\"]);\n              }), 128 /* KEYED_FRAGMENT */))];\n            }),\n            _: 1 /* STABLE */\n          }, 8 /* PROPS */, [\"modelValue\"]), $setup.whetherUseIntelligentize ? (_openBlock(), _createBlock(_component_intelligent_assistant, {\n            key: 0,\n            elIsShow: $setup.elTypeShow,\n            \"onUpdate:elIsShow\": _cache[1] || (_cache[1] = function ($event) {\n              return $setup.elTypeShow = $event;\n            }),\n            modelValue: $setup.visibleTypeShow,\n            \"onUpdate:modelValue\": _cache[2] || (_cache[2] = function ($event) {\n              return $setup.visibleTypeShow = $event;\n            })\n          }, {\n            default: _withCtx(function () {\n              return [_createVNode($setup[\"SuggestRecommendType\"], {\n                id: $setup.props.id,\n                content: $setup.props.content,\n                onCallback: $setup.typeCallback,\n                onSelect: $setup.typeSelect\n              }, null, 8 /* PROPS */, [\"id\", \"content\"])];\n            }),\n            _: 1 /* STABLE */\n          }, 8 /* PROPS */, [\"elIsShow\", \"modelValue\"])) : _createCommentVNode(\"v-if\", true)];\n        }),\n        _: 1 /* STABLE */\n      }, 512 /* NEED_PATCH */), [[_vShow, $setup.props.signId != '1']]), _withDirectives(_createVNode(_component_el_form_item, {\n        label: `${$setup.rejectName}理由`,\n        prop: \"rejectReason\",\n        class: \"globalFormTitle\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_el_select, {\n            modelValue: $setup.form.rejectReason,\n            \"onUpdate:modelValue\": _cache[3] || (_cache[3] = function ($event) {\n              return $setup.form.rejectReason = $event;\n            }),\n            placeholder: `请选择${$setup.rejectName}理由`,\n            clearable: \"\"\n          }, {\n            default: _withCtx(function () {\n              return [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.suggestionRejectReason, function (item) {\n                return _openBlock(), _createBlock(_component_el_option, {\n                  key: item.key,\n                  label: item.name,\n                  value: item.key\n                }, null, 8 /* PROPS */, [\"label\", \"value\"]);\n              }), 128 /* KEYED_FRAGMENT */))];\n            }),\n            _: 1 /* STABLE */\n          }, 8 /* PROPS */, [\"modelValue\", \"placeholder\"])];\n        }),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"label\"]), [[_vShow, $setup.isReviewResult === 'noAccept' && $setup.props.signId != '1' && $setup.props.signId != '2']]), _createVNode(_component_el_form_item, {\n        label: \"提案大类\",\n        prop: \"SuggestBigType\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_el_select, {\n            modelValue: $setup.form.SuggestBigType,\n            \"onUpdate:modelValue\": _cache[4] || (_cache[4] = function ($event) {\n              return $setup.form.SuggestBigType = $event;\n            }),\n            placeholder: \"请选择提案大类\",\n            onChange: $setup.SuggestBigTypeChange,\n            disabled: $setup.props.isLock,\n            clearable: \"\"\n          }, {\n            default: _withCtx(function () {\n              return [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.SuggestBigType, function (item) {\n                return _openBlock(), _createBlock(_component_el_option, {\n                  key: item.id,\n                  label: item.name,\n                  value: item.id\n                }, null, 8 /* PROPS */, [\"label\", \"value\"]);\n              }), 128 /* KEYED_FRAGMENT */))];\n            }),\n            _: 1 /* STABLE */\n          }, 8 /* PROPS */, [\"modelValue\", \"disabled\"])];\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"提案小类\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_el_select, {\n            modelValue: $setup.form.SuggestSmallType,\n            \"onUpdate:modelValue\": _cache[5] || (_cache[5] = function ($event) {\n              return $setup.form.SuggestSmallType = $event;\n            }),\n            placeholder: \"请选择提案小类\",\n            disabled: $setup.props.isLock,\n            clearable: \"\"\n          }, {\n            default: _withCtx(function () {\n              return [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.SuggestSmallType, function (item) {\n                return _openBlock(), _createBlock(_component_el_option, {\n                  key: item.id,\n                  label: item.name,\n                  value: item.id\n                }, null, 8 /* PROPS */, [\"label\", \"value\"]);\n              }), 128 /* KEYED_FRAGMENT */))];\n            }),\n            _: 1 /* STABLE */\n          }, 8 /* PROPS */, [\"modelValue\", \"disabled\"])];\n        }),\n        _: 1 /* STABLE */\n      }), $setup.props.signId != '1' ? (_openBlock(), _createBlock(_component_el_form_item, {\n        key: 0,\n        label: \"审查意见\",\n        class: \"globalFormTitle\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_el_input, {\n            modelValue: $setup.form.reviewOpinion,\n            \"onUpdate:modelValue\": _cache[6] || (_cache[6] = function ($event) {\n              return $setup.form.reviewOpinion = $event;\n            }),\n            placeholder: \"请输入审查意见\",\n            disabled: $setup.props.isLock,\n            type: \"textarea\",\n            rows: 5,\n            clearable: \"\"\n          }, null, 8 /* PROPS */, [\"modelValue\", \"disabled\"])];\n        }),\n        _: 1 /* STABLE */\n      })) : _createCommentVNode(\"v-if\", true), $setup.isReviewResult === 'success' && $setup.props.signId != '1' ? (_openBlock(), _createBlock(_component_el_form_item, {\n        key: 1,\n        label: \" 办理方式\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_el_select, {\n            modelValue: $setup.form.transactType,\n            \"onUpdate:modelValue\": _cache[7] || (_cache[7] = function ($event) {\n              return $setup.form.transactType = $event;\n            }),\n            placeholder: \"请选择办理方式\",\n            onChange: $setup.transactTypeChange,\n            clearable: \"\"\n          }, {\n            default: _withCtx(function () {\n              return [_createVNode(_component_el_option, {\n                label: \"主办\",\n                value: \"main_assist\"\n              }), _createVNode(_component_el_option, {\n                label: \"分办\",\n                value: \"publish\"\n              })];\n            }),\n            _: 1 /* STABLE */\n          }, 8 /* PROPS */, [\"modelValue\"])];\n        }),\n        _: 1 /* STABLE */\n      })) : _createCommentVNode(\"v-if\", true), $setup.isReviewResult === 'success' && $setup.form.transactType === 'main_assist' && $setup.props.signId != '1' ? (_openBlock(), _createBlock(_component_el_form_item, {\n        key: 2,\n        label: \"主办单位\",\n        prop: \"mainHandleOfficeId\",\n        class: \"globalFormTitle\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_suggest_simple_select_unit, {\n            modelValue: $setup.form.mainHandleOfficeId,\n            \"onUpdate:modelValue\": _cache[8] || (_cache[8] = function ($event) {\n              return $setup.form.mainHandleOfficeId = $event;\n            }),\n            filterId: $setup.form.handleOfficeIds,\n            max: 1\n          }, null, 8 /* PROPS */, [\"modelValue\", \"filterId\"]), $setup.whetherUseIntelligentize ? (_openBlock(), _createBlock(_component_intelligent_assistant, {\n            key: 0,\n            elIsShow: $setup.elUnitShow,\n            \"onUpdate:elIsShow\": _cache[9] || (_cache[9] = function ($event) {\n              return $setup.elUnitShow = $event;\n            }),\n            modelValue: $setup.visibleUnitShow,\n            \"onUpdate:modelValue\": _cache[10] || (_cache[10] = function ($event) {\n              return $setup.visibleUnitShow = $event;\n            })\n          }, {\n            default: _withCtx(function () {\n              return [_createVNode($setup[\"SuggestRecommendUnit\"], {\n                params: $setup.unitParams,\n                onCallback: $setup.unitCallback,\n                onSelect: $setup.unitSelect\n              }, null, 8 /* PROPS */, [\"params\"])];\n            }),\n            _: 1 /* STABLE */\n          }, 8 /* PROPS */, [\"elIsShow\", \"modelValue\"])) : _createCommentVNode(\"v-if\", true)];\n        }),\n        _: 1 /* STABLE */\n      })) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" <template v-if=\\\"isReviewResult === 'success' && form.transactType === 'main_assist' && props.signId != '1'\\\">\\r\\n        <el-form-item label=\\\"协办单位\\\" class=\\\"globalFormTitle\\\">\\r\\n          <suggest-simple-select-unit v-model=\\\"form.handleOfficeIds\\\"\\r\\n            :filterId=\\\"form.mainHandleOfficeId\\\"></suggest-simple-select-unit>\\r\\n        </el-form-item>\\r\\n      </template> \"), $setup.isReviewResult === 'success' && $setup.form.transactType === 'publish' && $setup.props.signId != '1' ? (_openBlock(), _createBlock(_component_el_form_item, {\n        key: 3,\n        label: \"分办单位\",\n        prop: \"handleOfficeIds\",\n        class: \"globalFormTitle\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_suggest_simple_select_unit, {\n            modelValue: $setup.form.handleOfficeIds,\n            \"onUpdate:modelValue\": _cache[11] || (_cache[11] = function ($event) {\n              return $setup.form.handleOfficeIds = $event;\n            })\n          }, null, 8 /* PROPS */, [\"modelValue\"]), $setup.whetherUseIntelligentize ? (_openBlock(), _createBlock(_component_intelligent_assistant, {\n            key: 0,\n            elIsShow: $setup.elUnitShow,\n            \"onUpdate:elIsShow\": _cache[12] || (_cache[12] = function ($event) {\n              return $setup.elUnitShow = $event;\n            }),\n            modelValue: $setup.visibleUnitShow,\n            \"onUpdate:modelValue\": _cache[13] || (_cache[13] = function ($event) {\n              return $setup.visibleUnitShow = $event;\n            })\n          }, {\n            default: _withCtx(function () {\n              return [_createVNode($setup[\"SuggestRecommendUnit\"], {\n                params: $setup.unitParams,\n                onCallback: $setup.unitCallback,\n                onSelect: $setup.unitSelect\n              }, null, 8 /* PROPS */, [\"params\"])];\n            }),\n            _: 1 /* STABLE */\n          }, 8 /* PROPS */, [\"elIsShow\", \"modelValue\"])) : _createCommentVNode(\"v-if\", true)];\n        }),\n        _: 1 /* STABLE */\n      })) : _createCommentVNode(\"v-if\", true), _createVNode($setup[\"ReviewSimilarityQuery\"], {\n        id: $setup.props.id,\n        content: $setup.props.content,\n        onCallback: $setup.resetForm\n      }, null, 8 /* PROPS */, [\"id\", \"content\"]), _createElementVNode(\"div\", _hoisted_4, [_createVNode(_component_el_button, {\n        type: \"primary\",\n        disabled: $setup.props.isLock,\n        onClick: _cache[14] || (_cache[14] = function ($event) {\n          return $setup.submitForm($setup.formRef);\n        })\n      }, {\n        default: _withCtx(function () {\n          return _cache[15] || (_cache[15] = [_createTextVNode(\"提交\")]);\n        }),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"disabled\"]), _createVNode(_component_el_button, {\n        onClick: $setup.resetForm\n      }, {\n        default: _withCtx(function () {\n          return _cache[16] || (_cache[16] = [_createTextVNode(\"取消\")]);\n        }),\n        _: 1 /* STABLE */\n      })]), $setup.props.isLock ? (_openBlock(), _createElementBlock(\"div\", _hoisted_5, [_createElementVNode(\"div\", null, \" 提案已被锁定（由\" + _toDisplayString($setup.props.lockVo.lockUserName) + \"于\" + _toDisplayString($setup.format($setup.props.lockVo.lockDate)) + \"） 锁定 \", 1 /* TEXT */)])) : _createCommentVNode(\"v-if\", true)];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"model\", \"rules\"])]);\n}", "map": {"version": 3, "names": ["class", "key", "style", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_toDisplayString", "$setup", "props", "name", "_createVNode", "_component_el_form", "ref", "model", "form", "rules", "inline", "default", "_withCtx", "_component_el_form_item", "label", "prop", "_component_el_radio_group", "modelValue", "reviewResult", "_cache", "$event", "onChange", "reviewResultChange", "_Fragment", "_renderList", "item", "_createBlock", "_component_el_radio", "disabled", "isLock", "nodeId", "_createTextVNode", "nodeName", "_", "whetherUseIntelligentize", "_component_intelligent_assistant", "elIsShow", "elTypeShow", "visibleTypeShow", "id", "content", "onCallback", "typeCallback", "onSelect", "typeSelect", "_createCommentVNode", "signId", "rejectName", "_component_el_select", "rejectReason", "placeholder", "clearable", "suggestionRejectReason", "_component_el_option", "value", "isReviewResult", "SuggestBigType", "SuggestBigTypeChange", "SuggestSmallType", "_component_el_input", "reviewOpinion", "type", "rows", "transactType", "transactTypeChange", "_component_suggest_simple_select_unit", "mainHandleOfficeId", "filterId", "handleOfficeIds", "max", "elUnitShow", "visibleUnitShow", "params", "unitParams", "unitCallback", "unitSelect", "resetForm", "_hoisted_4", "_component_el_button", "onClick", "submitForm", "formRef", "_hoisted_5", "lockVo", "lockUserName", "format", "lockDate"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestReview\\component\\SuggestReviewDetail.vue"], "sourcesContent": ["<template>\r\n  <div class=\"SuggestReviewDetail\">\r\n    <div class=\"SuggestReviewDetailNameBody\">\r\n      <div class=\"SuggestReviewDetailName\">\r\n        <div>{{ props.name }}</div>\r\n      </div>\r\n    </div>\r\n    <el-form ref=\"formRef\" :model=\"form\" :rules=\"rules\" inline label-position=\"top\" class=\"globalForm\">\r\n      <el-form-item label=\"审查结果\" prop=\"reviewResult\" class=\"globalFormTitle\" v-show=\"props.signId != '1'\">\r\n        <el-radio-group v-model=\"form.reviewResult\" @change=\"reviewResultChange\">\r\n          <el-radio v-for=\"item in reviewResult\" :disabled=\"props.isLock\" :key=\"item.nodeId\" :label=\"item.nodeId\">{{\r\n            item.nodeName }}</el-radio>\r\n        </el-radio-group>\r\n        <template v-if=\"whetherUseIntelligentize\">\r\n          <intelligent-assistant v-model:elIsShow=\"elTypeShow\" v-model=\"visibleTypeShow\">\r\n            <SuggestRecommendType :id=\"props.id\" :content=\"props.content\" @callback=\"typeCallback\" @select=\"typeSelect\">\r\n            </SuggestRecommendType>\r\n          </intelligent-assistant>\r\n        </template>\r\n      </el-form-item>\r\n      <el-form-item :label=\"`${rejectName}理由`\"\r\n        v-show=\"isReviewResult === 'noAccept' && props.signId != '1' && props.signId != '2'\" prop=\"rejectReason\"\r\n        class=\"globalFormTitle\">\r\n        <el-select v-model=\"form.rejectReason\" :placeholder=\"`请选择${rejectName}理由`\" clearable>\r\n          <el-option v-for=\"item in suggestionRejectReason\" :key=\"item.key\" :label=\"item.name\" :value=\"item.key\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"提案大类\" prop=\"SuggestBigType\">\r\n        <el-select v-model=\"form.SuggestBigType\" placeholder=\"请选择提案大类\" @change=\"SuggestBigTypeChange\"\r\n          :disabled=\"props.isLock\" clearable>\r\n          <el-option v-for=\"item in SuggestBigType\" :key=\"item.id\" :label=\"item.name\" :value=\"item.id\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"提案小类\">\r\n        <el-select v-model=\"form.SuggestSmallType\" placeholder=\"请选择提案小类\" :disabled=\"props.isLock\" clearable>\r\n          <el-option v-for=\"item in SuggestSmallType\" :key=\"item.id\" :label=\"item.name\" :value=\"item.id\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"审查意见\" class=\"globalFormTitle\" v-if=\"props.signId != '1'\">\r\n        <el-input v-model=\"form.reviewOpinion\" placeholder=\"请输入审查意见\" :disabled=\"props.isLock\" type=\"textarea\" :rows=\"5\"\r\n          clearable />\r\n      </el-form-item>\r\n      <template v-if=\"isReviewResult === 'success' && props.signId != '1'\">\r\n        <el-form-item label=\" 办理方式\">\r\n          <el-select v-model=\"form.transactType\" placeholder=\"请选择办理方式\" @change=\"transactTypeChange\" clearable>\r\n            <el-option label=\"主办\" value=\"main_assist\" />\r\n            <el-option label=\"分办\" value=\"publish\" />\r\n          </el-select>\r\n        </el-form-item>\r\n      </template>\r\n      <template v-if=\"isReviewResult === 'success' && form.transactType === 'main_assist' && props.signId != '1'\">\r\n        <el-form-item label=\"主办单位\" prop=\"mainHandleOfficeId\" class=\"globalFormTitle\">\r\n          <suggest-simple-select-unit v-model=\"form.mainHandleOfficeId\" :filterId=\"form.handleOfficeIds\"\r\n            :max=\"1\"></suggest-simple-select-unit>\r\n          <template v-if=\"whetherUseIntelligentize\">\r\n            <intelligent-assistant v-model:elIsShow=\"elUnitShow\" v-model=\"visibleUnitShow\">\r\n              <SuggestRecommendUnit :params=\"unitParams\" @callback=\"unitCallback\" @select=\"unitSelect\">\r\n              </SuggestRecommendUnit>\r\n            </intelligent-assistant>\r\n          </template>\r\n        </el-form-item>\r\n      </template>\r\n      <!-- <template v-if=\"isReviewResult === 'success' && form.transactType === 'main_assist' && props.signId != '1'\">\r\n        <el-form-item label=\"协办单位\" class=\"globalFormTitle\">\r\n          <suggest-simple-select-unit v-model=\"form.handleOfficeIds\"\r\n            :filterId=\"form.mainHandleOfficeId\"></suggest-simple-select-unit>\r\n        </el-form-item>\r\n      </template> -->\r\n      <template v-if=\"isReviewResult === 'success' && form.transactType === 'publish' && props.signId != '1'\">\r\n        <el-form-item label=\"分办单位\" prop=\"handleOfficeIds\" class=\"globalFormTitle\">\r\n          <suggest-simple-select-unit v-model=\"form.handleOfficeIds\"></suggest-simple-select-unit>\r\n          <template v-if=\"whetherUseIntelligentize\">\r\n            <intelligent-assistant v-model:elIsShow=\"elUnitShow\" v-model=\"visibleUnitShow\">\r\n              <SuggestRecommendUnit :params=\"unitParams\" @callback=\"unitCallback\" @select=\"unitSelect\">\r\n              </SuggestRecommendUnit>\r\n            </intelligent-assistant>\r\n          </template>\r\n        </el-form-item>\r\n      </template>\r\n      <ReviewSimilarityQuery :id=\"props.id\" :content=\"props.content\" @callback=\"resetForm\"></ReviewSimilarityQuery>\r\n      <div class=\"globalFormButton\">\r\n        <el-button type=\"primary\" :disabled=\"props.isLock\" @click=\"submitForm(formRef)\">提交</el-button>\r\n        <el-button @click=\"resetForm\">取消</el-button>\r\n      </div>\r\n      <div class=\"globalFormButton\" style=\"margin-top: 10px;\" v-if=\"props.isLock\">\r\n        <div> 提案已被锁定（由{{ props.lockVo.lockUserName }}于{{ format(props.lockVo.lockDate) }}） 锁定 </div>\r\n      </div>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'SuggestReviewDetail' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { reactive, ref, onActivated, onDeactivated, onBeforeUnmount, watch } from 'vue'\r\nimport { whetherUseIntelligentize } from 'common/js/system_var.js'\r\nimport { qiankunMicro } from 'common/config/MicroGlobal'\r\nimport { ElMessage } from 'element-plus'\r\nimport { format } from 'common/js/time.js'\r\nimport SuggestRecommendType from '@/components/SuggestRecommendType/SuggestRecommendType.vue'\r\nimport SuggestRecommendUnit from '@/components/SuggestRecommendUnit/SuggestRecommendUnit.vue'\r\nimport ReviewSimilarityQuery from './ReviewSimilarityQuery'\r\nconst props = defineProps({\r\n  id: { type: String, default: '' },\r\n  name: { type: String, default: '提案审查' },\r\n  content: { type: String, default: '' },\r\n  SuggestBigType: { type: String, default: '' },\r\n  SuggestSmallType: { type: String, default: '' },\r\n  isLock: { type: Boolean, default: false },\r\n  hopeHandleOfficeIds: { type: Array, default: () => ([]) },\r\n  lockVo: { type: Object, default: () => ({}) },\r\n  queryType: { type: String, default: '' },\r\n  signId: { type: String, default: '' },\r\n  bigThemeId: { type: String, default: '' },\r\n  smallThemeId: { type: String, default: '' },\r\n  jordan: { type: String, default: '' },\r\n  james: { type: String, default: '' },\r\n  kobe: { type: String, default: '' },\r\n  duncan: { type: String, default: '' },\r\n  wade: { type: String, default: '' }\r\n})\r\nconst emit = defineEmits(['callback'])\r\nconst isReviewResult = ref('')\r\nconst formRef = ref()\r\nconst form = reactive({\r\n  reviewResult: '', // 审查结果\r\n  rejectReason: '',\r\n  SuggestBigType: '', // 提案大类\r\n  SuggestSmallType: '', // 提案小类\r\n  reviewOpinion: '', // 审查意见\r\n  transactType: '', // 请选择办理方式\r\n  mainHandleOfficeId: [],\r\n  handleOfficeIds: []\r\n})\r\nconst rules = reactive({\r\n  reviewResult: [{ required: true, message: '请选择审查结果', trigger: ['blur', 'change'] }],\r\n  rejectReason: [{ required: false, message: '请选择不予接收理由', trigger: ['blur', 'change'] }],\r\n  SuggestBigType: [{ required: true, message: '请选择提案大类', trigger: ['blur', 'change'] }],\r\n  mainHandleOfficeId: [{ type: 'array', required: false, message: '请选择主办单位', trigger: ['blur', 'change'] }],\r\n  handleOfficeIds: [{ type: 'array', required: false, message: '请选择分办单位', trigger: ['blur', 'change'] }]\r\n})\r\nconst unitParams = ref({})\r\nconst reviewResult = ref([])\r\nconst SuggestBigType = ref([])\r\nconst SuggestSmallType = ref([])\r\nconst suggestionRejectReason = ref([])\r\nconst rejectName = ref('')\r\n\r\nconst elTypeShow = ref(false)\r\nconst visibleTypeShow = ref(false)\r\nconst elUnitShow = ref(false)\r\nconst visibleUnitShow = ref(false)\r\nconst suggestId = ref('')\r\n// const elAiChatClass = AiChatClass()\r\nonActivated(() => {\r\n  form.SuggestBigType = props.bigThemeId\r\n  form.SuggestSmallType = props.smallThemeId\r\n  form.reviewOpinion = props.james || ''\r\n  form.reviewResult = props.jordan || ''\r\n  if (form.reviewResult == 'prepareSubmitHandle') {\r\n    isReviewResult.value = 'success'\r\n  }\r\n  form.transactType = props.kobe || ''\r\n  form.mainHandleOfficeId = [props.duncan] || ''\r\n  console.log('props.wade==>', props.wade)\r\n  setTimeout(() => {\r\n    form.handleOfficeIds = [props.wade] || ''\r\n  }, 2000);\r\n  qiankunMicro.setGlobalState({ AiChatCode: 'ai-assistant-chat' })\r\n  qiankunMicro.setGlobalState({ AiChatWindow: true })\r\n  qiankunMicro.setGlobalState({ AiChatParams: { businessId: props.id } })\r\n  dictionaryData()\r\n  suggestionThemeSelect()\r\n  if (props.id) {\r\n    suggestionNextNodes()\r\n    suggestId.value = props.id\r\n  }\r\n  window.addEventListener('beforeunload', (e) => { suggestionUnlock() })\r\n  if (props.signId == '1') {\r\n    delete rules.reviewResult\r\n    delete rules.rejectReason\r\n  }\r\n})\r\n\r\nconst typeCallback = (isElIsShow, isVisibleIsShow) => {\r\n  if (props.signId != '1') {\r\n    elTypeShow.value = isElIsShow\r\n    visibleTypeShow.value = isVisibleIsShow\r\n    return\r\n  }\r\n}\r\nconst typeSelect = (item, id) => {\r\n  if (id) {\r\n    form.SuggestBigType = id\r\n    SuggestBigTypeChange()\r\n    form.SuggestSmallType = item._id\r\n  } else {\r\n    form.SuggestBigType = item._id\r\n    SuggestBigTypeChange()\r\n  }\r\n}\r\nconst unitCallback = (isElIsShow, isVisibleIsShow) => {\r\n  elUnitShow.value = isElIsShow\r\n  visibleUnitShow.value = isVisibleIsShow\r\n}\r\nconst unitSelect = (item) => {\r\n  if (form.transactType === 'main_assist') {\r\n    if (!form.mainHandleOfficeId.length) {\r\n      if (!form.handleOfficeIds.includes(item.id)) {\r\n        form.mainHandleOfficeId = [item.id]\r\n        ElMessage({ type: 'success', message: `已为您将【${item.name}】添加到主办单位` })\r\n      }\r\n    } else {\r\n      if (!form.mainHandleOfficeId.includes(item.id) && !form.handleOfficeIds.includes(item.id)) {\r\n        form.handleOfficeIds = [...form.handleOfficeIds, item.id]\r\n        ElMessage({ type: 'success', message: `当前已选择主办单位，已为您将【${item.name}】添加到协办单位` })\r\n      }\r\n    }\r\n  } else if (form.transactType === 'publish') {\r\n    if (!form.handleOfficeIds.includes(item.id)) {\r\n      form.handleOfficeIds = [...form.handleOfficeIds, item.id]\r\n      ElMessage({ type: 'success', message: `已为您将${item.name}添加到分办单位` })\r\n    }\r\n  }\r\n}\r\n\r\nconst suggestionUnlock = async () => {\r\n  if (props.isLock) return\r\n  const res = await api.suggestionUnlock({ ids: [suggestId.value] })\r\n  var { data } = res\r\n  console.log(data)\r\n}\r\nconst suggestionNextNodes = async () => {\r\n  const res = await api.suggestionNextNodes({ suggestionId: props.id })\r\n  var { data } = res\r\n  if (props.signId == '2') {\r\n    reviewResult.value = [\r\n      {\r\n        \"nodeId\": \"prepareSubmitHandle\",\r\n        \"nodeName\": \"立案\",\r\n        \"formType\": \"success\"\r\n      },\r\n      {\r\n        \"nodeId\": \"exchangeLetter\",\r\n        \"nodeName\": \"转来信\",\r\n        \"formType\": \"other\"\r\n      },\r\n      {\r\n        \"nodeId\": \"exchangeSocial\",\r\n        \"nodeName\": \"转社情民意\",\r\n        \"formType\": \"other\"\r\n      },\r\n      {\r\n        \"nodeId\": \"rejectReceive\",\r\n        \"nodeName\": \"不予立案\",\r\n        \"formType\": \"noAccept\"\r\n      },\r\n      {\r\n        \"nodeId\": \"cancelSuggestion\",\r\n        \"nodeName\": \"撤案\",\r\n        \"formType\": \"other\"\r\n      },\r\n      {\r\n        \"nodeId\": \"returnSubmit\",\r\n        \"nodeName\": \"退回\",\r\n        \"formType\": \"other\"\r\n      }\r\n    ]\r\n  } else {\r\n    reviewResult.value = data\r\n  }\r\n  for (let index = 0; index < data.length; index++) {\r\n    const item = data[index]\r\n    if (item.formType === 'noAccept') {\r\n      rejectName.value = item.nodeName\r\n    }\r\n  }\r\n}\r\nconst reviewResultChange = () => {\r\n  isReviewResult.value = ''\r\n  rules.rejectReason = [{ required: false, message: `请选择${rejectName.value}理由`, trigger: ['blur', 'change'] }]\r\n  for (let index = 0; index < reviewResult.value.length; index++) {\r\n    const item = reviewResult.value[index]\r\n    if (item.nodeId === form.reviewResult) {\r\n      isReviewResult.value = item.formType\r\n      if (item.formType === 'noAccept') {\r\n        rules.rejectReason = [{ required: true, message: `请选择${rejectName.value}理由`, trigger: ['blur', 'change'] }]\r\n      }\r\n    }\r\n  }\r\n}\r\nconst dictionaryData = async () => {\r\n  const res = await api.dictionaryData({ dictCodes: ['suggestion_reject_reason'] })\r\n  var { data } = res\r\n  suggestionRejectReason.value = data.suggestion_reject_reason\r\n}\r\nconst suggestionThemeSelect = async () => {\r\n  const res = await api.suggestionThemeSelect({ query: { isUsing: 1 } })\r\n  var { data } = res\r\n  SuggestBigType.value = data\r\n  SuggestBigTypeChange()\r\n}\r\nconst SuggestBigTypeChange = () => {\r\n  if (form.SuggestBigType) {\r\n    for (let index = 0; index < SuggestBigType.value.length; index++) {\r\n      const item = SuggestBigType.value[index]\r\n      if (item.id === form.SuggestBigType) {\r\n        if (!item.children.map(v => v.id).includes(form.SuggestSmallType)) {\r\n          form.SuggestSmallType = ''\r\n        }\r\n        SuggestSmallType.value = item.children\r\n      }\r\n    }\r\n  } else {\r\n    form.SuggestSmallType = ''\r\n    SuggestSmallType.value = []\r\n  }\r\n}\r\nconst transactTypeChange = () => {\r\n  if (form.transactType === 'main_assist') {\r\n    rules.mainHandleOfficeId = [{ type: 'array', required: true, message: '请选择主办单位', trigger: ['blur', 'change'] }]\r\n    rules.handleOfficeIds = [{ type: 'array', required: false, message: '请选择分办单位', trigger: ['blur', 'change'] }]\r\n  } else if (form.transactType === 'publish') {\r\n    rules.mainHandleOfficeId = [{ type: 'array', required: false, message: '请选择主办单位', trigger: ['blur', 'change'] }]\r\n    rules.handleOfficeIds = [{ type: 'array', required: true, message: '请选择分办单位', trigger: ['blur', 'change'] }]\r\n  } else {\r\n    rules.mainHandleOfficeId = [{ type: 'array', required: false, message: '请选择主办单位', trigger: ['blur', 'change'] }]\r\n    rules.handleOfficeIds = [{ type: 'array', required: false, message: '请选择分办单位', trigger: ['blur', 'change'] }]\r\n  }\r\n}\r\nconst submitForm = async (formEl) => {\r\n  if (!formEl) return\r\n  await formEl.validate((valid, fields) => {\r\n    if (valid) { emit('editCallback', suggestionComplete, form) } else { ElMessage({ type: 'warning', message: '请根据提示信息完善字段内容！' }) }\r\n  })\r\n}\r\n\r\nconst suggestionComplete = async () => {\r\n  const { code } = await api.suggestionComplete({\r\n    suggestionId: props.id,\r\n    nextNodeId: props.signId == '1' ? 'review' : props.signId == '2' ? 'prepareVerify' : form.reviewResult, // 审查结果\r\n    bigThemeId: form.SuggestBigType, // 提案大类\r\n    smallThemeId: form.SuggestSmallType, // 提案小类\r\n    variable: {\r\n      handleContent: form.reviewOpinion, // 审查意见\r\n      spareDict: isReviewResult.value === 'noAccept' ? form.rejectReason : ''\r\n    },\r\n    handleOfficeType: form.transactType, // 办理方式\r\n    mainHandleOfficeId: form.mainHandleOfficeId.join(''), // 主办单位\r\n    handleOfficeIds: form.handleOfficeIds.length > 0 && form.handleOfficeIds[0] ? form.handleOfficeIds : [] // 协办或分办单位\r\n  })\r\n  if (code === 200) {\r\n    ElMessage({ type: 'success', message: '审查成功' })\r\n    emit('callback')\r\n  }\r\n}\r\nconst resetForm = () => { emit('callback') }\r\n\r\nonDeactivated(() => {\r\n  qiankunMicro.setGlobalState({ AiChatWindow: false })\r\n  qiankunMicro.setGlobalState({ AiChatCode: 'test_chat' })\r\n  qiankunMicro.setGlobalState({ AiChatParams: {} })\r\n  qiankunMicro.setGlobalState({ AiChatContent: '' })\r\n  // elAiChatClass.AiChatConfig({ AiChatCode: 'test_chat', AiChatWindow: false })\r\n  // elAiChatClass.AiChatHistory()\r\n  suggestionUnlock()\r\n})\r\nonBeforeUnmount(() => {\r\n  qiankunMicro.setGlobalState({ AiChatWindow: false })\r\n  qiankunMicro.setGlobalState({ AiChatCode: 'test_chat' })\r\n  qiankunMicro.setGlobalState({ AiChatParams: {} })\r\n  qiankunMicro.setGlobalState({ AiChatContent: '' })\r\n  // elAiChatClass.AiChatConfig({ AiChatCode: 'test_chat', AiChatWindow: false })\r\n  // elAiChatClass.AiChatHistory()\r\n  // suggestionUnlock()\r\n})\r\nwatch(() => [props.SuggestBigType, props.SuggestSmallType], () => {\r\n  form.SuggestBigType = props.SuggestBigType\r\n  form.SuggestSmallType = props.SuggestSmallType\r\n  SuggestBigTypeChange()\r\n}, { immediate: true })\r\nwatch(() => [props.hopeHandleOfficeIds, props.content], () => {\r\n  // if (props.content) {\r\n  //   elAiChatClass.AiChatConfig({\r\n  //     AiChatCode: 'ai-assistant-chat',\r\n  //     AiChatModuleId: props.id,\r\n  //     AiChatWindow: true,\r\n  //     AiChatContent: props.content\r\n  //   })\r\n  //   elAiChatClass.AiChatHistory('ai-assistant-chat', props.id)\r\n  // }\r\n  qiankunMicro.setGlobalState({ AiChatContent: props.content })\r\n  const selectUnit = props?.hopeHandleOfficeIds || []\r\n  unitParams.value = { selectUnit: JSON.stringify(selectUnit), content: props.content }\r\n}, { immediate: true })\r\n</script>\r\n<style lang=\"scss\">\r\n.SuggestReviewDetail {\r\n  width: 100%;\r\n  height: 100%;\r\n\r\n  .SuggestReviewDetailNameBody {\r\n    padding: 0 var(--zy-distance-one);\r\n    padding-top: var(--zy-distance-one);\r\n\r\n    .SuggestReviewDetailName {\r\n      width: 100%;\r\n      color: var(--zy-el-color-primary);\r\n      font-size: var(--zy-name-font-size);\r\n      line-height: var(--zy-line-height);\r\n      font-weight: bold;\r\n      position: relative;\r\n      text-align: center;\r\n\r\n      div {\r\n        display: inline-block;\r\n        background-color: #fff;\r\n        position: relative;\r\n        z-index: 2;\r\n        padding: 0 20px;\r\n      }\r\n\r\n      &::after {\r\n        content: \"\";\r\n        position: absolute;\r\n        top: 50%;\r\n        left: 0;\r\n        transform: translateY(-50%);\r\n        width: 100%;\r\n        height: 1px;\r\n        background-color: var(--zy-el-color-primary);\r\n      }\r\n    }\r\n  }\r\n\r\n  .suggest-simple-select-unit {\r\n    box-shadow: 0 0 0 1px var(--zy-el-input-border-color, var(--zy-el-border-color)) inset;\r\n    border-radius: var(--zy-el-input-border-radius, var(--zy-el-border-radius-base));\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAqB;;EACzBA,KAAK,EAAC;AAA6B;;EACjCA,KAAK,EAAC;AAAyB;;EA6E/BA,KAAK,EAAC;AAAkB;;EAhFnCC,GAAA;EAoFWD,KAAK,EAAC,kBAAkB;EAACE,KAAyB,EAAzB;IAAA;EAAA;;;;;;;;;;;;;uBAnFlCC,mBAAA,CAuFM,OAvFNC,UAuFM,GAtFJC,mBAAA,CAIM,OAJNC,UAIM,GAHJD,mBAAA,CAEM,OAFNE,UAEM,GADJF,mBAAA,CAA2B,aAAAG,gBAAA,CAAnBC,MAAA,CAAAC,KAAK,CAACC,IAAI,iB,KAGtBC,YAAA,CAgFUC,kBAAA;IAhFDC,GAAG,EAAC,SAAS;IAAEC,KAAK,EAAEN,MAAA,CAAAO,IAAI;IAAGC,KAAK,EAAER,MAAA,CAAAQ,KAAK;IAAEC,MAAM,EAAN,EAAM;IAAC,gBAAc,EAAC,KAAK;IAAClB,KAAK,EAAC;;IAP1FmB,OAAA,EAAAC,QAAA,CAQM;MAAA,OAWe,C,gBAXfR,YAAA,CAWeS,uBAAA;QAXDC,KAAK,EAAC,MAAM;QAACC,IAAI,EAAC,cAAc;QAACvB,KAAK,EAAC;;QAR3DmB,OAAA,EAAAC,QAAA,CASQ;UAAA,OAGiB,CAHjBR,YAAA,CAGiBY,yBAAA;YAZzBC,UAAA,EASiChB,MAAA,CAAAO,IAAI,CAACU,YAAY;YATlD,uBAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OASiCnB,MAAA,CAAAO,IAAI,CAACU,YAAY,GAAAE,MAAA;YAAA;YAAGC,QAAM,EAAEpB,MAAA,CAAAqB;;YAT7DX,OAAA,EAAAC,QAAA,CAUoB;cAAA,OAA4B,E,kBAAtCjB,mBAAA,CAC6B4B,SAAA,QAXvCC,WAAA,CAUmCvB,MAAA,CAAAiB,YAAY,EAV/C,UAU2BO,IAAI;qCAArBC,YAAA,CAC6BC,mBAAA;kBADWC,QAAQ,EAAE3B,MAAA,CAAAC,KAAK,CAAC2B,MAAM;kBAAGpC,GAAG,EAAEgC,IAAI,CAACK,MAAM;kBAAGhB,KAAK,EAAEW,IAAI,CAACK;;kBAV1GnB,OAAA,EAAAC,QAAA,CAUkH;oBAAA,OACtF,CAX5BmB,gBAAA,CAAA/B,gBAAA,CAWYyB,IAAI,CAACO,QAAQ,iB;;kBAXzBC,CAAA;;;;YAAAA,CAAA;6CAawBhC,MAAA,CAAAiC,wBAAwB,I,cACtCR,YAAA,CAGwBS,gCAAA;YAjBlC1C,GAAA;YAcyC2C,QAAQ,EAAEnC,MAAA,CAAAoC,UAAU;YAd7D,qBAAAlB,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OAcmDnB,MAAA,CAAAoC,UAAU,GAAAjB,MAAA;YAAA;YAd7DH,UAAA,EAcwEhB,MAAA,CAAAqC,eAAe;YAdvF,uBAAAnB,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OAcwEnB,MAAA,CAAAqC,eAAe,GAAAlB,MAAA;YAAA;;YAdvFT,OAAA,EAAAC,QAAA,CAeY;cAAA,OACuB,CADvBR,YAAA,CACuBH,MAAA;gBADAsC,EAAE,EAAEtC,MAAA,CAAAC,KAAK,CAACqC,EAAE;gBAAGC,OAAO,EAAEvC,MAAA,CAAAC,KAAK,CAACsC,OAAO;gBAAGC,UAAQ,EAAExC,MAAA,CAAAyC,YAAY;gBAAGC,QAAM,EAAE1C,MAAA,CAAA2C;;;YAf5GX,CAAA;2DAAAY,mBAAA,e;;QAAAZ,CAAA;0CAQqFhC,MAAA,CAAAC,KAAK,CAAC4C,MAAM,S,mBAY3F1C,YAAA,CAMeS,uBAAA;QANAC,KAAK,KAAKb,MAAA,CAAA8C,UAAU;QACoDhC,IAAI,EAAC,cAAc;QACxGvB,KAAK,EAAC;;QAtBdmB,OAAA,EAAAC,QAAA,CAuBQ;UAAA,OAEY,CAFZR,YAAA,CAEY4C,oBAAA;YAzBpB/B,UAAA,EAuB4BhB,MAAA,CAAAO,IAAI,CAACyC,YAAY;YAvB7C,uBAAA9B,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OAuB4BnB,MAAA,CAAAO,IAAI,CAACyC,YAAY,GAAA7B,MAAA;YAAA;YAAG8B,WAAW,QAAQjD,MAAA,CAAA8C,UAAU;YAAMI,SAAS,EAAT;;YAvBnFxC,OAAA,EAAAC,QAAA,CAwBqB;cAAA,OAAsC,E,kBAAjDjB,mBAAA,CAAyG4B,SAAA,QAxBnHC,WAAA,CAwBoCvB,MAAA,CAAAmD,sBAAsB,EAxB1D,UAwB4B3B,IAAI;qCAAtBC,YAAA,CAAyG2B,oBAAA;kBAAtD5D,GAAG,EAAEgC,IAAI,CAAChC,GAAG;kBAAGqB,KAAK,EAAEW,IAAI,CAACtB,IAAI;kBAAGmD,KAAK,EAAE7B,IAAI,CAAChC;;;;YAxB5GwC,CAAA;;;QAAAA,CAAA;8CAqBgBhC,MAAA,CAAAsD,cAAc,mBAAmBtD,MAAA,CAAAC,KAAK,CAAC4C,MAAM,WAAW7C,MAAA,CAAAC,KAAK,CAAC4C,MAAM,S,GAM9E1C,YAAA,CAKeS,uBAAA;QALDC,KAAK,EAAC,MAAM;QAACC,IAAI,EAAC;;QA3BtCJ,OAAA,EAAAC,QAAA,CA4BQ;UAAA,OAGY,CAHZR,YAAA,CAGY4C,oBAAA;YA/BpB/B,UAAA,EA4B4BhB,MAAA,CAAAO,IAAI,CAACgD,cAAc;YA5B/C,uBAAArC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OA4B4BnB,MAAA,CAAAO,IAAI,CAACgD,cAAc,GAAApC,MAAA;YAAA;YAAE8B,WAAW,EAAC,SAAS;YAAE7B,QAAM,EAAEpB,MAAA,CAAAwD,oBAAoB;YACzF7B,QAAQ,EAAE3B,MAAA,CAAAC,KAAK,CAAC2B,MAAM;YAAEsB,SAAS,EAAT;;YA7BnCxC,OAAA,EAAAC,QAAA,CA8BqB;cAAA,OAA8B,E,kBAAzCjB,mBAAA,CAA+F4B,SAAA,QA9BzGC,WAAA,CA8BoCvB,MAAA,CAAAuD,cAAc,EA9BlD,UA8B4B/B,IAAI;qCAAtBC,YAAA,CAA+F2B,oBAAA;kBAApD5D,GAAG,EAAEgC,IAAI,CAACc,EAAE;kBAAGzB,KAAK,EAAEW,IAAI,CAACtB,IAAI;kBAAGmD,KAAK,EAAE7B,IAAI,CAACc;;;;YA9BnGN,CAAA;;;QAAAA,CAAA;UAiCM7B,YAAA,CAIeS,uBAAA;QAJDC,KAAK,EAAC;MAAM;QAjChCH,OAAA,EAAAC,QAAA,CAkCQ;UAAA,OAEY,CAFZR,YAAA,CAEY4C,oBAAA;YApCpB/B,UAAA,EAkC4BhB,MAAA,CAAAO,IAAI,CAACkD,gBAAgB;YAlCjD,uBAAAvC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OAkC4BnB,MAAA,CAAAO,IAAI,CAACkD,gBAAgB,GAAAtC,MAAA;YAAA;YAAE8B,WAAW,EAAC,SAAS;YAAEtB,QAAQ,EAAE3B,MAAA,CAAAC,KAAK,CAAC2B,MAAM;YAAEsB,SAAS,EAAT;;YAlClGxC,OAAA,EAAAC,QAAA,CAmCqB;cAAA,OAAgC,E,kBAA3CjB,mBAAA,CAAiG4B,SAAA,QAnC3GC,WAAA,CAmCoCvB,MAAA,CAAAyD,gBAAgB,EAnCpD,UAmC4BjC,IAAI;qCAAtBC,YAAA,CAAiG2B,oBAAA;kBAApD5D,GAAG,EAAEgC,IAAI,CAACc,EAAE;kBAAGzB,KAAK,EAAEW,IAAI,CAACtB,IAAI;kBAAGmD,KAAK,EAAE7B,IAAI,CAACc;;;;YAnCrGN,CAAA;;;QAAAA,CAAA;UAsC+DhC,MAAA,CAAAC,KAAK,CAAC4C,MAAM,W,cAArEpB,YAAA,CAGeb,uBAAA;QAzCrBpB,GAAA;QAsCoBqB,KAAK,EAAC,MAAM;QAACtB,KAAK,EAAC;;QAtCvCmB,OAAA,EAAAC,QAAA,CAuCQ;UAAA,OACc,CADdR,YAAA,CACcuD,mBAAA;YAxCtB1C,UAAA,EAuC2BhB,MAAA,CAAAO,IAAI,CAACoD,aAAa;YAvC7C,uBAAAzC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OAuC2BnB,MAAA,CAAAO,IAAI,CAACoD,aAAa,GAAAxC,MAAA;YAAA;YAAE8B,WAAW,EAAC,SAAS;YAAEtB,QAAQ,EAAE3B,MAAA,CAAAC,KAAK,CAAC2B,MAAM;YAAEgC,IAAI,EAAC,UAAU;YAAEC,IAAI,EAAE,CAAC;YAC5GX,SAAS,EAAT;;;QAxCVlB,CAAA;YAAAY,mBAAA,gBA0CsB5C,MAAA,CAAAsD,cAAc,kBAAkBtD,MAAA,CAAAC,KAAK,CAAC4C,MAAM,W,cAC1DpB,YAAA,CAKeb,uBAAA;QAhDvBpB,GAAA;QA2CsBqB,KAAK,EAAC;;QA3C5BH,OAAA,EAAAC,QAAA,CA4CU;UAAA,OAGY,CAHZR,YAAA,CAGY4C,oBAAA;YA/CtB/B,UAAA,EA4C8BhB,MAAA,CAAAO,IAAI,CAACuD,YAAY;YA5C/C,uBAAA5C,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OA4C8BnB,MAAA,CAAAO,IAAI,CAACuD,YAAY,GAAA3C,MAAA;YAAA;YAAE8B,WAAW,EAAC,SAAS;YAAE7B,QAAM,EAAEpB,MAAA,CAAA+D,kBAAkB;YAAEb,SAAS,EAAT;;YA5CpGxC,OAAA,EAAAC,QAAA,CA6CY;cAAA,OAA4C,CAA5CR,YAAA,CAA4CiD,oBAAA;gBAAjCvC,KAAK,EAAC,IAAI;gBAACwC,KAAK,EAAC;kBAC5BlD,YAAA,CAAwCiD,oBAAA;gBAA7BvC,KAAK,EAAC,IAAI;gBAACwC,KAAK,EAAC;;;YA9CxCrB,CAAA;;;QAAAA,CAAA;YAAAY,mBAAA,gBAkDsB5C,MAAA,CAAAsD,cAAc,kBAAkBtD,MAAA,CAAAO,IAAI,CAACuD,YAAY,sBAAsB9D,MAAA,CAAAC,KAAK,CAAC4C,MAAM,W,cACjGpB,YAAA,CASeb,uBAAA;QA5DvBpB,GAAA;QAmDsBqB,KAAK,EAAC,MAAM;QAACC,IAAI,EAAC,oBAAoB;QAACvB,KAAK,EAAC;;QAnDnEmB,OAAA,EAAAC,QAAA,CAoDU;UAAA,OACwC,CADxCR,YAAA,CACwC6D,qCAAA;YArDlDhD,UAAA,EAoD+ChB,MAAA,CAAAO,IAAI,CAAC0D,kBAAkB;YApDtE,uBAAA/C,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OAoD+CnB,MAAA,CAAAO,IAAI,CAAC0D,kBAAkB,GAAA9C,MAAA;YAAA;YAAG+C,QAAQ,EAAElE,MAAA,CAAAO,IAAI,CAAC4D,eAAe;YAC1FC,GAAG,EAAE;+DACQpE,MAAA,CAAAiC,wBAAwB,I,cACtCR,YAAA,CAGwBS,gCAAA;YA1DpC1C,GAAA;YAuD2C2C,QAAQ,EAAEnC,MAAA,CAAAqE,UAAU;YAvD/D,qBAAAnD,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OAuDqDnB,MAAA,CAAAqE,UAAU,GAAAlD,MAAA;YAAA;YAvD/DH,UAAA,EAuD0EhB,MAAA,CAAAsE,eAAe;YAvDzF,uBAAApD,MAAA,SAAAA,MAAA,iBAAAC,MAAA;cAAA,OAuD0EnB,MAAA,CAAAsE,eAAe,GAAAnD,MAAA;YAAA;;YAvDzFT,OAAA,EAAAC,QAAA,CAwDc;cAAA,OACuB,CADvBR,YAAA,CACuBH,MAAA;gBADAuE,MAAM,EAAEvE,MAAA,CAAAwE,UAAU;gBAAGhC,UAAQ,EAAExC,MAAA,CAAAyE,YAAY;gBAAG/B,QAAM,EAAE1C,MAAA,CAAA0E;;;YAxD3F1C,CAAA;2DAAAY,mBAAA,e;;QAAAZ,CAAA;YAAAY,mBAAA,gBA8DMA,mBAAA,oYAKe,EACC5C,MAAA,CAAAsD,cAAc,kBAAkBtD,MAAA,CAAAO,IAAI,CAACuD,YAAY,kBAAkB9D,MAAA,CAAAC,KAAK,CAAC4C,MAAM,W,cAC7FpB,YAAA,CAQeb,uBAAA;QA7EvBpB,GAAA;QAqEsBqB,KAAK,EAAC,MAAM;QAACC,IAAI,EAAC,iBAAiB;QAACvB,KAAK,EAAC;;QArEhEmB,OAAA,EAAAC,QAAA,CAsEU;UAAA,OAAwF,CAAxFR,YAAA,CAAwF6D,qCAAA;YAtElGhD,UAAA,EAsE+ChB,MAAA,CAAAO,IAAI,CAAC4D,eAAe;YAtEnE,uBAAAjD,MAAA,SAAAA,MAAA,iBAAAC,MAAA;cAAA,OAsE+CnB,MAAA,CAAAO,IAAI,CAAC4D,eAAe,GAAAhD,MAAA;YAAA;mDACzCnB,MAAA,CAAAiC,wBAAwB,I,cACtCR,YAAA,CAGwBS,gCAAA;YA3EpC1C,GAAA;YAwE2C2C,QAAQ,EAAEnC,MAAA,CAAAqE,UAAU;YAxE/D,qBAAAnD,MAAA,SAAAA,MAAA,iBAAAC,MAAA;cAAA,OAwEqDnB,MAAA,CAAAqE,UAAU,GAAAlD,MAAA;YAAA;YAxE/DH,UAAA,EAwE0EhB,MAAA,CAAAsE,eAAe;YAxEzF,uBAAApD,MAAA,SAAAA,MAAA,iBAAAC,MAAA;cAAA,OAwE0EnB,MAAA,CAAAsE,eAAe,GAAAnD,MAAA;YAAA;;YAxEzFT,OAAA,EAAAC,QAAA,CAyEc;cAAA,OACuB,CADvBR,YAAA,CACuBH,MAAA;gBADAuE,MAAM,EAAEvE,MAAA,CAAAwE,UAAU;gBAAGhC,UAAQ,EAAExC,MAAA,CAAAyE,YAAY;gBAAG/B,QAAM,EAAE1C,MAAA,CAAA0E;;;YAzE3F1C,CAAA;2DAAAY,mBAAA,e;;QAAAZ,CAAA;YAAAY,mBAAA,gBA+EMzC,YAAA,CAA6GH,MAAA;QAArFsC,EAAE,EAAEtC,MAAA,CAAAC,KAAK,CAACqC,EAAE;QAAGC,OAAO,EAAEvC,MAAA,CAAAC,KAAK,CAACsC,OAAO;QAAGC,UAAQ,EAAExC,MAAA,CAAA2E;kDAC1E/E,mBAAA,CAGM,OAHNgF,UAGM,GAFJzE,YAAA,CAA8F0E,oBAAA;QAAnFjB,IAAI,EAAC,SAAS;QAAEjC,QAAQ,EAAE3B,MAAA,CAAAC,KAAK,CAAC2B,MAAM;QAAGkD,OAAK,EAAA5D,MAAA,SAAAA,MAAA,iBAAAC,MAAA;UAAA,OAAEnB,MAAA,CAAA+E,UAAU,CAAC/E,MAAA,CAAAgF,OAAO;QAAA;;QAjFrFtE,OAAA,EAAAC,QAAA,CAiFwF;UAAA,OAAEO,MAAA,SAAAA,MAAA,QAjF1FY,gBAAA,CAiFwF,IAAE,E;;QAjF1FE,CAAA;uCAkFQ7B,YAAA,CAA4C0E,oBAAA;QAAhCC,OAAK,EAAE9E,MAAA,CAAA2E;MAAS;QAlFpCjE,OAAA,EAAAC,QAAA,CAkFsC;UAAA,OAAEO,MAAA,SAAAA,MAAA,QAlFxCY,gBAAA,CAkFsC,IAAE,E;;QAlFxCE,CAAA;YAoFoEhC,MAAA,CAAAC,KAAK,CAAC2B,MAAM,I,cAA1ElC,mBAAA,CAEM,OAFNuF,UAEM,GADJrF,mBAAA,CAA4F,aAAvF,WAAS,GAAAG,gBAAA,CAAGC,MAAA,CAAAC,KAAK,CAACiF,MAAM,CAACC,YAAY,IAAG,GAAC,GAAApF,gBAAA,CAAGC,MAAA,CAAAoF,MAAM,CAACpF,MAAA,CAAAC,KAAK,CAACiF,MAAM,CAACG,QAAQ,KAAI,OAAK,gB,KArF9FzC,mBAAA,e;;IAAAZ,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}