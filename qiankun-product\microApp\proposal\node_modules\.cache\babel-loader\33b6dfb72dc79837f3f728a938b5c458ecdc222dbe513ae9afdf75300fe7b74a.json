{"ast": null, "code": "function _toConsumableArray(r) { return _arrayWithoutHoles(r) || _iterableToArray(r) || _unsupportedIterableToArray(r) || _nonIterableSpread(); }\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(r, a) { if (r) { if (\"string\" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }\nfunction _iterableToArray(r) { if (\"undefined\" != typeof Symbol && null != r[Symbol.iterator] || null != r[\"@@iterator\"]) return Array.from(r); }\nfunction _arrayWithoutHoles(r) { if (Array.isArray(r)) return _arrayLikeToArray(r); }\nfunction _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }\nfunction _regeneratorRuntime() { \"use strict\"; /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */ _regeneratorRuntime = function _regeneratorRuntime() { return e; }; var t, e = {}, r = Object.prototype, n = r.hasOwnProperty, o = Object.defineProperty || function (t, e, r) { t[e] = r.value; }, i = \"function\" == typeof Symbol ? Symbol : {}, a = i.iterator || \"@@iterator\", c = i.asyncIterator || \"@@asyncIterator\", u = i.toStringTag || \"@@toStringTag\"; function define(t, e, r) { return Object.defineProperty(t, e, { value: r, enumerable: !0, configurable: !0, writable: !0 }), t[e]; } try { define({}, \"\"); } catch (t) { define = function define(t, e, r) { return t[e] = r; }; } function wrap(t, e, r, n) { var i = e && e.prototype instanceof Generator ? e : Generator, a = Object.create(i.prototype), c = new Context(n || []); return o(a, \"_invoke\", { value: makeInvokeMethod(t, r, c) }), a; } function tryCatch(t, e, r) { try { return { type: \"normal\", arg: t.call(e, r) }; } catch (t) { return { type: \"throw\", arg: t }; } } e.wrap = wrap; var h = \"suspendedStart\", l = \"suspendedYield\", f = \"executing\", s = \"completed\", y = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} var p = {}; define(p, a, function () { return this; }); var d = Object.getPrototypeOf, v = d && d(d(values([]))); v && v !== r && n.call(v, a) && (p = v); var g = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(p); function defineIteratorMethods(t) { [\"next\", \"throw\", \"return\"].forEach(function (e) { define(t, e, function (t) { return this._invoke(e, t); }); }); } function AsyncIterator(t, e) { function invoke(r, o, i, a) { var c = tryCatch(t[r], t, o); if (\"throw\" !== c.type) { var u = c.arg, h = u.value; return h && \"object\" == typeof h && n.call(h, \"__await\") ? e.resolve(h.__await).then(function (t) { invoke(\"next\", t, i, a); }, function (t) { invoke(\"throw\", t, i, a); }) : e.resolve(h).then(function (t) { u.value = t, i(u); }, function (t) { return invoke(\"throw\", t, i, a); }); } a(c.arg); } var r; o(this, \"_invoke\", { value: function value(t, n) { function callInvokeWithMethodAndArg() { return new e(function (e, r) { invoke(t, n, e, r); }); } return r = r ? r.then(callInvokeWithMethodAndArg, callInvokeWithMethodAndArg) : callInvokeWithMethodAndArg(); } }); } function makeInvokeMethod(e, r, n) { var o = h; return function (i, a) { if (o === f) throw Error(\"Generator is already running\"); if (o === s) { if (\"throw\" === i) throw a; return { value: t, done: !0 }; } for (n.method = i, n.arg = a;;) { var c = n.delegate; if (c) { var u = maybeInvokeDelegate(c, n); if (u) { if (u === y) continue; return u; } } if (\"next\" === n.method) n.sent = n._sent = n.arg;else if (\"throw\" === n.method) { if (o === h) throw o = s, n.arg; n.dispatchException(n.arg); } else \"return\" === n.method && n.abrupt(\"return\", n.arg); o = f; var p = tryCatch(e, r, n); if (\"normal\" === p.type) { if (o = n.done ? s : l, p.arg === y) continue; return { value: p.arg, done: n.done }; } \"throw\" === p.type && (o = s, n.method = \"throw\", n.arg = p.arg); } }; } function maybeInvokeDelegate(e, r) { var n = r.method, o = e.iterator[n]; if (o === t) return r.delegate = null, \"throw\" === n && e.iterator.return && (r.method = \"return\", r.arg = t, maybeInvokeDelegate(e, r), \"throw\" === r.method) || \"return\" !== n && (r.method = \"throw\", r.arg = new TypeError(\"The iterator does not provide a '\" + n + \"' method\")), y; var i = tryCatch(o, e.iterator, r.arg); if (\"throw\" === i.type) return r.method = \"throw\", r.arg = i.arg, r.delegate = null, y; var a = i.arg; return a ? a.done ? (r[e.resultName] = a.value, r.next = e.nextLoc, \"return\" !== r.method && (r.method = \"next\", r.arg = t), r.delegate = null, y) : a : (r.method = \"throw\", r.arg = new TypeError(\"iterator result is not an object\"), r.delegate = null, y); } function pushTryEntry(t) { var e = { tryLoc: t[0] }; 1 in t && (e.catchLoc = t[1]), 2 in t && (e.finallyLoc = t[2], e.afterLoc = t[3]), this.tryEntries.push(e); } function resetTryEntry(t) { var e = t.completion || {}; e.type = \"normal\", delete e.arg, t.completion = e; } function Context(t) { this.tryEntries = [{ tryLoc: \"root\" }], t.forEach(pushTryEntry, this), this.reset(!0); } function values(e) { if (e || \"\" === e) { var r = e[a]; if (r) return r.call(e); if (\"function\" == typeof e.next) return e; if (!isNaN(e.length)) { var o = -1, i = function next() { for (; ++o < e.length;) if (n.call(e, o)) return next.value = e[o], next.done = !1, next; return next.value = t, next.done = !0, next; }; return i.next = i; } } throw new TypeError(typeof e + \" is not iterable\"); } return GeneratorFunction.prototype = GeneratorFunctionPrototype, o(g, \"constructor\", { value: GeneratorFunctionPrototype, configurable: !0 }), o(GeneratorFunctionPrototype, \"constructor\", { value: GeneratorFunction, configurable: !0 }), GeneratorFunction.displayName = define(GeneratorFunctionPrototype, u, \"GeneratorFunction\"), e.isGeneratorFunction = function (t) { var e = \"function\" == typeof t && t.constructor; return !!e && (e === GeneratorFunction || \"GeneratorFunction\" === (e.displayName || e.name)); }, e.mark = function (t) { return Object.setPrototypeOf ? Object.setPrototypeOf(t, GeneratorFunctionPrototype) : (t.__proto__ = GeneratorFunctionPrototype, define(t, u, \"GeneratorFunction\")), t.prototype = Object.create(g), t; }, e.awrap = function (t) { return { __await: t }; }, defineIteratorMethods(AsyncIterator.prototype), define(AsyncIterator.prototype, c, function () { return this; }), e.AsyncIterator = AsyncIterator, e.async = function (t, r, n, o, i) { void 0 === i && (i = Promise); var a = new AsyncIterator(wrap(t, r, n, o), i); return e.isGeneratorFunction(r) ? a : a.next().then(function (t) { return t.done ? t.value : a.next(); }); }, defineIteratorMethods(g), define(g, u, \"Generator\"), define(g, a, function () { return this; }), define(g, \"toString\", function () { return \"[object Generator]\"; }), e.keys = function (t) { var e = Object(t), r = []; for (var n in e) r.push(n); return r.reverse(), function next() { for (; r.length;) { var t = r.pop(); if (t in e) return next.value = t, next.done = !1, next; } return next.done = !0, next; }; }, e.values = values, Context.prototype = { constructor: Context, reset: function reset(e) { if (this.prev = 0, this.next = 0, this.sent = this._sent = t, this.done = !1, this.delegate = null, this.method = \"next\", this.arg = t, this.tryEntries.forEach(resetTryEntry), !e) for (var r in this) \"t\" === r.charAt(0) && n.call(this, r) && !isNaN(+r.slice(1)) && (this[r] = t); }, stop: function stop() { this.done = !0; var t = this.tryEntries[0].completion; if (\"throw\" === t.type) throw t.arg; return this.rval; }, dispatchException: function dispatchException(e) { if (this.done) throw e; var r = this; function handle(n, o) { return a.type = \"throw\", a.arg = e, r.next = n, o && (r.method = \"next\", r.arg = t), !!o; } for (var o = this.tryEntries.length - 1; o >= 0; --o) { var i = this.tryEntries[o], a = i.completion; if (\"root\" === i.tryLoc) return handle(\"end\"); if (i.tryLoc <= this.prev) { var c = n.call(i, \"catchLoc\"), u = n.call(i, \"finallyLoc\"); if (c && u) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } else if (c) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); } else { if (!u) throw Error(\"try statement without catch or finally\"); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } } } }, abrupt: function abrupt(t, e) { for (var r = this.tryEntries.length - 1; r >= 0; --r) { var o = this.tryEntries[r]; if (o.tryLoc <= this.prev && n.call(o, \"finallyLoc\") && this.prev < o.finallyLoc) { var i = o; break; } } i && (\"break\" === t || \"continue\" === t) && i.tryLoc <= e && e <= i.finallyLoc && (i = null); var a = i ? i.completion : {}; return a.type = t, a.arg = e, i ? (this.method = \"next\", this.next = i.finallyLoc, y) : this.complete(a); }, complete: function complete(t, e) { if (\"throw\" === t.type) throw t.arg; return \"break\" === t.type || \"continue\" === t.type ? this.next = t.arg : \"return\" === t.type ? (this.rval = this.arg = t.arg, this.method = \"return\", this.next = \"end\") : \"normal\" === t.type && e && (this.next = e), y; }, finish: function finish(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.finallyLoc === t) return this.complete(r.completion, r.afterLoc), resetTryEntry(r), y; } }, catch: function _catch(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.tryLoc === t) { var n = r.completion; if (\"throw\" === n.type) { var o = n.arg; resetTryEntry(r); } return o; } } throw Error(\"illegal catch attempt\"); }, delegateYield: function delegateYield(e, r, n) { return this.delegate = { iterator: values(e), resultName: r, nextLoc: n }, \"next\" === this.method && (this.arg = t), y; } }, e; }\nfunction asyncGeneratorStep(n, t, e, r, o, a, c) { try { var i = n[a](c), u = i.value; } catch (n) { return void e(n); } i.done ? t(u) : Promise.resolve(u).then(r, o); }\nfunction _asyncToGenerator(n) { return function () { var t = this, e = arguments; return new Promise(function (r, o) { var a = n.apply(t, e); function _next(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"next\", n); } function _throw(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"throw\", n); } _next(void 0); }); }; }\nimport api from '@/api';\nimport { reactive, ref, onActivated, onDeactivated, onBeforeUnmount, nextTick } from 'vue';\nimport { useRoute } from 'vue-router';\nimport { useStore } from 'vuex';\nimport { user, whetherUseIntelligentize } from 'common/js/system_var.js';\nimport { qiankunMicro } from 'common/config/MicroGlobal';\nimport { ElMessage } from 'element-plus';\nimport SimilarityQuery from '@/components/SimilarityQuery/SimilarityQuery.vue';\nimport SuggestRecommendUser from '@/components/SuggestRecommendUser/SuggestRecommendUser.vue';\nimport SuggestReviewDetail from '@/views/SuggestReview/component/SuggestReviewDetail.vue';\nvar __default__ = {\n  name: 'SubmitSuggest'\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  setup(__props, _ref) {\n    var __expose = _ref.expose;\n    __expose();\n    var route = useRoute();\n    var store = useStore();\n    var loading = ref(false);\n    var loadingText = ref('');\n    var formRef = ref();\n    var form = reactive({\n      suggestSubmitWay: 'cppcc_member',\n      title: '',\n      // 提案标题\n      SuggestBigType: '',\n      // 提案大类\n      SuggestSmallType: '',\n      // 提案小类\n      suggestUserId: '',\n      cardNumber: '',\n      sectorType: '',\n      mobile: '',\n      callAddress: '',\n      delegationId: '',\n      isJoinProposal: 0,\n      joinUsers: [],\n      content: '',\n      suggestOpenType: 'open_all',\n      suggestSurveyType: '3',\n      isMakeMineJob: '1',\n      notHandleTimeType: '1',\n      isHopeEnhanceTalk: '1',\n      isNeedPaperAnswer: '1',\n      hopeHandleOfficeIds: [],\n      bigThemeId: '',\n      smallThemeId: '',\n      james: '',\n      jordan: '',\n      kobe: '',\n      duncan: '',\n      wade: ''\n    });\n    var rules = reactive({\n      suggestSubmitWay: [{\n        required: true,\n        message: '请选择提案提交类型',\n        trigger: ['blur', 'change']\n      }],\n      title: [{\n        required: true,\n        message: '请输入提案标题',\n        trigger: ['blur', 'change']\n      }],\n      content: [{\n        required: true,\n        message: '请输入提案内容',\n        trigger: ['blur', 'change']\n      }],\n      suggestUserId: [{\n        required: true,\n        message: '请选择提案者',\n        trigger: ['blur', 'change']\n      }],\n      delegationId: [{\n        required: false,\n        message: '请选择集体提案单位',\n        trigger: ['blur', 'change']\n      }],\n      isJoinProposal: [{\n        required: true,\n        message: '请选择是否联名提案',\n        trigger: ['blur', 'change']\n      }],\n      joinUsers: [{\n        type: 'array',\n        required: false,\n        message: '请选择提案联名人',\n        trigger: ['blur', 'change']\n      }]\n    });\n    var guid = function guid() {\n      return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {\n        var r = Math.random() * 16 | 0,\n          v = c == 'x' ? r : r & 0x3 | 0x8;\n        return v.toString(16);\n      });\n    };\n    var tinyMceSetting = {\n      tp_layout_options: {\n        style: {\n          'text-align': 'justify',\n          'text-indent': '2em',\n          'line-height': '20pt',\n          'font-size': '14pt',\n          'font-family': '仿宋_GB2312'\n        },\n        tagsStyle: {\n          span: {\n            'text-align': 'justify',\n            'text-indent': '2em',\n            'line-height': '20pt',\n            'font-size': '14pt',\n            'font-family': '仿宋_GB2312'\n          }\n        }\n      },\n      paste_postprocess: function paste_postprocess(plugin, args) {\n        nextTick(function () {\n          args.target.execCommand('mceTpLayout');\n          nextTick(function () {\n            args.target.selection.collapse();\n          });\n        });\n      },\n      import_word_callback: function import_word_callback(editor) {\n        nextTick(function () {\n          editor.execCommand('mceTpLayout');\n          nextTick(function () {\n            editor.selection.collapse();\n          });\n        });\n      },\n      menubar: `edit insert format table importWord textRectify tpLayout`,\n      menu: {\n        importWord: {\n          title: '导入Word',\n          items: 'importWord'\n        },\n        textRectify: {\n          title: '一键校正',\n          items: 'textRectify'\n        },\n        tpLayout: {\n          title: '一键排版',\n          items: 'tpLayout'\n        }\n      }\n    };\n    var SuggestBigType = ref([]);\n    var SuggestSmallType = ref([]);\n    var suggestTitleNumber = ref(30);\n    var suggestContentNumber = ref(2000);\n    var suggestMinSimilar = ref(0);\n    var suggestContentMinNumber = ref(0);\n    var termYearId = ref('');\n    var contentCount = ref(0);\n    var fileData = ref([]);\n    var delegationData = ref([]);\n    var suggestOpenTypeName = ref('');\n    var suggestSurveyTypeName = ref('');\n    var notHandleTimeTypeName = ref('');\n    var isHopeEnhanceTalkName = ref('');\n    var isMakeMineJobName = ref('');\n    var isNeedPaperAnswerName = ref('');\n    var suggestOpenType = ref([]);\n    var suggestSurveyType = ref([]);\n    var notHandleTimeType = ref([]);\n    var isHopeEnhanceTalk = ref([]);\n    var isMakeMineJob = ref([]);\n    var isNeedPaperAnswer = ref([]);\n    var contactPersonList = ref([{\n      id: guid(),\n      contactName: '',\n      contactPhone: '',\n      contactAddress: ''\n    }]);\n    var opinionsSuggestionsList = ref([{\n      id: guid(),\n      suggestion: '',\n      explanation: ''\n    }, {\n      id: guid(),\n      suggestion: '',\n      explanation: ''\n    }, {\n      id: guid(),\n      suggestion: '',\n      explanation: ''\n    }]);\n    var typeShow = ref(false);\n    var disabled = ref(false);\n    var isDisabled = ref(false);\n    var tabCode = ref(['cppccMember']);\n    var reviewShow = ref(false);\n    var queryType = ref('');\n    var show = ref(false);\n    var isShow = ref(false);\n    var elIsShow = ref(false);\n    var visibleIsShow = ref(false);\n    var userParams = ref({});\n    var timer = null;\n    // const AiParams = ref({})\n    // const elAiChatClass = AiChatClass()\n    // const handleAiParams = (data) => {\n    //   if (data) {\n    //     elAiChatClass.AiChatConfig({\n    //       AiChatCode: 'ai-intelligent-write-chat',\n    //       AiChatWindow: true,\n    //       AiChatFile: data.fileData,\n    //       AiChatParams: { tool: data.toolId, param: { isPage: '1' } }\n    //     })\n    //     elAiChatClass.AiChatHistory()\n    //     elAiChatClass.AiChatSend(data.toolContent)\n    //   } else {\n    //     elAiChatClass.AiChatConfig({ AiChatCode: 'ai-intelligent-write-chat', AiChatWindow: true })\n    //   }\n    // }\n    // onActivated(() => {\n    //   if (route.query.type !== 'review') {\n    //     const openAiParams = JSON.parse(sessionStorage.getItem('openAiParams')) || ''\n    //     if (openAiParams) AiParams.value = openAiParams\n    //     if (JSON.stringify(AiParams.value) !== '{}') {\n    //       handleAiParams(openAiParams ? AiParams.value : '')\n    //       sessionStorage.setItem('openAiParams', JSON.stringify(''))\n    //       timer = setTimeout(() => {\n    //         handleAiParams('')\n    //       }, 2000)\n    //     }\n    //   }\n\n    onActivated(function () {\n      qiankunMicro.setGlobalState({\n        AiChatCode: 'ai-intelligent-write-chat'\n      });\n      var openAiParams = JSON.parse(sessionStorage.getItem('openAiParams')) || '';\n      if (openAiParams) {\n        qiankunMicro.setGlobalState({\n          AiChatConfig: {\n            AiChatWindow: true,\n            AiChatFile: openAiParams.fileData,\n            AiChatParams: {\n              tool: openAiParams.toolId,\n              param: {\n                isPage: '1'\n              }\n            },\n            AiChatSendMessage: openAiParams.toolContent\n          }\n        });\n        sessionStorage.setItem('openAiParams', JSON.stringify(''));\n        timer = setTimeout(function () {\n          qiankunMicro.setGlobalState({\n            AiChatConfig: {\n              AiChatWindow: true,\n              AiChatFile: openAiParams.fileData,\n              AiChatParams: {}\n            }\n          });\n        }, 2000);\n      }\n      queryType.value = route.query.type;\n      if (route.query.clueListId) {\n        proposalClueInfo();\n      }\n      globalReadConfig();\n      termYearCurrent();\n      dictionaryData();\n      dictionaryNameData();\n      suggestionThemeSelect();\n      if (queryType.value === 'draft' || route.query.anewId) {\n        typeShow.value = true;\n        disabled.value = true;\n      }\n      if (route.query.id || route.query.anewId) {\n        typeShow.value = true;\n        suggestionInfo();\n      } else {\n        tabCode.value = ['cppccMember'];\n        if (user.value.specialRoleKeys.includes('team_office_user')) {\n          typeShow.value = true;\n        }\n        if (user.value.specialRoleKeys.includes('cppcc_member')) {\n          typeShow.value = true;\n          disabled.value = true;\n          form.suggestUserId = user.value.id;\n          cppccMemberInfo(user.value.id);\n        } else {\n          if (user.value.specialRoleKeys.includes('team_office_user')) {\n            form.suggestSubmitWay = 'team';\n          }\n        }\n        if (user.value.specialRoleKeys.includes('team_office_user') && user.value.specialRoleKeys.includes('cppcc_member')) {\n          typeShow.value = false;\n        }\n        if (user.value.specialRoleKeys.includes('admin')) {\n          form.suggestSubmitWay = 'cppcc_member';\n          typeShow.value = false;\n          disabled.value = false;\n          teamOfficeSelect({});\n        } else {\n          if (user.value.specialRoleKeys.includes('team_office_user')) {\n            teamOfficeSelect({\n              isSelectMine: 1\n            });\n          } else {\n            teamOfficeSelect({});\n          }\n        }\n        submitTypeChange();\n      }\n    });\n    onDeactivated(function () {\n      if (timer) {\n        clearTimeout(timer);\n        timer = null;\n      }\n      qiankunMicro.setGlobalState({\n        AiChatCode: 'test_chat'\n      });\n      qiankunMicro.setGlobalState({\n        AiChatConfig: {\n          AiChatWindow: false,\n          AiChatFile: [],\n          AiChatParams: {}\n        }\n      });\n      // elAiChatClass.AiChatConfig({ AiChatCode: 'test_chat', AiChatWindow: false })\n      // elAiChatClass.AiChatHistory()\n    });\n    onBeforeUnmount(function () {\n      if (timer) {\n        clearTimeout(timer);\n        timer = null;\n      }\n      qiankunMicro.setGlobalState({\n        AiChatCode: 'test_chat'\n      });\n      qiankunMicro.setGlobalState({\n        AiChatConfig: {\n          AiChatWindow: false,\n          AiChatFile: [],\n          AiChatParams: {}\n        }\n      });\n      // elAiChatClass.AiChatConfig({ AiChatCode: 'test_chat', AiChatWindow: false })\n      // elAiChatClass.AiChatHistory()\n    });\n    var suggestionThemeSelect = /*#__PURE__*/function () {\n      var _ref2 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee() {\n        var res, data;\n        return _regeneratorRuntime().wrap(function _callee$(_context) {\n          while (1) switch (_context.prev = _context.next) {\n            case 0:\n              _context.next = 2;\n              return api.suggestionThemeSelect({\n                query: {\n                  isUsing: 1\n                }\n              });\n            case 2:\n              res = _context.sent;\n              data = res.data;\n              SuggestBigType.value = data;\n              SuggestBigTypeChange();\n            case 6:\n            case \"end\":\n              return _context.stop();\n          }\n        }, _callee);\n      }));\n      return function suggestionThemeSelect() {\n        return _ref2.apply(this, arguments);\n      };\n    }();\n    var SuggestBigTypeChange = function SuggestBigTypeChange() {\n      if (form.SuggestBigType) {\n        for (var index = 0; index < SuggestBigType.value.length; index++) {\n          var item = SuggestBigType.value[index];\n          if (item.id === form.SuggestBigType) {\n            if (!item.children.map(function (v) {\n              return v.id;\n            }).includes(form.SuggestSmallType)) {\n              form.SuggestSmallType = '';\n            }\n            SuggestSmallType.value = item.children;\n          }\n        }\n      } else {\n        form.SuggestSmallType = '';\n        SuggestSmallType.value = [];\n      }\n    };\n    var handleSimilarity = function handleSimilarity(isType) {\n      if (!form.content) return ElMessage({\n        type: 'warning',\n        message: '请输入提案内容进行相似度查询！'\n      });\n      sessionStorage.setItem('TextQueryToolTitle', form.title);\n      sessionStorage.setItem('TextQueryToolContent', form.content);\n      isShow.value = isType;\n      show.value = true;\n    };\n    var handleSimilarityCallback = function handleSimilarityCallback(type, uflag) {\n      if (uflag == 1) {\n        if (type) {\n          globalJson(0);\n        } else {\n          ElMessage({\n            type: 'warning',\n            message: '您的相似度高于55%，请调整后在提交。'\n          });\n        }\n      }\n      show.value = false;\n    };\n    var handleContentBlur = function handleContentBlur() {\n      userParams.value = {\n        authorId: form.suggestUserId,\n        content: form.content\n      };\n    };\n    var userInitCallback = function userInitCallback(isElIsShow, isVisibleIsShow) {\n      elIsShow.value = isElIsShow;\n      visibleIsShow.value = isVisibleIsShow;\n    };\n    var userSelect = function userSelect(item) {\n      if (!form.joinUsers.includes(item.id)) {\n        form.joinUsers = [].concat(_toConsumableArray(form.joinUsers), [item.id]);\n      }\n    };\n    var globalReadConfig = /*#__PURE__*/function () {\n      var _ref3 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee2() {\n        var _yield$api$globalRead, data;\n        return _regeneratorRuntime().wrap(function _callee2$(_context2) {\n          while (1) switch (_context2.prev = _context2.next) {\n            case 0:\n              _context2.next = 2;\n              return api.globalReadConfig({\n                codes: ['suggestTitleNumber', 'suggestContentNumber', 'suggestMinSimilar', 'suggestContentMinNumber']\n              });\n            case 2:\n              _yield$api$globalRead = _context2.sent;\n              data = _yield$api$globalRead.data;\n              if (data.suggestTitleNumber) {\n                suggestTitleNumber.value = Number(data.suggestTitleNumber);\n              }\n              if (data.suggestContentNumber) {\n                suggestContentNumber.value = Number(data.suggestContentNumber);\n              }\n              if (data.suggestContentMinNumber) {\n                suggestContentMinNumber.value = Number(data.suggestContentMinNumber);\n              }\n              if (data.suggestMinSimilar) {\n                suggestMinSimilar.value = Number(data.suggestMinSimilar);\n              }\n            case 8:\n            case \"end\":\n              return _context2.stop();\n          }\n        }, _callee2);\n      }));\n      return function globalReadConfig() {\n        return _ref3.apply(this, arguments);\n      };\n    }();\n    // 获取当前届次\n    var termYearCurrent = /*#__PURE__*/function () {\n      var _ref4 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee3() {\n        var _yield$api$termYearCu, data;\n        return _regeneratorRuntime().wrap(function _callee3$(_context3) {\n          while (1) switch (_context3.prev = _context3.next) {\n            case 0:\n              _context3.next = 2;\n              return api.termYearCurrent({\n                termYearType: 'cppcc_member'\n              });\n            case 2:\n              _yield$api$termYearCu = _context3.sent;\n              data = _yield$api$termYearCu.data;\n              termYearId.value = data.id;\n            case 5:\n            case \"end\":\n              return _context3.stop();\n          }\n        }, _callee3);\n      }));\n      return function termYearCurrent() {\n        return _ref4.apply(this, arguments);\n      };\n    }();\n    var dictionaryData = /*#__PURE__*/function () {\n      var _ref5 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee4() {\n        var _yield$api$dictionary, data;\n        return _regeneratorRuntime().wrap(function _callee4$(_context4) {\n          while (1) switch (_context4.prev = _context4.next) {\n            case 0:\n              _context4.next = 2;\n              return api.dictionaryData({\n                dictCodes: ['suggest_open_type', 'suggest_survey_type', 'not_handle_time_type', 'is_hope_enhance_talk', 'is_make_mine_job', 'is_need_paper_answer']\n              });\n            case 2:\n              _yield$api$dictionary = _context4.sent;\n              data = _yield$api$dictionary.data;\n              suggestOpenType.value = data.suggest_open_type;\n              suggestSurveyType.value = data.suggest_survey_type;\n              notHandleTimeType.value = data.not_handle_time_type;\n              isHopeEnhanceTalk.value = data.is_hope_enhance_talk;\n              isMakeMineJob.value = data.is_make_mine_job;\n              isNeedPaperAnswer.value = data.is_need_paper_answer;\n            case 10:\n            case \"end\":\n              return _context4.stop();\n          }\n        }, _callee4);\n      }));\n      return function dictionaryData() {\n        return _ref5.apply(this, arguments);\n      };\n    }();\n    var dictionaryNameData = /*#__PURE__*/function () {\n      var _ref6 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee5() {\n        var _yield$api$dictionary2, data;\n        return _regeneratorRuntime().wrap(function _callee5$(_context5) {\n          while (1) switch (_context5.prev = _context5.next) {\n            case 0:\n              _context5.next = 2;\n              return api.dictionaryNameData({\n                dictCodes: ['suggest_open_type', 'suggest_survey_type', 'not_handle_time_type', 'is_hope_enhance_talk', 'is_make_mine_job', 'is_need_paper_answer']\n              });\n            case 2:\n              _yield$api$dictionary2 = _context5.sent;\n              data = _yield$api$dictionary2.data;\n              suggestOpenTypeName.value = data.suggest_open_type;\n              suggestSurveyTypeName.value = data.suggest_survey_type;\n              notHandleTimeTypeName.value = data.not_handle_time_type;\n              isHopeEnhanceTalkName.value = data.is_hope_enhance_talk;\n              isMakeMineJobName.value = data.is_make_mine_job;\n              isNeedPaperAnswerName.value = data.is_need_paper_answer;\n            case 10:\n            case \"end\":\n              return _context5.stop();\n          }\n        }, _callee5);\n      }));\n      return function dictionaryNameData() {\n        return _ref6.apply(this, arguments);\n      };\n    }();\n    var proposalClueInfo = /*#__PURE__*/function () {\n      var _ref7 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee6() {\n        var _yield$api$proposalCl, data;\n        return _regeneratorRuntime().wrap(function _callee6$(_context6) {\n          while (1) switch (_context6.prev = _context6.next) {\n            case 0:\n              _context6.next = 2;\n              return api.proposalClueInfo({\n                detailId: route.query.clueListId\n              });\n            case 2:\n              _yield$api$proposalCl = _context6.sent;\n              data = _yield$api$proposalCl.data;\n              form.title = data.title;\n              form.content = data.content;\n            case 6:\n            case \"end\":\n              return _context6.stop();\n          }\n        }, _callee6);\n      }));\n      return function proposalClueInfo() {\n        return _ref7.apply(this, arguments);\n      };\n    }();\n    var isLock = ref(false);\n    var lockVo = ref({});\n    var suggestionInfo = /*#__PURE__*/function () {\n      var _ref8 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee7() {\n        var _data$proposalInvento, _data$suggestOpenType, _data$suggestSurveyTy, _data$isMakeMineJob, _data$notHandleTimeTy, _data$isHopeEnhanceTa, _data$isNeedPaperAnsw, _data$hopeHandleOffic, _data$joinUsers, _data$contacters, res, data;\n        return _regeneratorRuntime().wrap(function _callee7$(_context7) {\n          while (1) switch (_context7.prev = _context7.next) {\n            case 0:\n              _context7.prev = 0;\n              _context7.next = 3;\n              return api.suggestionInfo({\n                detailId: route.query.id || route.query.anewId,\n                isOpenWithLock: queryType.value === 'review' ? 1 : null\n              });\n            case 3:\n              res = _context7.sent;\n              data = res.data;\n              reviewShow.value = true;\n              lockVo.value = data.lockVo;\n              isLock.value = route.query.type == 'review' && data.lockVo.isLock == 1 && data.lockVo.lockUserId != user.value.id;\n              form.suggestSubmitWay = data.suggestSubmitWay;\n              if (form.suggestSubmitWay === 'cppcc_member') {\n                tabCode.value = ['cppccMember'];\n                form.suggestUserId = data.suggestUserId;\n                if (data.suggestUserId) {\n                  cppccMemberInfo(data.suggestUserId);\n                }\n              }\n              if (form.suggestSubmitWay === 'team') {\n                isDisabled.value = true;\n                form.delegationId = data.delegationId;\n                delegationData.value = data.delegationId ? [{\n                  id: data.delegationId,\n                  name: data.delegationName\n                }] : [];\n              }\n              submitTypeChange();\n              form.title = data.title;\n              form.SuggestBigType = data.bigThemeId;\n              form.SuggestSmallType = data.smallThemeId;\n              SuggestBigTypeChange();\n              form.content = data.content;\n              if ((_data$proposalInvento = data.proposalInventoryList) !== null && _data$proposalInvento !== void 0 && _data$proposalInvento.length) {\n                opinionsSuggestionsList.value = data.proposalInventoryList.map(function (v) {\n                  return {\n                    id: v.id,\n                    suggestion: v.content,\n                    explanation: v.replenish\n                  };\n                });\n              }\n              form.bigThemeId = data.bigThemeId;\n              form.smallThemeId = data.smallThemeId;\n              form.james = data.james;\n              form.jordan = data.jordan;\n              form.kobe = data.kobe;\n              form.duncan = data.duncan;\n              form.wade = data.wade;\n              handleContentBlur();\n              form.isJoinProposal = data.isJoinProposal;\n              JoinChange();\n              form.suggestOpenType = (_data$suggestOpenType = data.suggestOpenType) === null || _data$suggestOpenType === void 0 ? void 0 : _data$suggestOpenType.value;\n              form.suggestSurveyType = (_data$suggestSurveyTy = data.suggestSurveyType) === null || _data$suggestSurveyTy === void 0 ? void 0 : _data$suggestSurveyTy.value;\n              form.isMakeMineJob = (_data$isMakeMineJob = data.isMakeMineJob) === null || _data$isMakeMineJob === void 0 ? void 0 : _data$isMakeMineJob.value;\n              form.notHandleTimeType = (_data$notHandleTimeTy = data.notHandleTimeType) === null || _data$notHandleTimeTy === void 0 ? void 0 : _data$notHandleTimeTy.value;\n              form.isHopeEnhanceTalk = (_data$isHopeEnhanceTa = data.isHopeEnhanceTalk) === null || _data$isHopeEnhanceTa === void 0 ? void 0 : _data$isHopeEnhanceTa.value;\n              form.isNeedPaperAnswer = (_data$isNeedPaperAnsw = data.isNeedPaperAnswer) === null || _data$isNeedPaperAnsw === void 0 ? void 0 : _data$isNeedPaperAnsw.value;\n              fileData.value = data.attachments || [];\n              form.hopeHandleOfficeIds = ((_data$hopeHandleOffic = data.hopeHandleOfficeIds) === null || _data$hopeHandleOffic === void 0 ? void 0 : _data$hopeHandleOffic.map(function (v) {\n                return v.officeId;\n              })) || [];\n              form.joinUsers = ((_data$joinUsers = data.joinUsers) === null || _data$joinUsers === void 0 ? void 0 : _data$joinUsers.map(function (v) {\n                return v.userId;\n              })) || [];\n              if ((_data$contacters = data.contacters) !== null && _data$contacters !== void 0 && _data$contacters.length) {\n                contactPersonList.value = data.contacters.map(function (v) {\n                  return {\n                    id: v.id,\n                    contactName: v.contacterName,\n                    contactPhone: v.contacterMobile,\n                    contactAddress: v.contacterAddress\n                  };\n                });\n              }\n              userParams.value = {\n                authorId: form.suggestUserId,\n                content: form.content\n              };\n              _context7.next = 44;\n              break;\n            case 41:\n              _context7.prev = 41;\n              _context7.t0 = _context7[\"catch\"](0);\n              if (_context7.t0.code === 500) {\n                if (route.query.id && queryType.value === 'review') {\n                  reviewShow.value = false;\n                  qiankunMicro.setGlobalState({\n                    closeOpenRoute: {\n                      openId: route.query.oldRouteId,\n                      closeId: route.query.routeId\n                    }\n                  });\n                }\n              }\n            case 44:\n            case \"end\":\n              return _context7.stop();\n          }\n        }, _callee7, null, [[0, 41]]);\n      }));\n      return function suggestionInfo() {\n        return _ref8.apply(this, arguments);\n      };\n    }();\n    var submitTypeChange = function submitTypeChange() {\n      if (form.suggestSubmitWay === 'cppcc_member') {\n        rules.suggestUserId = [{\n          required: true,\n          message: '请选择提案者',\n          trigger: ['blur', 'change']\n        }];\n        rules.delegationId = [{\n          required: false,\n          message: '请选择集体提案单位',\n          trigger: ['blur', 'change']\n        }];\n      } else if (form.suggestSubmitWay === 'team') {\n        rules.suggestUserId = [{\n          required: false,\n          message: '请选择提案者',\n          trigger: ['blur', 'change']\n        }];\n        rules.delegationId = [{\n          required: true,\n          message: '请选择集体提案单位',\n          trigger: ['blur', 'change']\n        }];\n      }\n    };\n    var JoinChange = function JoinChange() {\n      if (form.isJoinProposal) {\n        rules.joinUsers = [{\n          type: 'array',\n          required: true,\n          message: '请选择提案联名人',\n          trigger: ['blur', 'change']\n        }];\n      } else {\n        rules.joinUsers = [{\n          type: 'array',\n          required: false,\n          message: '请选择提案联名人',\n          trigger: ['blur', 'change']\n        }];\n      }\n    };\n    var handleContentCount = function handleContentCount(count) {\n      contentCount.value = count;\n    };\n    var userCallback = function userCallback(data) {\n      if (data) {\n        cppccMemberInfo(data.id);\n        form.joinUsers = form.joinUsers.filter(function (v) {\n          return v !== data.id;\n        });\n      } else {\n        form.cardNumber = '';\n        form.sectorType = '';\n        form.mobile = '';\n        form.callAddress = '';\n      }\n      userParams.value = {\n        authorId: form.suggestUserId,\n        content: form.content\n      };\n    };\n    var cppccMemberInfo = /*#__PURE__*/function () {\n      var _ref9 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee8(userId) {\n        var _data$sectorType;\n        var _yield$api$cppccMembe, data;\n        return _regeneratorRuntime().wrap(function _callee8$(_context8) {\n          while (1) switch (_context8.prev = _context8.next) {\n            case 0:\n              _context8.next = 2;\n              return api.cppccMemberInfo({\n                detailId: userId\n              });\n            case 2:\n              _yield$api$cppccMembe = _context8.sent;\n              data = _yield$api$cppccMembe.data;\n              form.cardNumber = data.cardNumberCppcc;\n              form.sectorType = (_data$sectorType = data.sectorType) === null || _data$sectorType === void 0 ? void 0 : _data$sectorType.label;\n              form.mobile = data.mobile;\n              form.callAddress = data.callAddress;\n            case 8:\n            case \"end\":\n              return _context8.stop();\n          }\n        }, _callee8);\n      }));\n      return function cppccMemberInfo(_x) {\n        return _ref9.apply(this, arguments);\n      };\n    }();\n    var teamOfficeSelect = /*#__PURE__*/function () {\n      var _ref10 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee9(params) {\n        var _yield$api$teamOffice, data;\n        return _regeneratorRuntime().wrap(function _callee9$(_context9) {\n          while (1) switch (_context9.prev = _context9.next) {\n            case 0:\n              _context9.next = 2;\n              return api.teamOfficeSelect(params);\n            case 2:\n              _yield$api$teamOffice = _context9.sent;\n              data = _yield$api$teamOffice.data;\n              if (data.length) {\n                if (user.value.specialRoleKeys.includes('team_office_user')) {\n                  isDisabled.value = true;\n                  form.delegationId = data[0].id;\n                }\n              }\n              delegationData.value = data;\n            case 6:\n            case \"end\":\n              return _context9.stop();\n          }\n        }, _callee9);\n      }));\n      return function teamOfficeSelect(_x2) {\n        return _ref10.apply(this, arguments);\n      };\n    }();\n    var fileUpload = function fileUpload(file) {\n      fileData.value = file;\n    };\n    var newOpinionsSuggestions = function newOpinionsSuggestions() {\n      opinionsSuggestionsList.value.push({\n        id: guid(),\n        suggestion: '',\n        explanation: ''\n      });\n    };\n    var delOpinionsSuggestions = function delOpinionsSuggestions(id) {\n      opinionsSuggestionsList.value = opinionsSuggestionsList.value.filter(function (v) {\n        return v.id !== id;\n      });\n    };\n    var newContactPerson = function newContactPerson() {\n      contactPersonList.value.push({\n        id: guid(),\n        contactName: '',\n        contactPhone: '',\n        contactAddress: ''\n      });\n    };\n    var delContactPerson = function delContactPerson(id) {\n      contactPersonList.value = contactPersonList.value.filter(function (v) {\n        return v.id !== id;\n      });\n    };\n    var submitForm = /*#__PURE__*/function () {\n      var _ref11 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee10(formEl, type, cb, _form) {\n        var hasValidSuggestion;\n        return _regeneratorRuntime().wrap(function _callee10$(_context10) {\n          while (1) switch (_context10.prev = _context10.next) {\n            case 0:\n              if (formEl) {\n                _context10.next = 2;\n                break;\n              }\n              return _context10.abrupt(\"return\");\n            case 2:\n              if (!(contentCount.value > suggestContentNumber.value && type == '0')) {\n                _context10.next = 5;\n                break;\n              }\n              ElMessage({\n                type: 'warning',\n                message: `当前输入的提案内容超过了${suggestContentNumber.value}字，不允许提交！`\n              });\n              return _context10.abrupt(\"return\");\n            case 5:\n              if (!(contentCount.value < suggestContentMinNumber.value && route.query.utype == '1' && type == '0')) {\n                _context10.next = 8;\n                break;\n              }\n              ElMessage({\n                type: 'warning',\n                message: `当前输入的提案内容少于${suggestContentMinNumber.value}字，不允许提交！`\n              });\n              return _context10.abrupt(\"return\");\n            case 8:\n              hasValidSuggestion = opinionsSuggestionsList.value.some(function (v) {\n                return v.suggestion !== null && v.suggestion !== undefined && v.suggestion.trim() !== \"\";\n              });\n              if (hasValidSuggestion) {\n                _context10.next = 12;\n                break;\n              }\n              ElMessage({\n                type: 'warning',\n                message: `意见建议清单中至少需要填写一项建议！`\n              });\n              return _context10.abrupt(\"return\");\n            case 12:\n              _context10.next = 14;\n              return formEl.validate(function (valid, fields) {\n                if (valid) {\n                  // if (whetherUseIntelligentize.value && !cb) {\n                  //   if (type) { globalJson(type, cb, _form) } else {\n                  //     ElMessageBox.confirm('系统将为您进行相似度查询，是否同意执行操作？', '提示', {\n                  //       closeOnClickModal: false,\n                  //       confirmButtonText: '同意',\n                  //       cancelButtonText: '跳过'\n                  //     }).then(() => { handleSimilarity(true) }).catch(() => { globalJson(type, cb, _form) })\n                  //   }\n                  // } else { globalJson(type, cb, _form) }\n                  globalJson(type, cb, _form);\n                } else {\n                  ElMessage({\n                    type: 'warning',\n                    message: '请根据提示信息完善字段内容！'\n                  });\n                }\n              });\n            case 14:\n            case \"end\":\n              return _context10.stop();\n          }\n        }, _callee10);\n      }));\n      return function submitForm(_x3, _x4, _x5, _x6) {\n        return _ref11.apply(this, arguments);\n      };\n    }();\n    var editCallback = function editCallback(cb, _form) {\n      submitForm(formRef.value, 0, cb, _form);\n    };\n    var globalJson = /*#__PURE__*/function () {\n      var _ref12 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee11(type, cb, _item) {\n        var formObj, _yield$api$globalJson, code;\n        return _regeneratorRuntime().wrap(function _callee11$(_context11) {\n          while (1) switch (_context11.prev = _context11.next) {\n            case 0:\n              formObj = {\n                id: route.query.id,\n                bigThemeId: _item ? _item.SuggestBigType : '',\n                // 提案大类\n                smallThemeId: _item ? _item.SuggestSmallType : '',\n                // 提案小类\n                suggestSubmitWay: form.suggestSubmitWay,\n                title: form.title,\n                // 提案标题\n                suggestUserId: form.suggestSubmitWay === 'cppcc_member' ? form.suggestUserId : null,\n                delegationId: form.suggestSubmitWay === 'team' ? form.delegationId : null,\n                content: form.content,\n                isJoinProposal: form.isJoinProposal,\n                suggestOpenType: form.suggestOpenType,\n                suggestSurveyType: form.suggestSurveyType,\n                isMakeMineJob: form.isMakeMineJob,\n                notHandleTimeType: form.notHandleTimeType,\n                isHopeEnhanceTalk: form.isHopeEnhanceTalk,\n                isNeedPaperAnswer: form.isNeedPaperAnswer,\n                attachmentIds: fileData.value.map(function (v) {\n                  return v.id;\n                })\n              };\n              if (route.query.signId == '2') {\n                formObj.james = _item.reviewOpinion || '';\n                formObj.jordan = _item.reviewResult || '';\n                formObj.kobe = _item.transactType || ''; // 办理方式\n                formObj.duncan = _item.mainHandleOfficeId.map(function (v) {\n                  return v;\n                }).join(',') || ''; // 主办单位\n                formObj.wade = _item.handleOfficeIds.map(function (v) {\n                  return v;\n                }).join(',') || ''; // 协办单位、分办\n              }\n              _context11.prev = 2;\n              _context11.next = 5;\n              return api.globalJson(route.query.id ? '/proposal/edit' : '/proposal/add', {\n                form: formObj,\n                isSaveDraft: type,\n                joinUsers: form.isJoinProposal ? form.joinUsers : [],\n                hopeHandleOfficeIds: form.hopeHandleOfficeIds,\n                contacters: contactPersonList.value.filter(function (v) {\n                  return v.contactName.replace(/(^\\s*)|(\\s*$)/g, '') || v.contactPhone.replace(/(^\\s*)|(\\s*$)/g, '') || v.contactAddress.replace(/(^\\s*)|(\\s*$)/g, '');\n                }).map(function (v) {\n                  return {\n                    contacterName: v.contactName,\n                    contacterMobile: v.contactPhone,\n                    contacterAddress: v.contactAddress\n                  };\n                }),\n                proposalInventories: opinionsSuggestionsList.value.map(function (v) {\n                  return {\n                    content: v.suggestion,\n                    replenish: v.explanation\n                  };\n                })\n              });\n            case 5:\n              _yield$api$globalJson = _context11.sent;\n              code = _yield$api$globalJson.code;\n              if (!(code === 200)) {\n                _context11.next = 16;\n                break;\n              }\n              if (!route.query.clueListId) {\n                _context11.next = 12;\n                break;\n              }\n              proposalClueUse();\n              _context11.next = 16;\n              break;\n            case 12:\n              if (!cb) {\n                _context11.next = 14;\n                break;\n              }\n              return _context11.abrupt(\"return\", cb());\n            case 14:\n              ElMessage({\n                type: 'success',\n                message: route.query.id ? '编辑成功' : '提交成功'\n              });\n              if (route.query.id || route.query.anewId || route.query.clueListId) {\n                qiankunMicro.setGlobalState({\n                  closeOpenRoute: {\n                    openId: route.query.oldRouteId,\n                    closeId: route.query.routeId\n                  }\n                });\n              } else {\n                qiankunMicro.setGlobalState({\n                  openRoute: {\n                    name: '我的提案',\n                    path: '/proposal/MyLedSuggest'\n                  }\n                });\n                store.commit('setRefreshRoute', 'SubmitSuggest');\n                setTimeout(function () {\n                  store.commit('setRefreshRoute', '');\n                }, 222);\n              }\n            case 16:\n              _context11.next = 21;\n              break;\n            case 18:\n              _context11.prev = 18;\n              _context11.t0 = _context11[\"catch\"](2);\n              loading.value = false;\n            case 21:\n            case \"end\":\n              return _context11.stop();\n          }\n        }, _callee11, null, [[2, 18]]);\n      }));\n      return function globalJson(_x7, _x8, _x9) {\n        return _ref12.apply(this, arguments);\n      };\n    }();\n    var proposalClueUse = /*#__PURE__*/function () {\n      var _ref13 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee12() {\n        var _yield$api$proposalCl2, code;\n        return _regeneratorRuntime().wrap(function _callee12$(_context12) {\n          while (1) switch (_context12.prev = _context12.next) {\n            case 0:\n              _context12.next = 2;\n              return api.proposalClueUse({\n                detailId: route.query.clueListId\n              });\n            case 2:\n              _yield$api$proposalCl2 = _context12.sent;\n              code = _yield$api$proposalCl2.code;\n              if (code === 200) {\n                ElMessage({\n                  type: 'success',\n                  message: '提交成功'\n                });\n                qiankunMicro.setGlobalState({\n                  closeOpenRoute: {\n                    openId: route.query.oldRouteId,\n                    closeId: route.query.routeId\n                  }\n                });\n              }\n            case 5:\n            case \"end\":\n              return _context12.stop();\n          }\n        }, _callee12);\n      }));\n      return function proposalClueUse() {\n        return _ref13.apply(this, arguments);\n      };\n    }();\n    var resetForm = function resetForm() {\n      if (route.query.id || route.query.anewId || route.query.clueListId) {\n        qiankunMicro.setGlobalState({\n          closeOpenRoute: {\n            openId: route.query.oldRouteId,\n            closeId: route.query.routeId\n          }\n        });\n      } else {\n        store.commit('setRefreshRoute', 'SubmitSuggest');\n        setTimeout(function () {\n          store.commit('setRefreshRoute', '');\n        }, 222);\n      }\n    };\n    var __returned__ = {\n      route,\n      store,\n      loading,\n      loadingText,\n      formRef,\n      form,\n      rules,\n      guid,\n      tinyMceSetting,\n      SuggestBigType,\n      SuggestSmallType,\n      suggestTitleNumber,\n      suggestContentNumber,\n      suggestMinSimilar,\n      suggestContentMinNumber,\n      termYearId,\n      contentCount,\n      fileData,\n      delegationData,\n      suggestOpenTypeName,\n      suggestSurveyTypeName,\n      notHandleTimeTypeName,\n      isHopeEnhanceTalkName,\n      isMakeMineJobName,\n      isNeedPaperAnswerName,\n      suggestOpenType,\n      suggestSurveyType,\n      notHandleTimeType,\n      isHopeEnhanceTalk,\n      isMakeMineJob,\n      isNeedPaperAnswer,\n      contactPersonList,\n      opinionsSuggestionsList,\n      typeShow,\n      disabled,\n      isDisabled,\n      tabCode,\n      reviewShow,\n      queryType,\n      show,\n      isShow,\n      elIsShow,\n      visibleIsShow,\n      userParams,\n      get timer() {\n        return timer;\n      },\n      set timer(v) {\n        timer = v;\n      },\n      suggestionThemeSelect,\n      SuggestBigTypeChange,\n      handleSimilarity,\n      handleSimilarityCallback,\n      handleContentBlur,\n      userInitCallback,\n      userSelect,\n      globalReadConfig,\n      termYearCurrent,\n      dictionaryData,\n      dictionaryNameData,\n      proposalClueInfo,\n      isLock,\n      lockVo,\n      suggestionInfo,\n      submitTypeChange,\n      JoinChange,\n      handleContentCount,\n      userCallback,\n      cppccMemberInfo,\n      teamOfficeSelect,\n      fileUpload,\n      newOpinionsSuggestions,\n      delOpinionsSuggestions,\n      newContactPerson,\n      delContactPerson,\n      submitForm,\n      editCallback,\n      globalJson,\n      proposalClueUse,\n      resetForm,\n      get api() {\n        return api;\n      },\n      reactive,\n      ref,\n      onActivated,\n      onDeactivated,\n      onBeforeUnmount,\n      nextTick,\n      get useRoute() {\n        return useRoute;\n      },\n      get useStore() {\n        return useStore;\n      },\n      get user() {\n        return user;\n      },\n      get whetherUseIntelligentize() {\n        return whetherUseIntelligentize;\n      },\n      get qiankunMicro() {\n        return qiankunMicro;\n      },\n      get ElMessage() {\n        return ElMessage;\n      },\n      SimilarityQuery,\n      SuggestRecommendUser,\n      SuggestReviewDetail\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["_regeneratorRuntime", "e", "t", "r", "Object", "prototype", "n", "hasOwnProperty", "o", "defineProperty", "value", "i", "Symbol", "a", "iterator", "c", "asyncIterator", "u", "toStringTag", "define", "enumerable", "configurable", "writable", "wrap", "Generator", "create", "Context", "makeInvokeMethod", "tryCatch", "type", "arg", "call", "h", "l", "f", "s", "y", "GeneratorFunction", "GeneratorFunctionPrototype", "p", "d", "getPrototypeOf", "v", "values", "g", "defineIteratorMethods", "for<PERSON>ach", "_invoke", "AsyncIterator", "invoke", "resolve", "__await", "then", "callInvokeWithMethodAndArg", "Error", "done", "method", "delegate", "maybeInvokeDelegate", "sent", "_sent", "dispatchException", "abrupt", "return", "TypeError", "resultName", "next", "nextLoc", "pushTryEntry", "tryLoc", "catchLoc", "finallyLoc", "afterLoc", "tryEntries", "push", "resetTryEntry", "completion", "reset", "isNaN", "length", "displayName", "isGeneratorFunction", "constructor", "name", "mark", "setPrototypeOf", "__proto__", "awrap", "async", "Promise", "keys", "reverse", "pop", "prev", "char<PERSON>t", "slice", "stop", "rval", "handle", "complete", "finish", "catch", "_catch", "<PERSON><PERSON><PERSON>", "asyncGeneratorStep", "_asyncToGenerator", "arguments", "apply", "_next", "_throw", "api", "reactive", "ref", "onActivated", "onDeactivated", "onBeforeUnmount", "nextTick", "useRoute", "useStore", "user", "whetherUseIntelligentize", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ElMessage", "Similarity<PERSON><PERSON>y", "SuggestRecommendUser", "SuggestReviewDetail", "__default__", "route", "store", "loading", "loadingText", "formRef", "form", "suggestSubmitWay", "title", "SuggestBigType", "SuggestSmallType", "suggestUserId", "cardNumber", "sectorType", "mobile", "call<PERSON>dd<PERSON>", "delegationId", "isJoinProposal", "joinUsers", "content", "suggestOpenType", "suggestSurveyType", "isMakeMineJob", "notHandleTimeType", "isHopeEnhanceTalk", "isNeedPaperAnswer", "hopeHandleOfficeIds", "bigThemeId", "smallThemeId", "james", "jordan", "kobe", "duncan", "wade", "rules", "required", "message", "trigger", "guid", "replace", "Math", "random", "toString", "tinyMceSetting", "tp_layout_options", "style", "tagsStyle", "span", "paste_postprocess", "plugin", "args", "target", "execCommand", "selection", "collapse", "import_word_callback", "editor", "menubar", "menu", "importWord", "items", "textRectify", "tpLayout", "suggestTitleNumber", "suggestContentNumber", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "suggestContentMinNumber", "termYearId", "contentCount", "fileData", "delegationData", "suggestOpenTypeName", "suggestSurveyTypeName", "notHandleTimeTypeName", "isHopeEnhanceTalkName", "isMakeMineJobName", "isNeedPaperAnswerName", "contactPersonList", "id", "contactName", "contactPhone", "contactAddress", "opinionsSuggestionsList", "suggestion", "explanation", "typeShow", "disabled", "isDisabled", "tabCode", "reviewShow", "queryType", "show", "isShow", "elIsShow", "visibleIsShow", "userParams", "timer", "setGlobalState", "AiChatCode", "openAiParams", "JSON", "parse", "sessionStorage", "getItem", "AiChatConfig", "AiChatWindow", "AiChatFile", "AiChatParams", "tool", "toolId", "param", "isPage", "AiChatSendMessage", "toolContent", "setItem", "stringify", "setTimeout", "query", "clueListId", "proposalClueInfo", "globalReadConfig", "termYearCurrent", "dictionaryData", "dictionaryNameData", "suggestionThemeSelect", "anewId", "suggestionInfo", "special<PERSON><PERSON><PERSON><PERSON>s", "includes", "cppccMemberInfo", "teamOfficeSelect", "isSelectMine", "submitTypeChange", "clearTimeout", "_ref2", "_callee", "res", "data", "_callee$", "_context", "isUsing", "SuggestBigTypeChange", "index", "item", "children", "map", "handleSimilarity", "isType", "handleSimilarityCallback", "uflag", "globalJson", "handleContentBlur", "authorId", "userInitCallback", "isElIsShow", "isVisibleIsShow", "userSelect", "concat", "_toConsumableArray", "_ref3", "_callee2", "_yield$api$globalRead", "_callee2$", "_context2", "codes", "Number", "_ref4", "_callee3", "_yield$api$termYearCu", "_callee3$", "_context3", "termYearType", "_ref5", "_callee4", "_yield$api$dictionary", "_callee4$", "_context4", "dictCodes", "suggest_open_type", "suggest_survey_type", "not_handle_time_type", "is_hope_enhance_talk", "is_make_mine_job", "is_need_paper_answer", "_ref6", "_callee5", "_yield$api$dictionary2", "_callee5$", "_context5", "_ref7", "_callee6", "_yield$api$proposalCl", "_callee6$", "_context6", "detailId", "isLock", "lockVo", "_ref8", "_callee7", "_data$proposalInvento", "_data$suggestOpenType", "_data$suggestSurveyTy", "_data$isMakeMineJob", "_data$notHandleTimeTy", "_data$isHopeEnhanceTa", "_data$isNeedPaperAnsw", "_data$hopeHandleOffic", "_data$joinUsers", "_data$contacters", "_callee7$", "_context7", "isOpenWithLock", "lockUserId", "delegation<PERSON>ame", "proposalInventoryList", "replenish", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "attachments", "officeId", "userId", "contacters", "contacterName", "contacterMobile", "contacter<PERSON><PERSON><PERSON>", "t0", "code", "closeOpenRoute", "openId", "oldRouteId", "closeId", "routeId", "handleContentCount", "count", "userCallback", "filter", "_ref9", "_callee8", "_data$sectorType", "_yield$api$cppccMembe", "_callee8$", "_context8", "cardNumberCppcc", "label", "_x", "_ref10", "_callee9", "params", "_yield$api$teamOffice", "_callee9$", "_context9", "_x2", "fileUpload", "file", "newOpinionsSuggestions", "delOpinionsSuggestions", "newContact<PERSON>erson", "delContact<PERSON>erson", "submitForm", "_ref11", "_callee10", "formEl", "cb", "_form", "hasValidSuggestion", "_callee10$", "_context10", "utype", "some", "undefined", "trim", "validate", "valid", "fields", "_x3", "_x4", "_x5", "_x6", "edit<PERSON>allback", "_ref12", "_callee11", "_item", "formObj", "_yield$api$globalJson", "_callee11$", "_context11", "attachmentIds", "signId", "reviewOpinion", "reviewResult", "transactType", "mainHandleOfficeId", "join", "handleOfficeIds", "isSaveDraft", "proposalInventories", "proposalClueUse", "openRoute", "path", "commit", "_x7", "_x8", "_x9", "_ref13", "_callee12", "_yield$api$proposalCl2", "_callee12$", "_context12", "resetForm"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/microApp/proposal/src/views/BehalfSuggest/SubmitSuggest/SubmitSuggest.vue"], "sourcesContent": ["<template>\r\n  <el-scrollbar always class=\"SubmitSuggest\" v-loading=\"loading\" :lement-loading-text=\"loadingText\">\r\n    <div class=\"SubmitSuggestBody\">\r\n      <div class=\"SubmitSuggestNameBody\">\r\n        <global-dynamic-title templateCode=\"proposal_title\"></global-dynamic-title>\r\n      </div>\r\n      <el-form ref=\"formRef\" :model=\"form\" :rules=\"rules\" inline :show-message=\"false\" class=\"globalPaperForm\">\r\n        <el-form-item label=\"提案提交类型\" v-if=\"!typeShow\" prop=\"suggestSubmitWay\" class=\"SubmitSuggestTitle\">\r\n          <el-radio-group v-model=\"form.suggestSubmitWay\" @change=\"submitTypeChange\">\r\n            <el-radio label=\"cppcc_member\">委员提案</el-radio>\r\n            <el-radio label=\"team\">集体提案</el-radio>\r\n          </el-radio-group>\r\n        </el-form-item>\r\n        <el-form-item label=\"提案标题\" prop=\"title\" class=\"SubmitSuggestTitle\">\r\n          <el-input v-model=\"form.title\" placeholder=\"请输入提案标题\" show-word-limit :maxlength=\"suggestTitleNumber\"\r\n            clearable />\r\n        </el-form-item>\r\n        <el-form-item v-show=\"form.suggestSubmitWay === 'cppcc_member'\" label=\"提案者\" prop=\"suggestUserId\"\r\n          class=\"SubmitSuggestLeft\">\r\n          <input-select-person v-model=\"form.suggestUserId\" placeholder=\"请选择提案者\" :disabled=\"disabled\" :tabCode=\"tabCode\"\r\n            @callback=\"userCallback\" />\r\n        </el-form-item>\r\n        <el-form-item v-show=\"form.suggestSubmitWay === 'cppcc_member'\" label=\"委员证号\">\r\n          <el-input v-model=\"form.cardNumber\" disabled />\r\n        </el-form-item>\r\n        <el-form-item v-show=\"form.suggestSubmitWay === 'cppcc_member'\" label=\"界别\" class=\"SubmitSuggestLeft\">\r\n          <el-input v-model=\"form.sectorType\" disabled />\r\n        </el-form-item>\r\n        <el-form-item v-show=\"form.suggestSubmitWay === 'cppcc_member'\" label=\"联系电话\">\r\n          <el-input v-model=\"form.mobile\" disabled />\r\n        </el-form-item>\r\n        <el-form-item v-show=\"form.suggestSubmitWay === 'cppcc_member'\" label=\"通讯地址\" class=\"SubmitSuggestTitle\">\r\n          <el-input v-model=\"form.callAddress\" disabled />\r\n        </el-form-item>\r\n        <el-form-item v-show=\"form.suggestSubmitWay === 'team'\" label=\"提案者\" prop=\"delegationId\"\r\n          class=\"SubmitSuggestTitle\">\r\n          <el-select v-model=\"form.delegationId\" :disabled=\"isDisabled\" placeholder=\"请选择集体提案单位\" clearable>\r\n            <el-option v-for=\"item in delegationData\" :key=\"item.id\" :label=\"item.name\" :value=\"item.id\" />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"是否联名提案\" prop=\"isJoinProposal\" class=\"SubmitSuggestTitle\">\r\n          <el-radio-group v-model=\"form.isJoinProposal\" @change=\"JoinChange\">\r\n            <el-radio :label=\"1\">是</el-radio>\r\n            <el-radio :label=\"0\">否</el-radio>\r\n          </el-radio-group>\r\n        </el-form-item>\r\n        <el-form-item label=\"提案联名人\" prop=\"joinUsers\" v-if=\"form.isJoinProposal\" class=\"SubmitSuggestTitle\">\r\n          <simple-select-person v-model=\"form.joinUsers\" placeholder=\"请选择提案联名人\"\r\n            :filterUser=\"form.suggestUserId ? [form.suggestUserId] : []\"\r\n            :tabCode=\"['cppccMember']\"></simple-select-person>\r\n          <template v-if=\"whetherUseIntelligentize && queryType !== 'review'\">\r\n            <intelligent-assistant v-model:elIsShow=\"elIsShow\" v-model=\"visibleIsShow\">\r\n              <SuggestRecommendUser :params=\"userParams\" @callback=\"userInitCallback\" @select=\"userSelect\">\r\n              </SuggestRecommendUser>\r\n            </intelligent-assistant>\r\n          </template>\r\n        </el-form-item>\r\n        <el-form-item label=\"提案内容\" prop=\"content\" class=\"SubmitSuggestTitle SubmitSuggestButton\">\r\n          <el-button @click=\"handleSimilarity(false)\"\r\n            v-if=\"whetherUseIntelligentize && queryType === 'review' && reviewShow\" type=\"primary\">相似度查询</el-button>\r\n        </el-form-item>\r\n        <TinyMceEditor v-model=\"form.content\" :setting=\"tinyMceSetting\" @count=\"handleContentCount\"\r\n          @blur=\"handleContentBlur\" textRectify />\r\n        <el-form-item class=\"opinionsSuggestionsList\" label=\"意见建议清单\">\r\n          <span style=\"position: absolute;top: 0;left:-17%;color:#f56c6c;\">*</span>\r\n          <div class=\"opinionsSuggestionsListHead\">\r\n            <div class=\"opinionsSuggestionsListItem row3\">建议</div>\r\n            <div class=\"opinionsSuggestionsListItem row2\">补充说明</div>\r\n            <div class=\"opinionsSuggestionsListItem row1\">操作</div>\r\n          </div>\r\n          <div class=\"opinionsSuggestionsListBody\" v-for=\"item in opinionsSuggestionsList\" :key=\"item.id\">\r\n            <div class=\"opinionsSuggestionsListItem row3\">\r\n              <el-input placeholder=\"请输入建议\" v-model=\"item.suggestion\" clearable maxlength=\"30\" show-word-limit>\r\n              </el-input>\r\n            </div>\r\n            <div class=\"opinionsSuggestionsListItem row2\">\r\n              <el-input placeholder=\"请输入补充说明\" v-model=\"item.explanation\" clearable>\r\n              </el-input>\r\n            </div>\r\n            <div class=\"opinionsSuggestionsListItem row1\">\r\n              <el-link @click=\"newOpinionsSuggestions\" v-if=\"opinionsSuggestionsList.length\">\r\n                <el-icon>\r\n                  <CirclePlus />\r\n                </el-icon>\r\n              </el-link>\r\n              <el-link v-if=\"opinionsSuggestionsList.length > 1\" @click=\"delOpinionsSuggestions(item.id)\">\r\n                <el-icon>\r\n                  <Remove />\r\n                </el-icon>\r\n              </el-link>\r\n            </div>\r\n          </div>\r\n          <div style=\"border-top: 1px solid #3657C0;width: 100%;text-align: center;color: red;\">\r\n            提案建议清单为必填项，须高度概括、事权明晰、简洁明了，请勿粘帖建议原文内容</div>\r\n        </el-form-item>\r\n        <el-form-item label=\"上传附件\" class=\"SubmitSuggestFormUpload\">\r\n          <xyl-upload-file :fileData=\"fileData\" @fileUpload=\"fileUpload\"\r\n            :fileType=\"['jpg', 'png', 'gif', 'jpeg', 'txt', 'doc', 'docx', 'wps', 'ppt', 'pptx', 'pdf', 'ofd', 'xls', 'xlsx', 'zip', 'rar', 'amr', 'mp4', 'avi', 'wav', 'uof', 'rtf', 'eio']\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"提案相关情况\" class=\"SubmitSuggestFormItem\">\r\n          <div class=\"SubmitSuggestFormInfo\" v-if=\"suggestOpenTypeName\">\r\n            <div class=\"SubmitSuggestFormInfoText\">{{ suggestOpenTypeName }}：</div>\r\n            <el-radio-group v-model=\"form.suggestOpenType\">\r\n              <el-radio v-for=\"item in suggestOpenType\" :key=\"item.key\" :label=\"item.key\">{{ item.name }}</el-radio>\r\n            </el-radio-group>\r\n          </div>\r\n          <div class=\"SubmitSuggestFormInfo\" v-if=\"suggestSurveyTypeName\">\r\n            <div class=\"SubmitSuggestFormInfoText\">{{ suggestSurveyTypeName }}：</div>\r\n            <el-radio-group v-model=\"form.suggestSurveyType\">\r\n              <el-radio v-for=\"item in suggestSurveyType\" :key=\"item.key\" :label=\"item.key\">{{ item.name }}</el-radio>\r\n            </el-radio-group>\r\n          </div>\r\n          <div class=\"SubmitSuggestFormInfo\" v-if=\"isMakeMineJobName\">\r\n            <div class=\"SubmitSuggestFormInfoText\">{{ isMakeMineJobName }}：</div>\r\n            <el-radio-group v-model=\"form.isMakeMineJob\">\r\n              <el-radio v-for=\"item in isMakeMineJob\" :key=\"item.key\" :label=\"item.key\">{{ item.name }}</el-radio>\r\n            </el-radio-group>\r\n          </div>\r\n          <div class=\"SubmitSuggestFormInfo\" v-if=\"notHandleTimeTypeName\">\r\n            <div class=\"SubmitSuggestFormInfoText\">{{ notHandleTimeTypeName }}：</div>\r\n            <el-radio-group v-model=\"form.notHandleTimeType\">\r\n              <el-radio v-for=\"item in notHandleTimeType\" :key=\"item.key\" :label=\"item.key\">{{ item.name }}</el-radio>\r\n            </el-radio-group>\r\n          </div>\r\n          <div class=\"SubmitSuggestFormInfo\" v-if=\"isHopeEnhanceTalkName\">\r\n            <div class=\"SubmitSuggestFormInfoText\">{{ isHopeEnhanceTalkName }}：</div>\r\n            <el-radio-group v-model=\"form.isHopeEnhanceTalk\">\r\n              <el-radio v-for=\"item in isHopeEnhanceTalk\" :key=\"item.key\" :label=\"item.key\">{{ item.name }}</el-radio>\r\n            </el-radio-group>\r\n          </div>\r\n          <div class=\"SubmitSuggestFormInfo\" v-if=\"isNeedPaperAnswerName\">\r\n            <div class=\"SubmitSuggestFormInfoText\">{{ isNeedPaperAnswerName }}：</div>\r\n            <el-radio-group v-model=\"form.isNeedPaperAnswer\">\r\n              <el-radio v-for=\"item in isNeedPaperAnswer\" :key=\"item.key\" :label=\"item.key\">{{ item.name }}</el-radio>\r\n            </el-radio-group>\r\n          </div>\r\n        </el-form-item>\r\n        <el-form-item label=\"希望送交办单位\" class=\"SubmitSuggestTitle\">\r\n          <suggest-simple-select-unit v-model=\"form.hopeHandleOfficeIds\"></suggest-simple-select-unit>\r\n        </el-form-item>\r\n        <el-form-item class=\"SubmitSuggestContactPerson\" label=\"提案联系人\">\r\n          <div class=\"SubmitSuggestContactPersonHead\">\r\n            <div class=\"SubmitSuggestContactPersonItem row2\">提案联系人姓名</div>\r\n            <div class=\"SubmitSuggestContactPersonItem row2\">提案联系人电话</div>\r\n            <div class=\"SubmitSuggestContactPersonItem row3\">联系人通讯地址</div>\r\n            <div class=\"SubmitSuggestContactPersonItem row1\">操作</div>\r\n          </div>\r\n          <div class=\"SubmitSuggestContactPersonBody\" v-for=\"item in contactPersonList\" :key=\"item.id\">\r\n            <div class=\"SubmitSuggestContactPersonItem row2\">\r\n              <el-input placeholder=\"请输入联系人姓名\" v-model=\"item.contactName\" clearable></el-input>\r\n            </div>\r\n            <div class=\"SubmitSuggestContactPersonItem row2\">\r\n              <el-input placeholder=\"请输入联系人电话\" v-model=\"item.contactPhone\" clearable></el-input>\r\n            </div>\r\n            <div class=\"SubmitSuggestContactPersonItem row3\">\r\n              <el-input placeholder=\"请输入联系人通讯地址\" v-model=\"item.contactAddress\" clearable></el-input>\r\n            </div>\r\n            <div class=\"SubmitSuggestContactPersonItem row1\">\r\n              <el-link @click=\"newContactPerson\" v-if=\"contactPersonList.length\">\r\n                <el-icon>\r\n                  <CirclePlus />\r\n                </el-icon>\r\n              </el-link>\r\n              <el-link v-if=\"contactPersonList.length > 1\" @click=\"delContactPerson(item.id)\">\r\n                <el-icon>\r\n                  <Remove />\r\n                </el-icon>\r\n              </el-link>\r\n            </div>\r\n          </div>\r\n        </el-form-item>\r\n        <div class=\"globalPaperFormButton\" v-if=\"queryType !== 'review'\">\r\n          <el-button type=\"primary\" @click=\"submitForm(formRef, 0)\">提交提案</el-button>\r\n          <el-button @click=\"submitForm(formRef, 1)\"\r\n            v-if=\"(!route.query.anewId && !route.query.id) || queryType === 'draft'\">\r\n            存为草稿\r\n          </el-button>\r\n          <el-button @click=\"resetForm\" v-if=\"!route.query.id\">重置</el-button>\r\n          <el-button @click=\"resetForm\" v-if=\"route.query.id\">取消</el-button>\r\n        </div>\r\n      </el-form>\r\n      <div v-if=\"queryType === 'review'\" class=\"SuggestSegmentation\"></div>\r\n      <keep-alive>\r\n        <SuggestReviewDetail :id=\"route.query.id\" :name=\"route.query.reviewName\" :content=\"form.content\"\r\n          :hopeHandleOfficeIds=\"form.hopeHandleOfficeIds\" v-if=\"queryType === 'review' && reviewShow\"\r\n          @editCallback=\"editCallback\" @callback=\"resetForm\" :queryType=\"queryType\" :signId=\"route.query.signId\"\r\n          :bigThemeId=\"form.bigThemeId\" :smallThemeId=\"form.smallThemeId\" :james=\"form.james\" :jordan=\"form.jordan\"\r\n          :kobe=\"form.kobe\" :duncan=\"form.duncan\" :wade=\"form.wade\">\r\n        </SuggestReviewDetail>\r\n      </keep-alive>\r\n    </div>\r\n    <xyl-popup-window v-model=\"show\" name=\"相似度查询\">\r\n      <SimilarityQuery :type=\"isShow\" :id=\"route.query.id\" :content=\"form.content\" @callback=\"handleSimilarityCallback\">\r\n      </SimilarityQuery>\r\n    </xyl-popup-window>\r\n  </el-scrollbar>\r\n</template>\r\n<script>\r\nexport default { name: 'SubmitSuggest' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { reactive, ref, onActivated, onDeactivated, onBeforeUnmount, nextTick } from 'vue'\r\nimport { useRoute } from 'vue-router'\r\nimport { useStore } from 'vuex'\r\nimport { user, whetherUseIntelligentize } from 'common/js/system_var.js'\r\nimport { qiankunMicro } from 'common/config/MicroGlobal'\r\nimport { ElMessage } from 'element-plus'\r\nimport SimilarityQuery from '@/components/SimilarityQuery/SimilarityQuery.vue'\r\nimport SuggestRecommendUser from '@/components/SuggestRecommendUser/SuggestRecommendUser.vue'\r\nimport SuggestReviewDetail from '@/views/SuggestReview/component/SuggestReviewDetail.vue'\r\n\r\nconst route = useRoute()\r\nconst store = useStore()\r\nconst loading = ref(false)\r\nconst loadingText = ref('')\r\nconst formRef = ref()\r\nconst form = reactive({\r\n  suggestSubmitWay: 'cppcc_member',\r\n  title: '', // 提案标题\r\n  SuggestBigType: '', // 提案大类\r\n  SuggestSmallType: '', // 提案小类\r\n  suggestUserId: '',\r\n  cardNumber: '',\r\n  sectorType: '',\r\n  mobile: '',\r\n  callAddress: '',\r\n  delegationId: '',\r\n  isJoinProposal: 0,\r\n  joinUsers: [],\r\n  content: '',\r\n  suggestOpenType: 'open_all',\r\n  suggestSurveyType: '3',\r\n  isMakeMineJob: '1',\r\n  notHandleTimeType: '1',\r\n  isHopeEnhanceTalk: '1',\r\n  isNeedPaperAnswer: '1',\r\n  hopeHandleOfficeIds: [],\r\n  bigThemeId: '',\r\n  smallThemeId: '',\r\n  james: '',\r\n  jordan: '',\r\n  kobe: '',\r\n  duncan: '',\r\n  wade: ''\r\n})\r\nconst rules = reactive({\r\n  suggestSubmitWay: [{ required: true, message: '请选择提案提交类型', trigger: ['blur', 'change'] }],\r\n  title: [{ required: true, message: '请输入提案标题', trigger: ['blur', 'change'] }],\r\n  content: [{ required: true, message: '请输入提案内容', trigger: ['blur', 'change'] }],\r\n  suggestUserId: [{ required: true, message: '请选择提案者', trigger: ['blur', 'change'] }],\r\n  delegationId: [{ required: false, message: '请选择集体提案单位', trigger: ['blur', 'change'] }],\r\n  isJoinProposal: [{ required: true, message: '请选择是否联名提案', trigger: ['blur', 'change'] }],\r\n  joinUsers: [{ type: 'array', required: false, message: '请选择提案联名人', trigger: ['blur', 'change'] }]\r\n})\r\n\r\nconst guid = () => {\r\n  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {\r\n    var r = (Math.random() * 16) | 0,\r\n      v = c == 'x' ? r : (r & 0x3) | 0x8\r\n    return v.toString(16)\r\n  })\r\n}\r\nconst tinyMceSetting = {\r\n  tp_layout_options: {\r\n    style: {\r\n      'text-align': 'justify',\r\n      'text-indent': '2em',\r\n      'line-height': '20pt',\r\n      'font-size': '14pt',\r\n      'font-family': '仿宋_GB2312'\r\n    },\r\n    tagsStyle: {\r\n      span: {\r\n        'text-align': 'justify',\r\n        'text-indent': '2em',\r\n        'line-height': '20pt',\r\n        'font-size': '14pt',\r\n        'font-family': '仿宋_GB2312'\r\n      }\r\n    }\r\n  },\r\n  paste_postprocess: (plugin, args) => {\r\n    nextTick(() => {\r\n      args.target.execCommand('mceTpLayout')\r\n      nextTick(() => {\r\n        args.target.selection.collapse()\r\n      })\r\n    })\r\n  },\r\n  import_word_callback: (editor) => {\r\n    nextTick(() => {\r\n      editor.execCommand('mceTpLayout')\r\n      nextTick(() => {\r\n        editor.selection.collapse()\r\n      })\r\n    })\r\n  },\r\n  menubar: `edit insert format table importWord textRectify tpLayout`,\r\n  menu: {\r\n    importWord: { title: '导入Word', items: 'importWord' },\r\n    textRectify: { title: '一键校正', items: 'textRectify' },\r\n    tpLayout: { title: '一键排版', items: 'tpLayout' }\r\n  },\r\n}\r\nconst SuggestBigType = ref([])\r\nconst SuggestSmallType = ref([])\r\nconst suggestTitleNumber = ref(30)\r\nconst suggestContentNumber = ref(2000)\r\nconst suggestMinSimilar = ref(0)\r\nconst suggestContentMinNumber = ref(0)\r\nconst termYearId = ref('')\r\nconst contentCount = ref(0)\r\nconst fileData = ref([])\r\nconst delegationData = ref([])\r\nconst suggestOpenTypeName = ref('')\r\nconst suggestSurveyTypeName = ref('')\r\nconst notHandleTimeTypeName = ref('')\r\nconst isHopeEnhanceTalkName = ref('')\r\nconst isMakeMineJobName = ref('')\r\nconst isNeedPaperAnswerName = ref('')\r\nconst suggestOpenType = ref([])\r\nconst suggestSurveyType = ref([])\r\nconst notHandleTimeType = ref([])\r\nconst isHopeEnhanceTalk = ref([])\r\nconst isMakeMineJob = ref([])\r\nconst isNeedPaperAnswer = ref([])\r\nconst contactPersonList = ref([{ id: guid(), contactName: '', contactPhone: '', contactAddress: '' }])\r\nconst opinionsSuggestionsList = ref([{ id: guid(), suggestion: '', explanation: '' }, { id: guid(), suggestion: '', explanation: '' }, { id: guid(), suggestion: '', explanation: '' }])\r\nconst typeShow = ref(false)\r\nconst disabled = ref(false)\r\nconst isDisabled = ref(false)\r\nconst tabCode = ref(['cppccMember'])\r\nconst reviewShow = ref(false)\r\nconst queryType = ref('')\r\n\r\nconst show = ref(false)\r\nconst isShow = ref(false)\r\nconst elIsShow = ref(false)\r\nconst visibleIsShow = ref(false)\r\nconst userParams = ref({})\r\nlet timer = null\r\n// const AiParams = ref({})\r\n// const elAiChatClass = AiChatClass()\r\n// const handleAiParams = (data) => {\r\n//   if (data) {\r\n//     elAiChatClass.AiChatConfig({\r\n//       AiChatCode: 'ai-intelligent-write-chat',\r\n//       AiChatWindow: true,\r\n//       AiChatFile: data.fileData,\r\n//       AiChatParams: { tool: data.toolId, param: { isPage: '1' } }\r\n//     })\r\n//     elAiChatClass.AiChatHistory()\r\n//     elAiChatClass.AiChatSend(data.toolContent)\r\n//   } else {\r\n//     elAiChatClass.AiChatConfig({ AiChatCode: 'ai-intelligent-write-chat', AiChatWindow: true })\r\n//   }\r\n// }\r\n// onActivated(() => {\r\n//   if (route.query.type !== 'review') {\r\n//     const openAiParams = JSON.parse(sessionStorage.getItem('openAiParams')) || ''\r\n//     if (openAiParams) AiParams.value = openAiParams\r\n//     if (JSON.stringify(AiParams.value) !== '{}') {\r\n//       handleAiParams(openAiParams ? AiParams.value : '')\r\n//       sessionStorage.setItem('openAiParams', JSON.stringify(''))\r\n//       timer = setTimeout(() => {\r\n//         handleAiParams('')\r\n//       }, 2000)\r\n//     }\r\n//   }\r\n\r\nonActivated(() => {\r\n  qiankunMicro.setGlobalState({ AiChatCode: 'ai-intelligent-write-chat' })\r\n  const openAiParams = JSON.parse(sessionStorage.getItem('openAiParams')) || ''\r\n  if (openAiParams) {\r\n    qiankunMicro.setGlobalState({\r\n      AiChatConfig: {\r\n        AiChatWindow: true,\r\n        AiChatFile: openAiParams.fileData,\r\n        AiChatParams: { tool: openAiParams.toolId, param: { isPage: '1' } },\r\n        AiChatSendMessage: openAiParams.toolContent\r\n      }\r\n    })\r\n    sessionStorage.setItem('openAiParams', JSON.stringify(''))\r\n    timer = setTimeout(() => {\r\n      qiankunMicro.setGlobalState({\r\n        AiChatConfig: {\r\n          AiChatWindow: true,\r\n          AiChatFile: openAiParams.fileData,\r\n          AiChatParams: {}\r\n        }\r\n      })\r\n    }, 2000)\r\n  }\r\n  queryType.value = route.query.type\r\n  if (route.query.clueListId) {\r\n    proposalClueInfo()\r\n  }\r\n  globalReadConfig()\r\n  termYearCurrent()\r\n  dictionaryData()\r\n  dictionaryNameData()\r\n  suggestionThemeSelect()\r\n  if (queryType.value === 'draft' || route.query.anewId) {\r\n    typeShow.value = true\r\n    disabled.value = true\r\n  }\r\n  if (route.query.id || route.query.anewId) {\r\n    typeShow.value = true\r\n    suggestionInfo()\r\n  } else {\r\n    tabCode.value = ['cppccMember']\r\n    if (user.value.specialRoleKeys.includes('team_office_user')) {\r\n      typeShow.value = true\r\n    }\r\n    if (user.value.specialRoleKeys.includes('cppcc_member')) {\r\n      typeShow.value = true\r\n      disabled.value = true\r\n      form.suggestUserId = user.value.id\r\n      cppccMemberInfo(user.value.id)\r\n    } else {\r\n      if (user.value.specialRoleKeys.includes('team_office_user')) {\r\n        form.suggestSubmitWay = 'team'\r\n      }\r\n    }\r\n    if (\r\n      user.value.specialRoleKeys.includes('team_office_user') &&\r\n      user.value.specialRoleKeys.includes('cppcc_member')\r\n    ) {\r\n      typeShow.value = false\r\n    }\r\n    if (user.value.specialRoleKeys.includes('admin')) {\r\n      form.suggestSubmitWay = 'cppcc_member'\r\n      typeShow.value = false\r\n      disabled.value = false\r\n      teamOfficeSelect({})\r\n    } else {\r\n      if (user.value.specialRoleKeys.includes('team_office_user')) {\r\n        teamOfficeSelect({ isSelectMine: 1 })\r\n      } else {\r\n        teamOfficeSelect({})\r\n      }\r\n    }\r\n    submitTypeChange()\r\n  }\r\n})\r\nonDeactivated(() => {\r\n  if (timer) {\r\n    clearTimeout(timer)\r\n    timer = null\r\n  }\r\n  qiankunMicro.setGlobalState({ AiChatCode: 'test_chat' })\r\n  qiankunMicro.setGlobalState({\r\n    AiChatConfig: {\r\n      AiChatWindow: false,\r\n      AiChatFile: [],\r\n      AiChatParams: {}\r\n    }\r\n  })\r\n  // elAiChatClass.AiChatConfig({ AiChatCode: 'test_chat', AiChatWindow: false })\r\n  // elAiChatClass.AiChatHistory()\r\n})\r\nonBeforeUnmount(() => {\r\n  if (timer) {\r\n    clearTimeout(timer)\r\n    timer = null\r\n  }\r\n  qiankunMicro.setGlobalState({ AiChatCode: 'test_chat' })\r\n  qiankunMicro.setGlobalState({\r\n    AiChatConfig: {\r\n      AiChatWindow: false,\r\n      AiChatFile: [],\r\n      AiChatParams: {}\r\n    }\r\n  })\r\n  // elAiChatClass.AiChatConfig({ AiChatCode: 'test_chat', AiChatWindow: false })\r\n  // elAiChatClass.AiChatHistory()\r\n})\r\n\r\nconst suggestionThemeSelect = async () => {\r\n  const res = await api.suggestionThemeSelect({ query: { isUsing: 1 } })\r\n  var { data } = res\r\n  SuggestBigType.value = data\r\n  SuggestBigTypeChange()\r\n}\r\n\r\nconst SuggestBigTypeChange = () => {\r\n  if (form.SuggestBigType) {\r\n    for (let index = 0; index < SuggestBigType.value.length; index++) {\r\n      const item = SuggestBigType.value[index]\r\n      if (item.id === form.SuggestBigType) {\r\n        if (!item.children.map((v) => v.id).includes(form.SuggestSmallType)) {\r\n          form.SuggestSmallType = ''\r\n        }\r\n        SuggestSmallType.value = item.children\r\n      }\r\n    }\r\n  } else {\r\n    form.SuggestSmallType = ''\r\n    SuggestSmallType.value = []\r\n  }\r\n}\r\n\r\nconst handleSimilarity = (isType) => {\r\n  if (!form.content) return ElMessage({ type: 'warning', message: '请输入提案内容进行相似度查询！' })\r\n  sessionStorage.setItem('TextQueryToolTitle', form.title)\r\n  sessionStorage.setItem('TextQueryToolContent', form.content)\r\n  isShow.value = isType\r\n  show.value = true\r\n}\r\nconst handleSimilarityCallback = (type, uflag) => {\r\n  if (uflag == 1) {\r\n    if (type) {\r\n      globalJson(0)\r\n    } else {\r\n      ElMessage({ type: 'warning', message: '您的相似度高于55%，请调整后在提交。' })\r\n    }\r\n  }\r\n  show.value = false\r\n}\r\nconst handleContentBlur = () => {\r\n  userParams.value = { authorId: form.suggestUserId, content: form.content }\r\n}\r\nconst userInitCallback = (isElIsShow, isVisibleIsShow) => {\r\n  elIsShow.value = isElIsShow\r\n  visibleIsShow.value = isVisibleIsShow\r\n}\r\nconst userSelect = (item) => {\r\n  if (!form.joinUsers.includes(item.id)) {\r\n    form.joinUsers = [...form.joinUsers, item.id]\r\n  }\r\n}\r\nconst globalReadConfig = async () => {\r\n  const { data } = await api.globalReadConfig({ codes: ['suggestTitleNumber', 'suggestContentNumber', 'suggestMinSimilar', 'suggestContentMinNumber'] })\r\n  if (data.suggestTitleNumber) {\r\n    suggestTitleNumber.value = Number(data.suggestTitleNumber)\r\n  }\r\n  if (data.suggestContentNumber) {\r\n    suggestContentNumber.value = Number(data.suggestContentNumber)\r\n  }\r\n  if (data.suggestContentMinNumber) {\r\n    suggestContentMinNumber.value = Number(data.suggestContentMinNumber)\r\n  }\r\n  if (data.suggestMinSimilar) {\r\n    suggestMinSimilar.value = Number(data.suggestMinSimilar)\r\n  }\r\n}\r\n// 获取当前届次\r\nconst termYearCurrent = async () => {\r\n  const { data } = await api.termYearCurrent({ termYearType: 'cppcc_member' })\r\n  termYearId.value = data.id\r\n}\r\nconst dictionaryData = async () => {\r\n  const { data } = await api.dictionaryData({\r\n    dictCodes: [\r\n      'suggest_open_type',\r\n      'suggest_survey_type',\r\n      'not_handle_time_type',\r\n      'is_hope_enhance_talk',\r\n      'is_make_mine_job',\r\n      'is_need_paper_answer'\r\n    ]\r\n  })\r\n  suggestOpenType.value = data.suggest_open_type\r\n  suggestSurveyType.value = data.suggest_survey_type\r\n  notHandleTimeType.value = data.not_handle_time_type\r\n  isHopeEnhanceTalk.value = data.is_hope_enhance_talk\r\n  isMakeMineJob.value = data.is_make_mine_job\r\n  isNeedPaperAnswer.value = data.is_need_paper_answer\r\n}\r\nconst dictionaryNameData = async () => {\r\n  const { data } = await api.dictionaryNameData({\r\n    dictCodes: [\r\n      'suggest_open_type',\r\n      'suggest_survey_type',\r\n      'not_handle_time_type',\r\n      'is_hope_enhance_talk',\r\n      'is_make_mine_job',\r\n      'is_need_paper_answer'\r\n    ]\r\n  })\r\n  suggestOpenTypeName.value = data.suggest_open_type\r\n  suggestSurveyTypeName.value = data.suggest_survey_type\r\n  notHandleTimeTypeName.value = data.not_handle_time_type\r\n  isHopeEnhanceTalkName.value = data.is_hope_enhance_talk\r\n  isMakeMineJobName.value = data.is_make_mine_job\r\n  isNeedPaperAnswerName.value = data.is_need_paper_answer\r\n}\r\nconst proposalClueInfo = async () => {\r\n  const { data } = await api.proposalClueInfo({ detailId: route.query.clueListId })\r\n  form.title = data.title\r\n  form.content = data.content\r\n}\r\nconst isLock = ref(false)\r\nconst lockVo = ref({})\r\nconst suggestionInfo = async () => {\r\n  try {\r\n    const res = await api.suggestionInfo({\r\n      detailId: route.query.id || route.query.anewId,\r\n      isOpenWithLock: queryType.value === 'review' ? 1 : null\r\n    })\r\n    var { data } = res\r\n    reviewShow.value = true\r\n    lockVo.value = data.lockVo\r\n    isLock.value = route.query.type == 'review' && data.lockVo.isLock == 1 && data.lockVo.lockUserId != user.value.id\r\n    form.suggestSubmitWay = data.suggestSubmitWay\r\n    if (form.suggestSubmitWay === 'cppcc_member') {\r\n      tabCode.value = ['cppccMember']\r\n      form.suggestUserId = data.suggestUserId\r\n      if (data.suggestUserId) {\r\n        cppccMemberInfo(data.suggestUserId)\r\n      }\r\n    }\r\n    if (form.suggestSubmitWay === 'team') {\r\n      isDisabled.value = true\r\n      form.delegationId = data.delegationId\r\n      delegationData.value = data.delegationId ? [{ id: data.delegationId, name: data.delegationName }] : []\r\n    }\r\n    submitTypeChange()\r\n    form.title = data.title\r\n    form.SuggestBigType = data.bigThemeId\r\n    form.SuggestSmallType = data.smallThemeId\r\n    SuggestBigTypeChange()\r\n    form.content = data.content\r\n    if (data.proposalInventoryList?.length) {\r\n      opinionsSuggestionsList.value = data.proposalInventoryList.map(v => ({ id: v.id, suggestion: v.content, explanation: v.replenish }))\r\n    }\r\n    form.bigThemeId = data.bigThemeId\r\n    form.smallThemeId = data.smallThemeId\r\n    form.james = data.james\r\n    form.jordan = data.jordan\r\n    form.kobe = data.kobe\r\n    form.duncan = data.duncan\r\n    form.wade = data.wade\r\n    handleContentBlur()\r\n    form.isJoinProposal = data.isJoinProposal\r\n    JoinChange()\r\n    form.suggestOpenType = data.suggestOpenType?.value\r\n    form.suggestSurveyType = data.suggestSurveyType?.value\r\n    form.isMakeMineJob = data.isMakeMineJob?.value\r\n    form.notHandleTimeType = data.notHandleTimeType?.value\r\n    form.isHopeEnhanceTalk = data.isHopeEnhanceTalk?.value\r\n    form.isNeedPaperAnswer = data.isNeedPaperAnswer?.value\r\n    fileData.value = data.attachments || []\r\n    form.hopeHandleOfficeIds = data.hopeHandleOfficeIds?.map((v) => v.officeId) || []\r\n    form.joinUsers = data.joinUsers?.map((v) => v.userId) || []\r\n    if (data.contacters?.length) {\r\n      contactPersonList.value = data.contacters.map((v) => ({\r\n        id: v.id,\r\n        contactName: v.contacterName,\r\n        contactPhone: v.contacterMobile,\r\n        contactAddress: v.contacterAddress\r\n      }))\r\n    }\r\n    userParams.value = { authorId: form.suggestUserId, content: form.content }\r\n  } catch (err) {\r\n    if (err.code === 500) {\r\n      if (route.query.id && queryType.value === 'review') {\r\n        reviewShow.value = false\r\n        qiankunMicro.setGlobalState({\r\n          closeOpenRoute: { openId: route.query.oldRouteId, closeId: route.query.routeId }\r\n        })\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\nconst submitTypeChange = () => {\r\n  if (form.suggestSubmitWay === 'cppcc_member') {\r\n    rules.suggestUserId = [{ required: true, message: '请选择提案者', trigger: ['blur', 'change'] }]\r\n    rules.delegationId = [{ required: false, message: '请选择集体提案单位', trigger: ['blur', 'change'] }]\r\n  } else if (form.suggestSubmitWay === 'team') {\r\n    rules.suggestUserId = [{ required: false, message: '请选择提案者', trigger: ['blur', 'change'] }]\r\n    rules.delegationId = [{ required: true, message: '请选择集体提案单位', trigger: ['blur', 'change'] }]\r\n  }\r\n}\r\nconst JoinChange = () => {\r\n  if (form.isJoinProposal) {\r\n    rules.joinUsers = [{ type: 'array', required: true, message: '请选择提案联名人', trigger: ['blur', 'change'] }]\r\n  } else {\r\n    rules.joinUsers = [{ type: 'array', required: false, message: '请选择提案联名人', trigger: ['blur', 'change'] }]\r\n  }\r\n}\r\nconst handleContentCount = (count) => {\r\n  contentCount.value = count\r\n}\r\n\r\nconst userCallback = (data) => {\r\n  if (data) {\r\n    cppccMemberInfo(data.id)\r\n    form.joinUsers = form.joinUsers.filter((v) => v !== data.id)\r\n  } else {\r\n    form.cardNumber = ''\r\n    form.sectorType = ''\r\n    form.mobile = ''\r\n    form.callAddress = ''\r\n  }\r\n  userParams.value = { authorId: form.suggestUserId, content: form.content }\r\n}\r\nconst cppccMemberInfo = async (userId) => {\r\n  const { data } = await api.cppccMemberInfo({ detailId: userId })\r\n  form.cardNumber = data.cardNumberCppcc\r\n  form.sectorType = data.sectorType?.label\r\n  form.mobile = data.mobile\r\n  form.callAddress = data.callAddress\r\n}\r\nconst teamOfficeSelect = async (params) => {\r\n  const { data } = await api.teamOfficeSelect(params)\r\n  if (data.length) {\r\n    if (user.value.specialRoleKeys.includes('team_office_user')) {\r\n      isDisabled.value = true\r\n      form.delegationId = data[0].id\r\n    }\r\n  }\r\n  delegationData.value = data\r\n}\r\nconst fileUpload = (file) => {\r\n  fileData.value = file\r\n}\r\nconst newOpinionsSuggestions = () => {\r\n  opinionsSuggestionsList.value.push({ id: guid(), suggestion: '', explanation: '' })\r\n}\r\nconst delOpinionsSuggestions = (id) => {\r\n  opinionsSuggestionsList.value = opinionsSuggestionsList.value.filter(v => v.id !== id)\r\n}\r\nconst newContactPerson = () => {\r\n  contactPersonList.value.push({ id: guid(), contactName: '', contactPhone: '', contactAddress: '' })\r\n}\r\nconst delContactPerson = (id) => {\r\n  contactPersonList.value = contactPersonList.value.filter((v) => v.id !== id)\r\n}\r\nconst submitForm = async (formEl, type, cb, _form) => {\r\n  if (!formEl) return\r\n  if (contentCount.value > suggestContentNumber.value && type == '0') {\r\n    ElMessage({ type: 'warning', message: `当前输入的提案内容超过了${suggestContentNumber.value}字，不允许提交！` })\r\n    return\r\n  }\r\n  if (contentCount.value < suggestContentMinNumber.value && route.query.utype == '1' && type == '0') {\r\n    ElMessage({ type: 'warning', message: `当前输入的提案内容少于${suggestContentMinNumber.value}字，不允许提交！` })\r\n    return\r\n  }\r\n  const hasValidSuggestion = opinionsSuggestionsList.value.some(\r\n    (v) => v.suggestion !== null && v.suggestion !== undefined && v.suggestion.trim() !== \"\"\r\n  );\r\n  if (!hasValidSuggestion) {\r\n    ElMessage({ type: 'warning', message: `意见建议清单中至少需要填写一项建议！` });\r\n    return;\r\n  }\r\n\r\n  await formEl.validate((valid, fields) => {\r\n    if (valid) {\r\n      // if (whetherUseIntelligentize.value && !cb) {\r\n      //   if (type) { globalJson(type, cb, _form) } else {\r\n      //     ElMessageBox.confirm('系统将为您进行相似度查询，是否同意执行操作？', '提示', {\r\n      //       closeOnClickModal: false,\r\n      //       confirmButtonText: '同意',\r\n      //       cancelButtonText: '跳过'\r\n      //     }).then(() => { handleSimilarity(true) }).catch(() => { globalJson(type, cb, _form) })\r\n      //   }\r\n      // } else { globalJson(type, cb, _form) }\r\n      globalJson(type, cb, _form)\r\n    } else { ElMessage({ type: 'warning', message: '请根据提示信息完善字段内容！' }) }\r\n  })\r\n}\r\nconst editCallback = (cb, _form) => {\r\n  submitForm(formRef.value, 0, cb, _form)\r\n}\r\nconst globalJson = async (type, cb, _item) => {\r\n  var formObj = {\r\n    id: route.query.id,\r\n    bigThemeId: _item ? _item.SuggestBigType : '', // 提案大类\r\n    smallThemeId: _item ? _item.SuggestSmallType : '', // 提案小类\r\n    suggestSubmitWay: form.suggestSubmitWay,\r\n    title: form.title, // 提案标题\r\n    suggestUserId: form.suggestSubmitWay === 'cppcc_member' ? form.suggestUserId : null,\r\n    delegationId: form.suggestSubmitWay === 'team' ? form.delegationId : null,\r\n    content: form.content,\r\n    isJoinProposal: form.isJoinProposal,\r\n    suggestOpenType: form.suggestOpenType,\r\n    suggestSurveyType: form.suggestSurveyType,\r\n    isMakeMineJob: form.isMakeMineJob,\r\n    notHandleTimeType: form.notHandleTimeType,\r\n    isHopeEnhanceTalk: form.isHopeEnhanceTalk,\r\n    isNeedPaperAnswer: form.isNeedPaperAnswer,\r\n    attachmentIds: fileData.value.map(v => v.id),\r\n  }\r\n  if (route.query.signId == '2') {\r\n    formObj.james = _item.reviewOpinion || ''\r\n    formObj.jordan = _item.reviewResult || ''\r\n    formObj.kobe = _item.transactType || '' // 办理方式\r\n    formObj.duncan = _item.mainHandleOfficeId.map(v => v).join(',') || '' // 主办单位\r\n    formObj.wade = _item.handleOfficeIds.map(v => v).join(',') || '' // 协办单位、分办\r\n  }\r\n  try {\r\n    const { code } = await api.globalJson(route.query.id ? '/proposal/edit' : '/proposal/add', {\r\n      form: formObj,\r\n      isSaveDraft: type,\r\n      joinUsers: form.isJoinProposal ? form.joinUsers : [],\r\n      hopeHandleOfficeIds: form.hopeHandleOfficeIds,\r\n      contacters: contactPersonList.value.filter(v => v.contactName.replace(/(^\\s*)|(\\s*$)/g, '') || v.contactPhone.replace(/(^\\s*)|(\\s*$)/g, '') || v.contactAddress.replace(/(^\\s*)|(\\s*$)/g, '')).map(v => ({ contacterName: v.contactName, contacterMobile: v.contactPhone, contacterAddress: v.contactAddress })),\r\n      proposalInventories: opinionsSuggestionsList.value.map(v => ({ content: v.suggestion, replenish: v.explanation }))\r\n    })\r\n    if (code === 200) {\r\n      if (route.query.clueListId) {\r\n        proposalClueUse()\r\n      } else {\r\n        if (cb) {\r\n          return cb()\r\n        }\r\n        ElMessage({ type: 'success', message: route.query.id ? '编辑成功' : '提交成功' })\r\n        if (route.query.id || route.query.anewId || route.query.clueListId) {\r\n          qiankunMicro.setGlobalState({\r\n            closeOpenRoute: { openId: route.query.oldRouteId, closeId: route.query.routeId }\r\n          })\r\n        } else {\r\n          qiankunMicro.setGlobalState({\r\n            openRoute: { name: '我的提案', path: '/proposal/MyLedSuggest' }\r\n          })\r\n          store.commit('setRefreshRoute', 'SubmitSuggest')\r\n          setTimeout(() => {\r\n            store.commit('setRefreshRoute', '')\r\n          }, 222)\r\n        }\r\n      }\r\n    }\r\n  } catch (err) {\r\n    loading.value = false\r\n  }\r\n}\r\nconst proposalClueUse = async () => {\r\n  const { code } = await api.proposalClueUse({ detailId: route.query.clueListId })\r\n  if (code === 200) {\r\n    ElMessage({ type: 'success', message: '提交成功' })\r\n    qiankunMicro.setGlobalState({ closeOpenRoute: { openId: route.query.oldRouteId, closeId: route.query.routeId } })\r\n  }\r\n}\r\nconst resetForm = () => {\r\n  if (route.query.id || route.query.anewId || route.query.clueListId) {\r\n    qiankunMicro.setGlobalState({ closeOpenRoute: { openId: route.query.oldRouteId, closeId: route.query.routeId } })\r\n  } else {\r\n    store.commit('setRefreshRoute', 'SubmitSuggest')\r\n    setTimeout(() => {\r\n      store.commit('setRefreshRoute', '')\r\n    }, 222)\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.SubmitSuggest {\r\n  width: 100%;\r\n  height: 100%;\r\n\r\n  .SubmitSuggestBody {\r\n    width: 990px;\r\n    margin: 20px auto;\r\n    background-color: #fff;\r\n    box-shadow: 0px 3px 15px 1px rgba(0, 0, 0, 0.16);\r\n\r\n    .SubmitSuggestNameBody {\r\n      padding: var(--zy-distance-one);\r\n      padding-bottom: 0;\r\n\r\n      .global-dynamic-title {\r\n        border-bottom: 3px solid var(--zy-el-color-primary);\r\n      }\r\n    }\r\n\r\n    .globalPaperForm {\r\n      width: 100%;\r\n      padding: var(--zy-distance-one);\r\n      padding-top: 0;\r\n\r\n      .zy-el-form-item {\r\n        width: 50%;\r\n        margin: 0;\r\n        border-bottom: 1px solid var(--zy-el-color-primary);\r\n\r\n        .zy-el-form-item__label {\r\n          width: 138px;\r\n          justify-content: center;\r\n        }\r\n\r\n        .zy-el-form-item__content {\r\n          border-left: 1px solid var(--zy-el-color-primary);\r\n          border-right: 1px solid transparent;\r\n\r\n          &>.simple-select-person {\r\n            box-shadow: 0 0 0 0 !important;\r\n          }\r\n\r\n          &>.zy-el-input,\r\n          .zy-el-input-number {\r\n            width: 100%;\r\n\r\n            .zy-el-input__wrapper {\r\n              box-shadow: 0 0 0 0 !important;\r\n            }\r\n          }\r\n\r\n          &>.zy-el-select,\r\n          .zy-el-select-v2 {\r\n            .zy-el-select__wrapper {\r\n              box-shadow: 0 0 0 0 !important;\r\n            }\r\n          }\r\n\r\n          &>.zy-el-radio-group {\r\n            padding-left: 15px;\r\n          }\r\n\r\n          &>.zy-el-date-editor {\r\n            width: 100%;\r\n\r\n            &>.zy-el-input__wrapper {\r\n              width: 100%;\r\n              box-shadow: 0 0 0 0 !important;\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      .SubmitSuggestLeft {\r\n        .zy-el-form-item__content {\r\n          border-right-color: var(--zy-el-color-primary);\r\n        }\r\n      }\r\n\r\n      .SubmitSuggestTitle {\r\n        width: 100%;\r\n\r\n        .zy-el-form-item__content {\r\n          border-right-color: transparent;\r\n        }\r\n      }\r\n\r\n      .SubmitSuggestButton {\r\n        .zy-el-form-item__content {\r\n          flex-wrap: nowrap;\r\n          justify-content: space-between;\r\n\r\n          .SubmitSuggestContentNumber {\r\n            padding: 0 10px;\r\n            color: var(--zy-el-color-error);\r\n            font-size: var(--zy-text-font-size);\r\n            line-height: var(--zy-line-height);\r\n          }\r\n\r\n          .SubmitSuggestUpload {\r\n            margin-left: 12px;\r\n            margin-right: 12px;\r\n          }\r\n\r\n          .zy-el-button {\r\n            --zy-el-button-size: var(--zy-height-routine);\r\n          }\r\n        }\r\n      }\r\n\r\n      .TinyMceEditor {\r\n        border-bottom: 1px solid var(--zy-el-color-primary);\r\n      }\r\n\r\n      .opinionsSuggestionsList {\r\n        width: 100%;\r\n\r\n        .opinionsSuggestionsListHead,\r\n        .opinionsSuggestionsListBody {\r\n          width: 100%;\r\n          display: flex;\r\n        }\r\n\r\n        .opinionsSuggestionsListBody {\r\n          border-top: 1px solid var(--zy-el-color-primary);\r\n        }\r\n\r\n        .row1 {\r\n          flex: 1;\r\n        }\r\n\r\n        .row2 {\r\n          flex: 2;\r\n        }\r\n\r\n        .row3 {\r\n          flex: 3;\r\n        }\r\n\r\n        .opinionsSuggestionsListItem {\r\n          height: 40px;\r\n          line-height: 40px;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n\r\n          &>.zy-el-input {\r\n            width: 100%;\r\n\r\n            .zy-el-input__wrapper {\r\n              box-shadow: 0 0 0 0 !important;\r\n            }\r\n          }\r\n\r\n          .zy-el-link {\r\n            font-size: 18px;\r\n            line-height: 24px;\r\n          }\r\n\r\n          .zy-el-link+.zy-el-link {\r\n            margin-left: 12px;\r\n          }\r\n        }\r\n\r\n        .opinionsSuggestionsListItem+.opinionsSuggestionsListItem {\r\n          border-left: 1px solid var(--zy-el-color-primary);\r\n        }\r\n      }\r\n\r\n      .SubmitSuggestFormUpload {\r\n        width: 100%;\r\n\r\n        .zy-el-form-item__content {\r\n          padding: 15px;\r\n          border-right-color: transparent;\r\n\r\n          .SubmitSuggestFormInfo {\r\n            width: 100%;\r\n            display: flex;\r\n          }\r\n        }\r\n      }\r\n\r\n      .SubmitSuggestFormItem {\r\n        width: 100%;\r\n\r\n        .zy-el-form-item__content {\r\n          padding: 0 15px;\r\n          border-right-color: transparent;\r\n\r\n          .SubmitSuggestFormInfo {\r\n            width: 100%;\r\n            display: flex;\r\n          }\r\n        }\r\n      }\r\n\r\n      .SubmitSuggestContactPerson {\r\n        width: 100%;\r\n\r\n        .SubmitSuggestContactPersonHead,\r\n        .SubmitSuggestContactPersonBody {\r\n          width: 100%;\r\n          display: flex;\r\n        }\r\n\r\n        .SubmitSuggestContactPersonBody {\r\n          border-top: 1px solid var(--zy-el-color-primary);\r\n        }\r\n\r\n        .row1 {\r\n          flex: 1;\r\n        }\r\n\r\n        .row2 {\r\n          flex: 2;\r\n        }\r\n\r\n        .row3 {\r\n          flex: 3;\r\n        }\r\n\r\n        .SubmitSuggestContactPersonItem {\r\n          height: 40px;\r\n          line-height: 40px;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n\r\n          &>.zy-el-input {\r\n            width: 100%;\r\n\r\n            .zy-el-input__wrapper {\r\n              box-shadow: 0 0 0 0 !important;\r\n            }\r\n          }\r\n\r\n          .zy-el-link {\r\n            font-size: 18px;\r\n            line-height: 24px;\r\n          }\r\n\r\n          .zy-el-link+.zy-el-link {\r\n            margin-left: 12px;\r\n          }\r\n        }\r\n\r\n        .SubmitSuggestContactPersonItem+.SubmitSuggestContactPersonItem {\r\n          border-left: 1px solid var(--zy-el-color-primary);\r\n        }\r\n      }\r\n\r\n      .globalPaperFormButton {\r\n        width: 100%;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        padding-top: 22px;\r\n\r\n        .zy-el-button+.zy-el-button {\r\n          margin-left: var(--zy-distance-two);\r\n        }\r\n      }\r\n    }\r\n\r\n    .SuggestSegmentation {\r\n      width: 100%;\r\n      height: 10px;\r\n      background-color: var(--zy-el-color-info-light-9);\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;+CA0MA,qJAAAA,mBAAA,YAAAA,oBAAA,WAAAC,CAAA,SAAAC,CAAA,EAAAD,CAAA,OAAAE,CAAA,GAAAC,MAAA,CAAAC,SAAA,EAAAC,CAAA,GAAAH,CAAA,CAAAI,cAAA,EAAAC,CAAA,GAAAJ,MAAA,CAAAK,cAAA,cAAAP,CAAA,EAAAD,CAAA,EAAAE,CAAA,IAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,CAAAO,KAAA,KAAAC,CAAA,wBAAAC,MAAA,GAAAA,MAAA,OAAAC,CAAA,GAAAF,CAAA,CAAAG,QAAA,kBAAAC,CAAA,GAAAJ,CAAA,CAAAK,aAAA,uBAAAC,CAAA,GAAAN,CAAA,CAAAO,WAAA,8BAAAC,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAC,MAAA,CAAAK,cAAA,CAAAP,CAAA,EAAAD,CAAA,IAAAS,KAAA,EAAAP,CAAA,EAAAiB,UAAA,MAAAC,YAAA,MAAAC,QAAA,SAAApB,CAAA,CAAAD,CAAA,WAAAkB,MAAA,mBAAAjB,CAAA,IAAAiB,MAAA,YAAAA,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,gBAAAoB,KAAArB,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAK,CAAA,GAAAV,CAAA,IAAAA,CAAA,CAAAI,SAAA,YAAAmB,SAAA,GAAAvB,CAAA,GAAAuB,SAAA,EAAAX,CAAA,GAAAT,MAAA,CAAAqB,MAAA,CAAAd,CAAA,CAAAN,SAAA,GAAAU,CAAA,OAAAW,OAAA,CAAApB,CAAA,gBAAAE,CAAA,CAAAK,CAAA,eAAAH,KAAA,EAAAiB,gBAAA,CAAAzB,CAAA,EAAAC,CAAA,EAAAY,CAAA,MAAAF,CAAA,aAAAe,SAAA1B,CAAA,EAAAD,CAAA,EAAAE,CAAA,mBAAA0B,IAAA,YAAAC,GAAA,EAAA5B,CAAA,CAAA6B,IAAA,CAAA9B,CAAA,EAAAE,CAAA,cAAAD,CAAA,aAAA2B,IAAA,WAAAC,GAAA,EAAA5B,CAAA,QAAAD,CAAA,CAAAsB,IAAA,GAAAA,IAAA,MAAAS,CAAA,qBAAAC,CAAA,qBAAAC,CAAA,gBAAAC,CAAA,gBAAAC,CAAA,gBAAAZ,UAAA,cAAAa,kBAAA,cAAAC,2BAAA,SAAAC,CAAA,OAAApB,MAAA,CAAAoB,CAAA,EAAA1B,CAAA,qCAAA2B,CAAA,GAAApC,MAAA,CAAAqC,cAAA,EAAAC,CAAA,GAAAF,CAAA,IAAAA,CAAA,CAAAA,CAAA,CAAAG,MAAA,QAAAD,CAAA,IAAAA,CAAA,KAAAvC,CAAA,IAAAG,CAAA,CAAAyB,IAAA,CAAAW,CAAA,EAAA7B,CAAA,MAAA0B,CAAA,GAAAG,CAAA,OAAAE,CAAA,GAAAN,0BAAA,CAAAjC,SAAA,GAAAmB,SAAA,CAAAnB,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAc,CAAA,YAAAM,sBAAA3C,CAAA,gCAAA4C,OAAA,WAAA7C,CAAA,IAAAkB,MAAA,CAAAjB,CAAA,EAAAD,CAAA,YAAAC,CAAA,gBAAA6C,OAAA,CAAA9C,CAAA,EAAAC,CAAA,sBAAA8C,cAAA9C,CAAA,EAAAD,CAAA,aAAAgD,OAAA9C,CAAA,EAAAK,CAAA,EAAAG,CAAA,EAAAE,CAAA,QAAAE,CAAA,GAAAa,QAAA,CAAA1B,CAAA,CAAAC,CAAA,GAAAD,CAAA,EAAAM,CAAA,mBAAAO,CAAA,CAAAc,IAAA,QAAAZ,CAAA,GAAAF,CAAA,CAAAe,GAAA,EAAAE,CAAA,GAAAf,CAAA,CAAAP,KAAA,SAAAsB,CAAA,uBAAAA,CAAA,IAAA1B,CAAA,CAAAyB,IAAA,CAAAC,CAAA,eAAA/B,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,CAAAmB,OAAA,EAAAC,IAAA,WAAAlD,CAAA,IAAA+C,MAAA,SAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,gBAAAX,CAAA,IAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,QAAAZ,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,EAAAoB,IAAA,WAAAlD,CAAA,IAAAe,CAAA,CAAAP,KAAA,GAAAR,CAAA,EAAAS,CAAA,CAAAM,CAAA,gBAAAf,CAAA,WAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,SAAAA,CAAA,CAAAE,CAAA,CAAAe,GAAA,SAAA3B,CAAA,EAAAK,CAAA,oBAAAE,KAAA,WAAAA,MAAAR,CAAA,EAAAI,CAAA,aAAA+C,2BAAA,eAAApD,CAAA,WAAAA,CAAA,EAAAE,CAAA,IAAA8C,MAAA,CAAA/C,CAAA,EAAAI,CAAA,EAAAL,CAAA,EAAAE,CAAA,gBAAAA,CAAA,GAAAA,CAAA,GAAAA,CAAA,CAAAiD,IAAA,CAAAC,0BAAA,EAAAA,0BAAA,IAAAA,0BAAA,qBAAA1B,iBAAA1B,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAE,CAAA,GAAAwB,CAAA,mBAAArB,CAAA,EAAAE,CAAA,QAAAL,CAAA,KAAA0B,CAAA,QAAAoB,KAAA,sCAAA9C,CAAA,KAAA2B,CAAA,oBAAAxB,CAAA,QAAAE,CAAA,WAAAH,KAAA,EAAAR,CAAA,EAAAqD,IAAA,eAAAjD,CAAA,CAAAkD,MAAA,GAAA7C,CAAA,EAAAL,CAAA,CAAAwB,GAAA,GAAAjB,CAAA,UAAAE,CAAA,GAAAT,CAAA,CAAAmD,QAAA,MAAA1C,CAAA,QAAAE,CAAA,GAAAyC,mBAAA,CAAA3C,CAAA,EAAAT,CAAA,OAAAW,CAAA,QAAAA,CAAA,KAAAmB,CAAA,mBAAAnB,CAAA,qBAAAX,CAAA,CAAAkD,MAAA,EAAAlD,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAsD,KAAA,GAAAtD,CAAA,CAAAwB,GAAA,sBAAAxB,CAAA,CAAAkD,MAAA,QAAAhD,CAAA,KAAAwB,CAAA,QAAAxB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAwB,GAAA,EAAAxB,CAAA,CAAAuD,iBAAA,CAAAvD,CAAA,CAAAwB,GAAA,uBAAAxB,CAAA,CAAAkD,MAAA,IAAAlD,CAAA,CAAAwD,MAAA,WAAAxD,CAAA,CAAAwB,GAAA,GAAAtB,CAAA,GAAA0B,CAAA,MAAAK,CAAA,GAAAX,QAAA,CAAA3B,CAAA,EAAAE,CAAA,EAAAG,CAAA,oBAAAiC,CAAA,CAAAV,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAiD,IAAA,GAAApB,CAAA,GAAAF,CAAA,EAAAM,CAAA,CAAAT,GAAA,KAAAM,CAAA,qBAAA1B,KAAA,EAAA6B,CAAA,CAAAT,GAAA,EAAAyB,IAAA,EAAAjD,CAAA,CAAAiD,IAAA,kBAAAhB,CAAA,CAAAV,IAAA,KAAArB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAkD,MAAA,YAAAlD,CAAA,CAAAwB,GAAA,GAAAS,CAAA,CAAAT,GAAA,mBAAA4B,oBAAAzD,CAAA,EAAAE,CAAA,QAAAG,CAAA,GAAAH,CAAA,CAAAqD,MAAA,EAAAhD,CAAA,GAAAP,CAAA,CAAAa,QAAA,CAAAR,CAAA,OAAAE,CAAA,KAAAN,CAAA,SAAAC,CAAA,CAAAsD,QAAA,qBAAAnD,CAAA,IAAAL,CAAA,CAAAa,QAAA,CAAAiD,MAAA,KAAA5D,CAAA,CAAAqD,MAAA,aAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,EAAAwD,mBAAA,CAAAzD,CAAA,EAAAE,CAAA,eAAAA,CAAA,CAAAqD,MAAA,kBAAAlD,CAAA,KAAAH,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,uCAAA1D,CAAA,iBAAA8B,CAAA,MAAAzB,CAAA,GAAAiB,QAAA,CAAApB,CAAA,EAAAP,CAAA,CAAAa,QAAA,EAAAX,CAAA,CAAA2B,GAAA,mBAAAnB,CAAA,CAAAkB,IAAA,SAAA1B,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,GAAAnB,CAAA,CAAAmB,GAAA,EAAA3B,CAAA,CAAAsD,QAAA,SAAArB,CAAA,MAAAvB,CAAA,GAAAF,CAAA,CAAAmB,GAAA,SAAAjB,CAAA,GAAAA,CAAA,CAAA0C,IAAA,IAAApD,CAAA,CAAAF,CAAA,CAAAgE,UAAA,IAAApD,CAAA,CAAAH,KAAA,EAAAP,CAAA,CAAA+D,IAAA,GAAAjE,CAAA,CAAAkE,OAAA,eAAAhE,CAAA,CAAAqD,MAAA,KAAArD,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,GAAAC,CAAA,CAAAsD,QAAA,SAAArB,CAAA,IAAAvB,CAAA,IAAAV,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,sCAAA7D,CAAA,CAAAsD,QAAA,SAAArB,CAAA,cAAAgC,aAAAlE,CAAA,QAAAD,CAAA,KAAAoE,MAAA,EAAAnE,CAAA,YAAAA,CAAA,KAAAD,CAAA,CAAAqE,QAAA,GAAApE,CAAA,WAAAA,CAAA,KAAAD,CAAA,CAAAsE,UAAA,GAAArE,CAAA,KAAAD,CAAA,CAAAuE,QAAA,GAAAtE,CAAA,WAAAuE,UAAA,CAAAC,IAAA,CAAAzE,CAAA,cAAA0E,cAAAzE,CAAA,QAAAD,CAAA,GAAAC,CAAA,CAAA0E,UAAA,QAAA3E,CAAA,CAAA4B,IAAA,oBAAA5B,CAAA,CAAA6B,GAAA,EAAA5B,CAAA,CAAA0E,UAAA,GAAA3E,CAAA,aAAAyB,QAAAxB,CAAA,SAAAuE,UAAA,MAAAJ,MAAA,aAAAnE,CAAA,CAAA4C,OAAA,CAAAsB,YAAA,cAAAS,KAAA,iBAAAlC,OAAA1C,CAAA,QAAAA,CAAA,WAAAA,CAAA,QAAAE,CAAA,GAAAF,CAAA,CAAAY,CAAA,OAAAV,CAAA,SAAAA,CAAA,CAAA4B,IAAA,CAAA9B,CAAA,4BAAAA,CAAA,CAAAiE,IAAA,SAAAjE,CAAA,OAAA6E,KAAA,CAAA7E,CAAA,CAAA8E,MAAA,SAAAvE,CAAA,OAAAG,CAAA,YAAAuD,KAAA,aAAA1D,CAAA,GAAAP,CAAA,CAAA8E,MAAA,OAAAzE,CAAA,CAAAyB,IAAA,CAAA9B,CAAA,EAAAO,CAAA,UAAA0D,IAAA,CAAAxD,KAAA,GAAAT,CAAA,CAAAO,CAAA,GAAA0D,IAAA,CAAAX,IAAA,OAAAW,IAAA,SAAAA,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,YAAAvD,CAAA,CAAAuD,IAAA,GAAAvD,CAAA,gBAAAqD,SAAA,QAAA/D,CAAA,iCAAAoC,iBAAA,CAAAhC,SAAA,GAAAiC,0BAAA,EAAA9B,CAAA,CAAAoC,CAAA,mBAAAlC,KAAA,EAAA4B,0BAAA,EAAAjB,YAAA,SAAAb,CAAA,CAAA8B,0BAAA,mBAAA5B,KAAA,EAAA2B,iBAAA,EAAAhB,YAAA,SAAAgB,iBAAA,CAAA2C,WAAA,GAAA7D,MAAA,CAAAmB,0BAAA,EAAArB,CAAA,wBAAAhB,CAAA,CAAAgF,mBAAA,aAAA/E,CAAA,QAAAD,CAAA,wBAAAC,CAAA,IAAAA,CAAA,CAAAgF,WAAA,WAAAjF,CAAA,KAAAA,CAAA,KAAAoC,iBAAA,6BAAApC,CAAA,CAAA+E,WAAA,IAAA/E,CAAA,CAAAkF,IAAA,OAAAlF,CAAA,CAAAmF,IAAA,aAAAlF,CAAA,WAAAE,MAAA,CAAAiF,cAAA,GAAAjF,MAAA,CAAAiF,cAAA,CAAAnF,CAAA,EAAAoC,0BAAA,KAAApC,CAAA,CAAAoF,SAAA,GAAAhD,0BAAA,EAAAnB,MAAA,CAAAjB,CAAA,EAAAe,CAAA,yBAAAf,CAAA,CAAAG,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAmB,CAAA,GAAA1C,CAAA,KAAAD,CAAA,CAAAsF,KAAA,aAAArF,CAAA,aAAAiD,OAAA,EAAAjD,CAAA,OAAA2C,qBAAA,CAAAG,aAAA,CAAA3C,SAAA,GAAAc,MAAA,CAAA6B,aAAA,CAAA3C,SAAA,EAAAU,CAAA,iCAAAd,CAAA,CAAA+C,aAAA,GAAAA,aAAA,EAAA/C,CAAA,CAAAuF,KAAA,aAAAtF,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,EAAAG,CAAA,eAAAA,CAAA,KAAAA,CAAA,GAAA8E,OAAA,OAAA5E,CAAA,OAAAmC,aAAA,CAAAzB,IAAA,CAAArB,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,GAAAG,CAAA,UAAAV,CAAA,CAAAgF,mBAAA,CAAA9E,CAAA,IAAAU,CAAA,GAAAA,CAAA,CAAAqD,IAAA,GAAAd,IAAA,WAAAlD,CAAA,WAAAA,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAQ,KAAA,GAAAG,CAAA,CAAAqD,IAAA,WAAArB,qBAAA,CAAAD,CAAA,GAAAzB,MAAA,CAAAyB,CAAA,EAAA3B,CAAA,gBAAAE,MAAA,CAAAyB,CAAA,EAAA/B,CAAA,iCAAAM,MAAA,CAAAyB,CAAA,6DAAA3C,CAAA,CAAAyF,IAAA,aAAAxF,CAAA,QAAAD,CAAA,GAAAG,MAAA,CAAAF,CAAA,GAAAC,CAAA,gBAAAG,CAAA,IAAAL,CAAA,EAAAE,CAAA,CAAAuE,IAAA,CAAApE,CAAA,UAAAH,CAAA,CAAAwF,OAAA,aAAAzB,KAAA,WAAA/D,CAAA,CAAA4E,MAAA,SAAA7E,CAAA,GAAAC,CAAA,CAAAyF,GAAA,QAAA1F,CAAA,IAAAD,CAAA,SAAAiE,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,WAAAA,IAAA,CAAAX,IAAA,OAAAW,IAAA,QAAAjE,CAAA,CAAA0C,MAAA,GAAAA,MAAA,EAAAjB,OAAA,CAAArB,SAAA,KAAA6E,WAAA,EAAAxD,OAAA,EAAAmD,KAAA,WAAAA,MAAA5E,CAAA,aAAA4F,IAAA,WAAA3B,IAAA,WAAAP,IAAA,QAAAC,KAAA,GAAA1D,CAAA,OAAAqD,IAAA,YAAAE,QAAA,cAAAD,MAAA,gBAAA1B,GAAA,GAAA5B,CAAA,OAAAuE,UAAA,CAAA3B,OAAA,CAAA6B,aAAA,IAAA1E,CAAA,WAAAE,CAAA,kBAAAA,CAAA,CAAA2F,MAAA,OAAAxF,CAAA,CAAAyB,IAAA,OAAA5B,CAAA,MAAA2E,KAAA,EAAA3E,CAAA,CAAA4F,KAAA,cAAA5F,CAAA,IAAAD,CAAA,MAAA8F,IAAA,WAAAA,KAAA,SAAAzC,IAAA,WAAArD,CAAA,QAAAuE,UAAA,IAAAG,UAAA,kBAAA1E,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,cAAAmE,IAAA,KAAApC,iBAAA,WAAAA,kBAAA5D,CAAA,aAAAsD,IAAA,QAAAtD,CAAA,MAAAE,CAAA,kBAAA+F,OAAA5F,CAAA,EAAAE,CAAA,WAAAK,CAAA,CAAAgB,IAAA,YAAAhB,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAE,CAAA,CAAA+D,IAAA,GAAA5D,CAAA,EAAAE,CAAA,KAAAL,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,KAAAM,CAAA,aAAAA,CAAA,QAAAiE,UAAA,CAAAM,MAAA,MAAAvE,CAAA,SAAAA,CAAA,QAAAG,CAAA,QAAA8D,UAAA,CAAAjE,CAAA,GAAAK,CAAA,GAAAF,CAAA,CAAAiE,UAAA,iBAAAjE,CAAA,CAAA0D,MAAA,SAAA6B,MAAA,aAAAvF,CAAA,CAAA0D,MAAA,SAAAwB,IAAA,QAAA9E,CAAA,GAAAT,CAAA,CAAAyB,IAAA,CAAApB,CAAA,eAAAM,CAAA,GAAAX,CAAA,CAAAyB,IAAA,CAAApB,CAAA,qBAAAI,CAAA,IAAAE,CAAA,aAAA4E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,gBAAAuB,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,cAAAxD,CAAA,aAAA8E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,qBAAArD,CAAA,QAAAqC,KAAA,qDAAAuC,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,YAAAT,MAAA,WAAAA,OAAA5D,CAAA,EAAAD,CAAA,aAAAE,CAAA,QAAAsE,UAAA,CAAAM,MAAA,MAAA5E,CAAA,SAAAA,CAAA,QAAAK,CAAA,QAAAiE,UAAA,CAAAtE,CAAA,OAAAK,CAAA,CAAA6D,MAAA,SAAAwB,IAAA,IAAAvF,CAAA,CAAAyB,IAAA,CAAAvB,CAAA,wBAAAqF,IAAA,GAAArF,CAAA,CAAA+D,UAAA,QAAA5D,CAAA,GAAAH,CAAA,aAAAG,CAAA,iBAAAT,CAAA,mBAAAA,CAAA,KAAAS,CAAA,CAAA0D,MAAA,IAAApE,CAAA,IAAAA,CAAA,IAAAU,CAAA,CAAA4D,UAAA,KAAA5D,CAAA,cAAAE,CAAA,GAAAF,CAAA,GAAAA,CAAA,CAAAiE,UAAA,cAAA/D,CAAA,CAAAgB,IAAA,GAAA3B,CAAA,EAAAW,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAU,CAAA,SAAA6C,MAAA,gBAAAU,IAAA,GAAAvD,CAAA,CAAA4D,UAAA,EAAAnC,CAAA,SAAA+D,QAAA,CAAAtF,CAAA,MAAAsF,QAAA,WAAAA,SAAAjG,CAAA,EAAAD,CAAA,oBAAAC,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,qBAAA5B,CAAA,CAAA2B,IAAA,mBAAA3B,CAAA,CAAA2B,IAAA,QAAAqC,IAAA,GAAAhE,CAAA,CAAA4B,GAAA,gBAAA5B,CAAA,CAAA2B,IAAA,SAAAoE,IAAA,QAAAnE,GAAA,GAAA5B,CAAA,CAAA4B,GAAA,OAAA0B,MAAA,kBAAAU,IAAA,yBAAAhE,CAAA,CAAA2B,IAAA,IAAA5B,CAAA,UAAAiE,IAAA,GAAAjE,CAAA,GAAAmC,CAAA,KAAAgE,MAAA,WAAAA,OAAAlG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAoE,UAAA,KAAArE,CAAA,cAAAiG,QAAA,CAAAhG,CAAA,CAAAyE,UAAA,EAAAzE,CAAA,CAAAqE,QAAA,GAAAG,aAAA,CAAAxE,CAAA,GAAAiC,CAAA,OAAAiE,KAAA,WAAAC,OAAApG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAkE,MAAA,KAAAnE,CAAA,QAAAI,CAAA,GAAAH,CAAA,CAAAyE,UAAA,kBAAAtE,CAAA,CAAAuB,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAwB,GAAA,EAAA6C,aAAA,CAAAxE,CAAA,YAAAK,CAAA,YAAA8C,KAAA,8BAAAiD,aAAA,WAAAA,cAAAtG,CAAA,EAAAE,CAAA,EAAAG,CAAA,gBAAAmD,QAAA,KAAA3C,QAAA,EAAA6B,MAAA,CAAA1C,CAAA,GAAAgE,UAAA,EAAA9D,CAAA,EAAAgE,OAAA,EAAA7D,CAAA,oBAAAkD,MAAA,UAAA1B,GAAA,GAAA5B,CAAA,GAAAkC,CAAA,OAAAnC,CAAA;AAAA,SAAAuG,mBAAAlG,CAAA,EAAAJ,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAK,CAAA,EAAAK,CAAA,EAAAE,CAAA,cAAAJ,CAAA,GAAAL,CAAA,CAAAO,CAAA,EAAAE,CAAA,GAAAE,CAAA,GAAAN,CAAA,CAAAD,KAAA,WAAAJ,CAAA,gBAAAL,CAAA,CAAAK,CAAA,KAAAK,CAAA,CAAA4C,IAAA,GAAArD,CAAA,CAAAe,CAAA,IAAAwE,OAAA,CAAAvC,OAAA,CAAAjC,CAAA,EAAAmC,IAAA,CAAAjD,CAAA,EAAAK,CAAA;AAAA,SAAAiG,kBAAAnG,CAAA,6BAAAJ,CAAA,SAAAD,CAAA,GAAAyG,SAAA,aAAAjB,OAAA,WAAAtF,CAAA,EAAAK,CAAA,QAAAK,CAAA,GAAAP,CAAA,CAAAqG,KAAA,CAAAzG,CAAA,EAAAD,CAAA,YAAA2G,MAAAtG,CAAA,IAAAkG,kBAAA,CAAA3F,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAoG,KAAA,EAAAC,MAAA,UAAAvG,CAAA,cAAAuG,OAAAvG,CAAA,IAAAkG,kBAAA,CAAA3F,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAoG,KAAA,EAAAC,MAAA,WAAAvG,CAAA,KAAAsG,KAAA;AADA,OAAOE,GAAG,MAAM,OAAO;AACvB,SAASC,QAAQ,EAAEC,GAAG,EAAEC,WAAW,EAAEC,aAAa,EAAEC,eAAe,EAAEC,QAAQ,QAAQ,KAAK;AAC1F,SAASC,QAAQ,QAAQ,YAAY;AACrC,SAASC,QAAQ,QAAQ,MAAM;AAC/B,SAASC,IAAI,EAAEC,wBAAwB,QAAQ,yBAAyB;AACxE,SAASC,YAAY,QAAQ,2BAA2B;AACxD,SAASC,SAAS,QAAQ,cAAc;AACxC,OAAOC,eAAe,MAAM,kDAAkD;AAC9E,OAAOC,oBAAoB,MAAM,4DAA4D;AAC7F,OAAOC,mBAAmB,MAAM,yDAAyD;AAZzF,IAAAC,WAAA,GAAe;EAAE3C,IAAI,EAAE;AAAgB,CAAC;;;;;IAcxC,IAAM4C,KAAK,GAAGV,QAAQ,CAAC,CAAC;IACxB,IAAMW,KAAK,GAAGV,QAAQ,CAAC,CAAC;IACxB,IAAMW,OAAO,GAAGjB,GAAG,CAAC,KAAK,CAAC;IAC1B,IAAMkB,WAAW,GAAGlB,GAAG,CAAC,EAAE,CAAC;IAC3B,IAAMmB,OAAO,GAAGnB,GAAG,CAAC,CAAC;IACrB,IAAMoB,IAAI,GAAGrB,QAAQ,CAAC;MACpBsB,gBAAgB,EAAE,cAAc;MAChCC,KAAK,EAAE,EAAE;MAAE;MACXC,cAAc,EAAE,EAAE;MAAE;MACpBC,gBAAgB,EAAE,EAAE;MAAE;MACtBC,aAAa,EAAE,EAAE;MACjBC,UAAU,EAAE,EAAE;MACdC,UAAU,EAAE,EAAE;MACdC,MAAM,EAAE,EAAE;MACVC,WAAW,EAAE,EAAE;MACfC,YAAY,EAAE,EAAE;MAChBC,cAAc,EAAE,CAAC;MACjBC,SAAS,EAAE,EAAE;MACbC,OAAO,EAAE,EAAE;MACXC,eAAe,EAAE,UAAU;MAC3BC,iBAAiB,EAAE,GAAG;MACtBC,aAAa,EAAE,GAAG;MAClBC,iBAAiB,EAAE,GAAG;MACtBC,iBAAiB,EAAE,GAAG;MACtBC,iBAAiB,EAAE,GAAG;MACtBC,mBAAmB,EAAE,EAAE;MACvBC,UAAU,EAAE,EAAE;MACdC,YAAY,EAAE,EAAE;MAChBC,KAAK,EAAE,EAAE;MACTC,MAAM,EAAE,EAAE;MACVC,IAAI,EAAE,EAAE;MACRC,MAAM,EAAE,EAAE;MACVC,IAAI,EAAE;IACR,CAAC,CAAC;IACF,IAAMC,KAAK,GAAGjD,QAAQ,CAAC;MACrBsB,gBAAgB,EAAE,CAAC;QAAE4B,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,WAAW;QAAEC,OAAO,EAAE,CAAC,MAAM,EAAE,QAAQ;MAAE,CAAC,CAAC;MACzF7B,KAAK,EAAE,CAAC;QAAE2B,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,SAAS;QAAEC,OAAO,EAAE,CAAC,MAAM,EAAE,QAAQ;MAAE,CAAC,CAAC;MAC5ElB,OAAO,EAAE,CAAC;QAAEgB,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,SAAS;QAAEC,OAAO,EAAE,CAAC,MAAM,EAAE,QAAQ;MAAE,CAAC,CAAC;MAC9E1B,aAAa,EAAE,CAAC;QAAEwB,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,QAAQ;QAAEC,OAAO,EAAE,CAAC,MAAM,EAAE,QAAQ;MAAE,CAAC,CAAC;MACnFrB,YAAY,EAAE,CAAC;QAAEmB,QAAQ,EAAE,KAAK;QAAEC,OAAO,EAAE,WAAW;QAAEC,OAAO,EAAE,CAAC,MAAM,EAAE,QAAQ;MAAE,CAAC,CAAC;MACtFpB,cAAc,EAAE,CAAC;QAAEkB,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,WAAW;QAAEC,OAAO,EAAE,CAAC,MAAM,EAAE,QAAQ;MAAE,CAAC,CAAC;MACvFnB,SAAS,EAAE,CAAC;QAAEnH,IAAI,EAAE,OAAO;QAAEoI,QAAQ,EAAE,KAAK;QAAEC,OAAO,EAAE,UAAU;QAAEC,OAAO,EAAE,CAAC,MAAM,EAAE,QAAQ;MAAE,CAAC;IAClG,CAAC,CAAC;IAEF,IAAMC,IAAI,GAAG,SAAPA,IAAIA,CAAA,EAAS;MACjB,OAAO,sCAAsC,CAACC,OAAO,CAAC,OAAO,EAAE,UAACtJ,CAAC,EAAK;QACpE,IAAIZ,CAAC,GAAImK,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAI,CAAC;UAC9B7H,CAAC,GAAG3B,CAAC,IAAI,GAAG,GAAGZ,CAAC,GAAIA,CAAC,GAAG,GAAG,GAAI,GAAG;QACpC,OAAOuC,CAAC,CAAC8H,QAAQ,CAAC,EAAE,CAAC;MACvB,CAAC,CAAC;IACJ,CAAC;IACD,IAAMC,cAAc,GAAG;MACrBC,iBAAiB,EAAE;QACjBC,KAAK,EAAE;UACL,YAAY,EAAE,SAAS;UACvB,aAAa,EAAE,KAAK;UACpB,aAAa,EAAE,MAAM;UACrB,WAAW,EAAE,MAAM;UACnB,aAAa,EAAE;QACjB,CAAC;QACDC,SAAS,EAAE;UACTC,IAAI,EAAE;YACJ,YAAY,EAAE,SAAS;YACvB,aAAa,EAAE,KAAK;YACpB,aAAa,EAAE,MAAM;YACrB,WAAW,EAAE,MAAM;YACnB,aAAa,EAAE;UACjB;QACF;MACF,CAAC;MACDC,iBAAiB,EAAE,SAAnBA,iBAAiBA,CAAGC,MAAM,EAAEC,IAAI,EAAK;QACnC5D,QAAQ,CAAC,YAAM;UACb4D,IAAI,CAACC,MAAM,CAACC,WAAW,CAAC,aAAa,CAAC;UACtC9D,QAAQ,CAAC,YAAM;YACb4D,IAAI,CAACC,MAAM,CAACE,SAAS,CAACC,QAAQ,CAAC,CAAC;UAClC,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ,CAAC;MACDC,oBAAoB,EAAE,SAAtBA,oBAAoBA,CAAGC,MAAM,EAAK;QAChClE,QAAQ,CAAC,YAAM;UACbkE,MAAM,CAACJ,WAAW,CAAC,aAAa,CAAC;UACjC9D,QAAQ,CAAC,YAAM;YACbkE,MAAM,CAACH,SAAS,CAACC,QAAQ,CAAC,CAAC;UAC7B,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ,CAAC;MACDG,OAAO,EAAE,0DAA0D;MACnEC,IAAI,EAAE;QACJC,UAAU,EAAE;UAAEnD,KAAK,EAAE,QAAQ;UAAEoD,KAAK,EAAE;QAAa,CAAC;QACpDC,WAAW,EAAE;UAAErD,KAAK,EAAE,MAAM;UAAEoD,KAAK,EAAE;QAAc,CAAC;QACpDE,QAAQ,EAAE;UAAEtD,KAAK,EAAE,MAAM;UAAEoD,KAAK,EAAE;QAAW;MAC/C;IACF,CAAC;IACD,IAAMnD,cAAc,GAAGvB,GAAG,CAAC,EAAE,CAAC;IAC9B,IAAMwB,gBAAgB,GAAGxB,GAAG,CAAC,EAAE,CAAC;IAChC,IAAM6E,kBAAkB,GAAG7E,GAAG,CAAC,EAAE,CAAC;IAClC,IAAM8E,oBAAoB,GAAG9E,GAAG,CAAC,IAAI,CAAC;IACtC,IAAM+E,iBAAiB,GAAG/E,GAAG,CAAC,CAAC,CAAC;IAChC,IAAMgF,uBAAuB,GAAGhF,GAAG,CAAC,CAAC,CAAC;IACtC,IAAMiF,UAAU,GAAGjF,GAAG,CAAC,EAAE,CAAC;IAC1B,IAAMkF,YAAY,GAAGlF,GAAG,CAAC,CAAC,CAAC;IAC3B,IAAMmF,QAAQ,GAAGnF,GAAG,CAAC,EAAE,CAAC;IACxB,IAAMoF,cAAc,GAAGpF,GAAG,CAAC,EAAE,CAAC;IAC9B,IAAMqF,mBAAmB,GAAGrF,GAAG,CAAC,EAAE,CAAC;IACnC,IAAMsF,qBAAqB,GAAGtF,GAAG,CAAC,EAAE,CAAC;IACrC,IAAMuF,qBAAqB,GAAGvF,GAAG,CAAC,EAAE,CAAC;IACrC,IAAMwF,qBAAqB,GAAGxF,GAAG,CAAC,EAAE,CAAC;IACrC,IAAMyF,iBAAiB,GAAGzF,GAAG,CAAC,EAAE,CAAC;IACjC,IAAM0F,qBAAqB,GAAG1F,GAAG,CAAC,EAAE,CAAC;IACrC,IAAMkC,eAAe,GAAGlC,GAAG,CAAC,EAAE,CAAC;IAC/B,IAAMmC,iBAAiB,GAAGnC,GAAG,CAAC,EAAE,CAAC;IACjC,IAAMqC,iBAAiB,GAAGrC,GAAG,CAAC,EAAE,CAAC;IACjC,IAAMsC,iBAAiB,GAAGtC,GAAG,CAAC,EAAE,CAAC;IACjC,IAAMoC,aAAa,GAAGpC,GAAG,CAAC,EAAE,CAAC;IAC7B,IAAMuC,iBAAiB,GAAGvC,GAAG,CAAC,EAAE,CAAC;IACjC,IAAM2F,iBAAiB,GAAG3F,GAAG,CAAC,CAAC;MAAE4F,EAAE,EAAExC,IAAI,CAAC,CAAC;MAAEyC,WAAW,EAAE,EAAE;MAAEC,YAAY,EAAE,EAAE;MAAEC,cAAc,EAAE;IAAG,CAAC,CAAC,CAAC;IACtG,IAAMC,uBAAuB,GAAGhG,GAAG,CAAC,CAAC;MAAE4F,EAAE,EAAExC,IAAI,CAAC,CAAC;MAAE6C,UAAU,EAAE,EAAE;MAAEC,WAAW,EAAE;IAAG,CAAC,EAAE;MAAEN,EAAE,EAAExC,IAAI,CAAC,CAAC;MAAE6C,UAAU,EAAE,EAAE;MAAEC,WAAW,EAAE;IAAG,CAAC,EAAE;MAAEN,EAAE,EAAExC,IAAI,CAAC,CAAC;MAAE6C,UAAU,EAAE,EAAE;MAAEC,WAAW,EAAE;IAAG,CAAC,CAAC,CAAC;IACxL,IAAMC,QAAQ,GAAGnG,GAAG,CAAC,KAAK,CAAC;IAC3B,IAAMoG,QAAQ,GAAGpG,GAAG,CAAC,KAAK,CAAC;IAC3B,IAAMqG,UAAU,GAAGrG,GAAG,CAAC,KAAK,CAAC;IAC7B,IAAMsG,OAAO,GAAGtG,GAAG,CAAC,CAAC,aAAa,CAAC,CAAC;IACpC,IAAMuG,UAAU,GAAGvG,GAAG,CAAC,KAAK,CAAC;IAC7B,IAAMwG,SAAS,GAAGxG,GAAG,CAAC,EAAE,CAAC;IAEzB,IAAMyG,IAAI,GAAGzG,GAAG,CAAC,KAAK,CAAC;IACvB,IAAM0G,MAAM,GAAG1G,GAAG,CAAC,KAAK,CAAC;IACzB,IAAM2G,QAAQ,GAAG3G,GAAG,CAAC,KAAK,CAAC;IAC3B,IAAM4G,aAAa,GAAG5G,GAAG,CAAC,KAAK,CAAC;IAChC,IAAM6G,UAAU,GAAG7G,GAAG,CAAC,CAAC,CAAC,CAAC;IAC1B,IAAI8G,KAAK,GAAG,IAAI;IAChB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IAEA7G,WAAW,CAAC,YAAM;MAChBQ,YAAY,CAACsG,cAAc,CAAC;QAAEC,UAAU,EAAE;MAA4B,CAAC,CAAC;MACxE,IAAMC,YAAY,GAAGC,IAAI,CAACC,KAAK,CAACC,cAAc,CAACC,OAAO,CAAC,cAAc,CAAC,CAAC,IAAI,EAAE;MAC7E,IAAIJ,YAAY,EAAE;QAChBxG,YAAY,CAACsG,cAAc,CAAC;UAC1BO,YAAY,EAAE;YACZC,YAAY,EAAE,IAAI;YAClBC,UAAU,EAAEP,YAAY,CAAC9B,QAAQ;YACjCsC,YAAY,EAAE;cAAEC,IAAI,EAAET,YAAY,CAACU,MAAM;cAAEC,KAAK,EAAE;gBAAEC,MAAM,EAAE;cAAI;YAAE,CAAC;YACnEC,iBAAiB,EAAEb,YAAY,CAACc;UAClC;QACF,CAAC,CAAC;QACFX,cAAc,CAACY,OAAO,CAAC,cAAc,EAAEd,IAAI,CAACe,SAAS,CAAC,EAAE,CAAC,CAAC;QAC1DnB,KAAK,GAAGoB,UAAU,CAAC,YAAM;UACvBzH,YAAY,CAACsG,cAAc,CAAC;YAC1BO,YAAY,EAAE;cACZC,YAAY,EAAE,IAAI;cAClBC,UAAU,EAAEP,YAAY,CAAC9B,QAAQ;cACjCsC,YAAY,EAAE,CAAC;YACjB;UACF,CAAC,CAAC;QACJ,CAAC,EAAE,IAAI,CAAC;MACV;MACAjB,SAAS,CAAC9M,KAAK,GAAGqH,KAAK,CAACoH,KAAK,CAACtN,IAAI;MAClC,IAAIkG,KAAK,CAACoH,KAAK,CAACC,UAAU,EAAE;QAC1BC,gBAAgB,CAAC,CAAC;MACpB;MACAC,gBAAgB,CAAC,CAAC;MAClBC,eAAe,CAAC,CAAC;MACjBC,cAAc,CAAC,CAAC;MAChBC,kBAAkB,CAAC,CAAC;MACpBC,qBAAqB,CAAC,CAAC;MACvB,IAAIlC,SAAS,CAAC9M,KAAK,KAAK,OAAO,IAAIqH,KAAK,CAACoH,KAAK,CAACQ,MAAM,EAAE;QACrDxC,QAAQ,CAACzM,KAAK,GAAG,IAAI;QACrB0M,QAAQ,CAAC1M,KAAK,GAAG,IAAI;MACvB;MACA,IAAIqH,KAAK,CAACoH,KAAK,CAACvC,EAAE,IAAI7E,KAAK,CAACoH,KAAK,CAACQ,MAAM,EAAE;QACxCxC,QAAQ,CAACzM,KAAK,GAAG,IAAI;QACrBkP,cAAc,CAAC,CAAC;MAClB,CAAC,MAAM;QACLtC,OAAO,CAAC5M,KAAK,GAAG,CAAC,aAAa,CAAC;QAC/B,IAAI6G,IAAI,CAAC7G,KAAK,CAACmP,eAAe,CAACC,QAAQ,CAAC,kBAAkB,CAAC,EAAE;UAC3D3C,QAAQ,CAACzM,KAAK,GAAG,IAAI;QACvB;QACA,IAAI6G,IAAI,CAAC7G,KAAK,CAACmP,eAAe,CAACC,QAAQ,CAAC,cAAc,CAAC,EAAE;UACvD3C,QAAQ,CAACzM,KAAK,GAAG,IAAI;UACrB0M,QAAQ,CAAC1M,KAAK,GAAG,IAAI;UACrB0H,IAAI,CAACK,aAAa,GAAGlB,IAAI,CAAC7G,KAAK,CAACkM,EAAE;UAClCmD,eAAe,CAACxI,IAAI,CAAC7G,KAAK,CAACkM,EAAE,CAAC;QAChC,CAAC,MAAM;UACL,IAAIrF,IAAI,CAAC7G,KAAK,CAACmP,eAAe,CAACC,QAAQ,CAAC,kBAAkB,CAAC,EAAE;YAC3D1H,IAAI,CAACC,gBAAgB,GAAG,MAAM;UAChC;QACF;QACA,IACEd,IAAI,CAAC7G,KAAK,CAACmP,eAAe,CAACC,QAAQ,CAAC,kBAAkB,CAAC,IACvDvI,IAAI,CAAC7G,KAAK,CAACmP,eAAe,CAACC,QAAQ,CAAC,cAAc,CAAC,EACnD;UACA3C,QAAQ,CAACzM,KAAK,GAAG,KAAK;QACxB;QACA,IAAI6G,IAAI,CAAC7G,KAAK,CAACmP,eAAe,CAACC,QAAQ,CAAC,OAAO,CAAC,EAAE;UAChD1H,IAAI,CAACC,gBAAgB,GAAG,cAAc;UACtC8E,QAAQ,CAACzM,KAAK,GAAG,KAAK;UACtB0M,QAAQ,CAAC1M,KAAK,GAAG,KAAK;UACtBsP,gBAAgB,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC,MAAM;UACL,IAAIzI,IAAI,CAAC7G,KAAK,CAACmP,eAAe,CAACC,QAAQ,CAAC,kBAAkB,CAAC,EAAE;YAC3DE,gBAAgB,CAAC;cAAEC,YAAY,EAAE;YAAE,CAAC,CAAC;UACvC,CAAC,MAAM;YACLD,gBAAgB,CAAC,CAAC,CAAC,CAAC;UACtB;QACF;QACAE,gBAAgB,CAAC,CAAC;MACpB;IACF,CAAC,CAAC;IACFhJ,aAAa,CAAC,YAAM;MAClB,IAAI4G,KAAK,EAAE;QACTqC,YAAY,CAACrC,KAAK,CAAC;QACnBA,KAAK,GAAG,IAAI;MACd;MACArG,YAAY,CAACsG,cAAc,CAAC;QAAEC,UAAU,EAAE;MAAY,CAAC,CAAC;MACxDvG,YAAY,CAACsG,cAAc,CAAC;QAC1BO,YAAY,EAAE;UACZC,YAAY,EAAE,KAAK;UACnBC,UAAU,EAAE,EAAE;UACdC,YAAY,EAAE,CAAC;QACjB;MACF,CAAC,CAAC;MACF;MACA;IACF,CAAC,CAAC;IACFtH,eAAe,CAAC,YAAM;MACpB,IAAI2G,KAAK,EAAE;QACTqC,YAAY,CAACrC,KAAK,CAAC;QACnBA,KAAK,GAAG,IAAI;MACd;MACArG,YAAY,CAACsG,cAAc,CAAC;QAAEC,UAAU,EAAE;MAAY,CAAC,CAAC;MACxDvG,YAAY,CAACsG,cAAc,CAAC;QAC1BO,YAAY,EAAE;UACZC,YAAY,EAAE,KAAK;UACnBC,UAAU,EAAE,EAAE;UACdC,YAAY,EAAE,CAAC;QACjB;MACF,CAAC,CAAC;MACF;MACA;IACF,CAAC,CAAC;IAEF,IAAMiB,qBAAqB;MAAA,IAAAU,KAAA,GAAA3J,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAiL,QAAA;QAAA,IAAAC,GAAA,EAAAC,IAAA;QAAA,OAAAvQ,mBAAA,GAAAuB,IAAA,UAAAiP,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAA5K,IAAA,GAAA4K,QAAA,CAAAvM,IAAA;YAAA;cAAAuM,QAAA,CAAAvM,IAAA;cAAA,OACV4C,GAAG,CAAC4I,qBAAqB,CAAC;gBAAEP,KAAK,EAAE;kBAAEuB,OAAO,EAAE;gBAAE;cAAE,CAAC,CAAC;YAAA;cAAhEJ,GAAG,GAAAG,QAAA,CAAA9M,IAAA;cACH4M,IAAI,GAAKD,GAAG,CAAZC,IAAI;cACVhI,cAAc,CAAC7H,KAAK,GAAG6P,IAAI;cAC3BI,oBAAoB,CAAC,CAAC;YAAA;YAAA;cAAA,OAAAF,QAAA,CAAAzK,IAAA;UAAA;QAAA,GAAAqK,OAAA;MAAA,CACvB;MAAA,gBALKX,qBAAqBA,CAAA;QAAA,OAAAU,KAAA,CAAAzJ,KAAA,OAAAD,SAAA;MAAA;IAAA,GAK1B;IAED,IAAMiK,oBAAoB,GAAG,SAAvBA,oBAAoBA,CAAA,EAAS;MACjC,IAAIvI,IAAI,CAACG,cAAc,EAAE;QACvB,KAAK,IAAIqI,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGrI,cAAc,CAAC7H,KAAK,CAACqE,MAAM,EAAE6L,KAAK,EAAE,EAAE;UAChE,IAAMC,IAAI,GAAGtI,cAAc,CAAC7H,KAAK,CAACkQ,KAAK,CAAC;UACxC,IAAIC,IAAI,CAACjE,EAAE,KAAKxE,IAAI,CAACG,cAAc,EAAE;YACnC,IAAI,CAACsI,IAAI,CAACC,QAAQ,CAACC,GAAG,CAAC,UAACrO,CAAC;cAAA,OAAKA,CAAC,CAACkK,EAAE;YAAA,EAAC,CAACkD,QAAQ,CAAC1H,IAAI,CAACI,gBAAgB,CAAC,EAAE;cACnEJ,IAAI,CAACI,gBAAgB,GAAG,EAAE;YAC5B;YACAA,gBAAgB,CAAC9H,KAAK,GAAGmQ,IAAI,CAACC,QAAQ;UACxC;QACF;MACF,CAAC,MAAM;QACL1I,IAAI,CAACI,gBAAgB,GAAG,EAAE;QAC1BA,gBAAgB,CAAC9H,KAAK,GAAG,EAAE;MAC7B;IACF,CAAC;IAED,IAAMsQ,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAIC,MAAM,EAAK;MACnC,IAAI,CAAC7I,IAAI,CAACa,OAAO,EAAE,OAAOvB,SAAS,CAAC;QAAE7F,IAAI,EAAE,SAAS;QAAEqI,OAAO,EAAE;MAAkB,CAAC,CAAC;MACpFkE,cAAc,CAACY,OAAO,CAAC,oBAAoB,EAAE5G,IAAI,CAACE,KAAK,CAAC;MACxD8F,cAAc,CAACY,OAAO,CAAC,sBAAsB,EAAE5G,IAAI,CAACa,OAAO,CAAC;MAC5DyE,MAAM,CAAChN,KAAK,GAAGuQ,MAAM;MACrBxD,IAAI,CAAC/M,KAAK,GAAG,IAAI;IACnB,CAAC;IACD,IAAMwQ,wBAAwB,GAAG,SAA3BA,wBAAwBA,CAAIrP,IAAI,EAAEsP,KAAK,EAAK;MAChD,IAAIA,KAAK,IAAI,CAAC,EAAE;QACd,IAAItP,IAAI,EAAE;UACRuP,UAAU,CAAC,CAAC,CAAC;QACf,CAAC,MAAM;UACL1J,SAAS,CAAC;YAAE7F,IAAI,EAAE,SAAS;YAAEqI,OAAO,EAAE;UAAsB,CAAC,CAAC;QAChE;MACF;MACAuD,IAAI,CAAC/M,KAAK,GAAG,KAAK;IACpB,CAAC;IACD,IAAM2Q,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAA,EAAS;MAC9BxD,UAAU,CAACnN,KAAK,GAAG;QAAE4Q,QAAQ,EAAElJ,IAAI,CAACK,aAAa;QAAEQ,OAAO,EAAEb,IAAI,CAACa;MAAQ,CAAC;IAC5E,CAAC;IACD,IAAMsI,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAIC,UAAU,EAAEC,eAAe,EAAK;MACxD9D,QAAQ,CAACjN,KAAK,GAAG8Q,UAAU;MAC3B5D,aAAa,CAAClN,KAAK,GAAG+Q,eAAe;IACvC,CAAC;IACD,IAAMC,UAAU,GAAG,SAAbA,UAAUA,CAAIb,IAAI,EAAK;MAC3B,IAAI,CAACzI,IAAI,CAACY,SAAS,CAAC8G,QAAQ,CAACe,IAAI,CAACjE,EAAE,CAAC,EAAE;QACrCxE,IAAI,CAACY,SAAS,MAAA2I,MAAA,CAAAC,kBAAA,CAAOxJ,IAAI,CAACY,SAAS,IAAE6H,IAAI,CAACjE,EAAE,EAAC;MAC/C;IACF,CAAC;IACD,IAAM0C,gBAAgB;MAAA,IAAAuC,KAAA,GAAApL,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAA0M,SAAA;QAAA,IAAAC,qBAAA,EAAAxB,IAAA;QAAA,OAAAvQ,mBAAA,GAAAuB,IAAA,UAAAyQ,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAApM,IAAA,GAAAoM,SAAA,CAAA/N,IAAA;YAAA;cAAA+N,SAAA,CAAA/N,IAAA;cAAA,OACA4C,GAAG,CAACwI,gBAAgB,CAAC;gBAAE4C,KAAK,EAAE,CAAC,oBAAoB,EAAE,sBAAsB,EAAE,mBAAmB,EAAE,yBAAyB;cAAE,CAAC,CAAC;YAAA;cAAAH,qBAAA,GAAAE,SAAA,CAAAtO,IAAA;cAA9I4M,IAAI,GAAAwB,qBAAA,CAAJxB,IAAI;cACZ,IAAIA,IAAI,CAAC1E,kBAAkB,EAAE;gBAC3BA,kBAAkB,CAACnL,KAAK,GAAGyR,MAAM,CAAC5B,IAAI,CAAC1E,kBAAkB,CAAC;cAC5D;cACA,IAAI0E,IAAI,CAACzE,oBAAoB,EAAE;gBAC7BA,oBAAoB,CAACpL,KAAK,GAAGyR,MAAM,CAAC5B,IAAI,CAACzE,oBAAoB,CAAC;cAChE;cACA,IAAIyE,IAAI,CAACvE,uBAAuB,EAAE;gBAChCA,uBAAuB,CAACtL,KAAK,GAAGyR,MAAM,CAAC5B,IAAI,CAACvE,uBAAuB,CAAC;cACtE;cACA,IAAIuE,IAAI,CAACxE,iBAAiB,EAAE;gBAC1BA,iBAAiB,CAACrL,KAAK,GAAGyR,MAAM,CAAC5B,IAAI,CAACxE,iBAAiB,CAAC;cAC1D;YAAC;YAAA;cAAA,OAAAkG,SAAA,CAAAjM,IAAA;UAAA;QAAA,GAAA8L,QAAA;MAAA,CACF;MAAA,gBAdKxC,gBAAgBA,CAAA;QAAA,OAAAuC,KAAA,CAAAlL,KAAA,OAAAD,SAAA;MAAA;IAAA,GAcrB;IACD;IACA,IAAM6I,eAAe;MAAA,IAAA6C,KAAA,GAAA3L,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAiN,SAAA;QAAA,IAAAC,qBAAA,EAAA/B,IAAA;QAAA,OAAAvQ,mBAAA,GAAAuB,IAAA,UAAAgR,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA3M,IAAA,GAAA2M,SAAA,CAAAtO,IAAA;YAAA;cAAAsO,SAAA,CAAAtO,IAAA;cAAA,OACC4C,GAAG,CAACyI,eAAe,CAAC;gBAAEkD,YAAY,EAAE;cAAe,CAAC,CAAC;YAAA;cAAAH,qBAAA,GAAAE,SAAA,CAAA7O,IAAA;cAApE4M,IAAI,GAAA+B,qBAAA,CAAJ/B,IAAI;cACZtE,UAAU,CAACvL,KAAK,GAAG6P,IAAI,CAAC3D,EAAE;YAAA;YAAA;cAAA,OAAA4F,SAAA,CAAAxM,IAAA;UAAA;QAAA,GAAAqM,QAAA;MAAA,CAC3B;MAAA,gBAHK9C,eAAeA,CAAA;QAAA,OAAA6C,KAAA,CAAAzL,KAAA,OAAAD,SAAA;MAAA;IAAA,GAGpB;IACD,IAAM8I,cAAc;MAAA,IAAAkD,KAAA,GAAAjM,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAuN,SAAA;QAAA,IAAAC,qBAAA,EAAArC,IAAA;QAAA,OAAAvQ,mBAAA,GAAAuB,IAAA,UAAAsR,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAjN,IAAA,GAAAiN,SAAA,CAAA5O,IAAA;YAAA;cAAA4O,SAAA,CAAA5O,IAAA;cAAA,OACE4C,GAAG,CAAC0I,cAAc,CAAC;gBACxCuD,SAAS,EAAE,CACT,mBAAmB,EACnB,qBAAqB,EACrB,sBAAsB,EACtB,sBAAsB,EACtB,kBAAkB,EAClB,sBAAsB;cAE1B,CAAC,CAAC;YAAA;cAAAH,qBAAA,GAAAE,SAAA,CAAAnP,IAAA;cATM4M,IAAI,GAAAqC,qBAAA,CAAJrC,IAAI;cAUZrH,eAAe,CAACxI,KAAK,GAAG6P,IAAI,CAACyC,iBAAiB;cAC9C7J,iBAAiB,CAACzI,KAAK,GAAG6P,IAAI,CAAC0C,mBAAmB;cAClD5J,iBAAiB,CAAC3I,KAAK,GAAG6P,IAAI,CAAC2C,oBAAoB;cACnD5J,iBAAiB,CAAC5I,KAAK,GAAG6P,IAAI,CAAC4C,oBAAoB;cACnD/J,aAAa,CAAC1I,KAAK,GAAG6P,IAAI,CAAC6C,gBAAgB;cAC3C7J,iBAAiB,CAAC7I,KAAK,GAAG6P,IAAI,CAAC8C,oBAAoB;YAAA;YAAA;cAAA,OAAAP,SAAA,CAAA9M,IAAA;UAAA;QAAA,GAAA2M,QAAA;MAAA,CACpD;MAAA,gBAjBKnD,cAAcA,CAAA;QAAA,OAAAkD,KAAA,CAAA/L,KAAA,OAAAD,SAAA;MAAA;IAAA,GAiBnB;IACD,IAAM+I,kBAAkB;MAAA,IAAA6D,KAAA,GAAA7M,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAmO,SAAA;QAAA,IAAAC,sBAAA,EAAAjD,IAAA;QAAA,OAAAvQ,mBAAA,GAAAuB,IAAA,UAAAkS,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA7N,IAAA,GAAA6N,SAAA,CAAAxP,IAAA;YAAA;cAAAwP,SAAA,CAAAxP,IAAA;cAAA,OACF4C,GAAG,CAAC2I,kBAAkB,CAAC;gBAC5CsD,SAAS,EAAE,CACT,mBAAmB,EACnB,qBAAqB,EACrB,sBAAsB,EACtB,sBAAsB,EACtB,kBAAkB,EAClB,sBAAsB;cAE1B,CAAC,CAAC;YAAA;cAAAS,sBAAA,GAAAE,SAAA,CAAA/P,IAAA;cATM4M,IAAI,GAAAiD,sBAAA,CAAJjD,IAAI;cAUZlE,mBAAmB,CAAC3L,KAAK,GAAG6P,IAAI,CAACyC,iBAAiB;cAClD1G,qBAAqB,CAAC5L,KAAK,GAAG6P,IAAI,CAAC0C,mBAAmB;cACtD1G,qBAAqB,CAAC7L,KAAK,GAAG6P,IAAI,CAAC2C,oBAAoB;cACvD1G,qBAAqB,CAAC9L,KAAK,GAAG6P,IAAI,CAAC4C,oBAAoB;cACvD1G,iBAAiB,CAAC/L,KAAK,GAAG6P,IAAI,CAAC6C,gBAAgB;cAC/C1G,qBAAqB,CAAChM,KAAK,GAAG6P,IAAI,CAAC8C,oBAAoB;YAAA;YAAA;cAAA,OAAAK,SAAA,CAAA1N,IAAA;UAAA;QAAA,GAAAuN,QAAA;MAAA,CACxD;MAAA,gBAjBK9D,kBAAkBA,CAAA;QAAA,OAAA6D,KAAA,CAAA3M,KAAA,OAAAD,SAAA;MAAA;IAAA,GAiBvB;IACD,IAAM2I,gBAAgB;MAAA,IAAAsE,KAAA,GAAAlN,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAwO,SAAA;QAAA,IAAAC,qBAAA,EAAAtD,IAAA;QAAA,OAAAvQ,mBAAA,GAAAuB,IAAA,UAAAuS,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAlO,IAAA,GAAAkO,SAAA,CAAA7P,IAAA;YAAA;cAAA6P,SAAA,CAAA7P,IAAA;cAAA,OACA4C,GAAG,CAACuI,gBAAgB,CAAC;gBAAE2E,QAAQ,EAAEjM,KAAK,CAACoH,KAAK,CAACC;cAAW,CAAC,CAAC;YAAA;cAAAyE,qBAAA,GAAAE,SAAA,CAAApQ,IAAA;cAAzE4M,IAAI,GAAAsD,qBAAA,CAAJtD,IAAI;cACZnI,IAAI,CAACE,KAAK,GAAGiI,IAAI,CAACjI,KAAK;cACvBF,IAAI,CAACa,OAAO,GAAGsH,IAAI,CAACtH,OAAO;YAAA;YAAA;cAAA,OAAA8K,SAAA,CAAA/N,IAAA;UAAA;QAAA,GAAA4N,QAAA;MAAA,CAC5B;MAAA,gBAJKvE,gBAAgBA,CAAA;QAAA,OAAAsE,KAAA,CAAAhN,KAAA,OAAAD,SAAA;MAAA;IAAA,GAIrB;IACD,IAAMuN,MAAM,GAAGjN,GAAG,CAAC,KAAK,CAAC;IACzB,IAAMkN,MAAM,GAAGlN,GAAG,CAAC,CAAC,CAAC,CAAC;IACtB,IAAM4I,cAAc;MAAA,IAAAuE,KAAA,GAAA1N,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAgP,SAAA;QAAA,IAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,mBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,eAAA,EAAAC,gBAAA,EAAAxE,GAAA,EAAAC,IAAA;QAAA,OAAAvQ,mBAAA,GAAAuB,IAAA,UAAAwT,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAnP,IAAA,GAAAmP,SAAA,CAAA9Q,IAAA;YAAA;cAAA8Q,SAAA,CAAAnP,IAAA;cAAAmP,SAAA,CAAA9Q,IAAA;cAAA,OAED4C,GAAG,CAAC8I,cAAc,CAAC;gBACnCoE,QAAQ,EAAEjM,KAAK,CAACoH,KAAK,CAACvC,EAAE,IAAI7E,KAAK,CAACoH,KAAK,CAACQ,MAAM;gBAC9CsF,cAAc,EAAEzH,SAAS,CAAC9M,KAAK,KAAK,QAAQ,GAAG,CAAC,GAAG;cACrD,CAAC,CAAC;YAAA;cAHI4P,GAAG,GAAA0E,SAAA,CAAArR,IAAA;cAIH4M,IAAI,GAAKD,GAAG,CAAZC,IAAI;cACVhD,UAAU,CAAC7M,KAAK,GAAG,IAAI;cACvBwT,MAAM,CAACxT,KAAK,GAAG6P,IAAI,CAAC2D,MAAM;cAC1BD,MAAM,CAACvT,KAAK,GAAGqH,KAAK,CAACoH,KAAK,CAACtN,IAAI,IAAI,QAAQ,IAAI0O,IAAI,CAAC2D,MAAM,CAACD,MAAM,IAAI,CAAC,IAAI1D,IAAI,CAAC2D,MAAM,CAACgB,UAAU,IAAI3N,IAAI,CAAC7G,KAAK,CAACkM,EAAE;cACjHxE,IAAI,CAACC,gBAAgB,GAAGkI,IAAI,CAAClI,gBAAgB;cAC7C,IAAID,IAAI,CAACC,gBAAgB,KAAK,cAAc,EAAE;gBAC5CiF,OAAO,CAAC5M,KAAK,GAAG,CAAC,aAAa,CAAC;gBAC/B0H,IAAI,CAACK,aAAa,GAAG8H,IAAI,CAAC9H,aAAa;gBACvC,IAAI8H,IAAI,CAAC9H,aAAa,EAAE;kBACtBsH,eAAe,CAACQ,IAAI,CAAC9H,aAAa,CAAC;gBACrC;cACF;cACA,IAAIL,IAAI,CAACC,gBAAgB,KAAK,MAAM,EAAE;gBACpCgF,UAAU,CAAC3M,KAAK,GAAG,IAAI;gBACvB0H,IAAI,CAACU,YAAY,GAAGyH,IAAI,CAACzH,YAAY;gBACrCsD,cAAc,CAAC1L,KAAK,GAAG6P,IAAI,CAACzH,YAAY,GAAG,CAAC;kBAAE8D,EAAE,EAAE2D,IAAI,CAACzH,YAAY;kBAAE3D,IAAI,EAAEoL,IAAI,CAAC4E;gBAAe,CAAC,CAAC,GAAG,EAAE;cACxG;cACAjF,gBAAgB,CAAC,CAAC;cAClB9H,IAAI,CAACE,KAAK,GAAGiI,IAAI,CAACjI,KAAK;cACvBF,IAAI,CAACG,cAAc,GAAGgI,IAAI,CAAC9G,UAAU;cACrCrB,IAAI,CAACI,gBAAgB,GAAG+H,IAAI,CAAC7G,YAAY;cACzCiH,oBAAoB,CAAC,CAAC;cACtBvI,IAAI,CAACa,OAAO,GAAGsH,IAAI,CAACtH,OAAO;cAC3B,KAAAoL,qBAAA,GAAI9D,IAAI,CAAC6E,qBAAqB,cAAAf,qBAAA,eAA1BA,qBAAA,CAA4BtP,MAAM,EAAE;gBACtCiI,uBAAuB,CAACtM,KAAK,GAAG6P,IAAI,CAAC6E,qBAAqB,CAACrE,GAAG,CAAC,UAAArO,CAAC;kBAAA,OAAK;oBAAEkK,EAAE,EAAElK,CAAC,CAACkK,EAAE;oBAAEK,UAAU,EAAEvK,CAAC,CAACuG,OAAO;oBAAEiE,WAAW,EAAExK,CAAC,CAAC2S;kBAAU,CAAC;gBAAA,CAAC,CAAC;cACtI;cACAjN,IAAI,CAACqB,UAAU,GAAG8G,IAAI,CAAC9G,UAAU;cACjCrB,IAAI,CAACsB,YAAY,GAAG6G,IAAI,CAAC7G,YAAY;cACrCtB,IAAI,CAACuB,KAAK,GAAG4G,IAAI,CAAC5G,KAAK;cACvBvB,IAAI,CAACwB,MAAM,GAAG2G,IAAI,CAAC3G,MAAM;cACzBxB,IAAI,CAACyB,IAAI,GAAG0G,IAAI,CAAC1G,IAAI;cACrBzB,IAAI,CAAC0B,MAAM,GAAGyG,IAAI,CAACzG,MAAM;cACzB1B,IAAI,CAAC2B,IAAI,GAAGwG,IAAI,CAACxG,IAAI;cACrBsH,iBAAiB,CAAC,CAAC;cACnBjJ,IAAI,CAACW,cAAc,GAAGwH,IAAI,CAACxH,cAAc;cACzCuM,UAAU,CAAC,CAAC;cACZlN,IAAI,CAACc,eAAe,IAAAoL,qBAAA,GAAG/D,IAAI,CAACrH,eAAe,cAAAoL,qBAAA,uBAApBA,qBAAA,CAAsB5T,KAAK;cAClD0H,IAAI,CAACe,iBAAiB,IAAAoL,qBAAA,GAAGhE,IAAI,CAACpH,iBAAiB,cAAAoL,qBAAA,uBAAtBA,qBAAA,CAAwB7T,KAAK;cACtD0H,IAAI,CAACgB,aAAa,IAAAoL,mBAAA,GAAGjE,IAAI,CAACnH,aAAa,cAAAoL,mBAAA,uBAAlBA,mBAAA,CAAoB9T,KAAK;cAC9C0H,IAAI,CAACiB,iBAAiB,IAAAoL,qBAAA,GAAGlE,IAAI,CAAClH,iBAAiB,cAAAoL,qBAAA,uBAAtBA,qBAAA,CAAwB/T,KAAK;cACtD0H,IAAI,CAACkB,iBAAiB,IAAAoL,qBAAA,GAAGnE,IAAI,CAACjH,iBAAiB,cAAAoL,qBAAA,uBAAtBA,qBAAA,CAAwBhU,KAAK;cACtD0H,IAAI,CAACmB,iBAAiB,IAAAoL,qBAAA,GAAGpE,IAAI,CAAChH,iBAAiB,cAAAoL,qBAAA,uBAAtBA,qBAAA,CAAwBjU,KAAK;cACtDyL,QAAQ,CAACzL,KAAK,GAAG6P,IAAI,CAACgF,WAAW,IAAI,EAAE;cACvCnN,IAAI,CAACoB,mBAAmB,GAAG,EAAAoL,qBAAA,GAAArE,IAAI,CAAC/G,mBAAmB,cAAAoL,qBAAA,uBAAxBA,qBAAA,CAA0B7D,GAAG,CAAC,UAACrO,CAAC;gBAAA,OAAKA,CAAC,CAAC8S,QAAQ;cAAA,EAAC,KAAI,EAAE;cACjFpN,IAAI,CAACY,SAAS,GAAG,EAAA6L,eAAA,GAAAtE,IAAI,CAACvH,SAAS,cAAA6L,eAAA,uBAAdA,eAAA,CAAgB9D,GAAG,CAAC,UAACrO,CAAC;gBAAA,OAAKA,CAAC,CAAC+S,MAAM;cAAA,EAAC,KAAI,EAAE;cAC3D,KAAAX,gBAAA,GAAIvE,IAAI,CAACmF,UAAU,cAAAZ,gBAAA,eAAfA,gBAAA,CAAiB/P,MAAM,EAAE;gBAC3B4H,iBAAiB,CAACjM,KAAK,GAAG6P,IAAI,CAACmF,UAAU,CAAC3E,GAAG,CAAC,UAACrO,CAAC;kBAAA,OAAM;oBACpDkK,EAAE,EAAElK,CAAC,CAACkK,EAAE;oBACRC,WAAW,EAAEnK,CAAC,CAACiT,aAAa;oBAC5B7I,YAAY,EAAEpK,CAAC,CAACkT,eAAe;oBAC/B7I,cAAc,EAAErK,CAAC,CAACmT;kBACpB,CAAC;gBAAA,CAAC,CAAC;cACL;cACAhI,UAAU,CAACnN,KAAK,GAAG;gBAAE4Q,QAAQ,EAAElJ,IAAI,CAACK,aAAa;gBAAEQ,OAAO,EAAEb,IAAI,CAACa;cAAQ,CAAC;cAAA+L,SAAA,CAAA9Q,IAAA;cAAA;YAAA;cAAA8Q,SAAA,CAAAnP,IAAA;cAAAmP,SAAA,CAAAc,EAAA,GAAAd,SAAA;cAE1E,IAAIA,SAAA,CAAAc,EAAA,CAAIC,IAAI,KAAK,GAAG,EAAE;gBACpB,IAAIhO,KAAK,CAACoH,KAAK,CAACvC,EAAE,IAAIY,SAAS,CAAC9M,KAAK,KAAK,QAAQ,EAAE;kBAClD6M,UAAU,CAAC7M,KAAK,GAAG,KAAK;kBACxB+G,YAAY,CAACsG,cAAc,CAAC;oBAC1BiI,cAAc,EAAE;sBAAEC,MAAM,EAAElO,KAAK,CAACoH,KAAK,CAAC+G,UAAU;sBAAEC,OAAO,EAAEpO,KAAK,CAACoH,KAAK,CAACiH;oBAAQ;kBACjF,CAAC,CAAC;gBACJ;cACF;YAAC;YAAA;cAAA,OAAApB,SAAA,CAAAhP,IAAA;UAAA;QAAA,GAAAoO,QAAA;MAAA,CAEJ;MAAA,gBAtEKxE,cAAcA,CAAA;QAAA,OAAAuE,KAAA,CAAAxN,KAAA,OAAAD,SAAA;MAAA;IAAA,GAsEnB;IAED,IAAMwJ,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAA,EAAS;MAC7B,IAAI9H,IAAI,CAACC,gBAAgB,KAAK,cAAc,EAAE;QAC5C2B,KAAK,CAACvB,aAAa,GAAG,CAAC;UAAEwB,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,QAAQ;UAAEC,OAAO,EAAE,CAAC,MAAM,EAAE,QAAQ;QAAE,CAAC,CAAC;QAC1FH,KAAK,CAAClB,YAAY,GAAG,CAAC;UAAEmB,QAAQ,EAAE,KAAK;UAAEC,OAAO,EAAE,WAAW;UAAEC,OAAO,EAAE,CAAC,MAAM,EAAE,QAAQ;QAAE,CAAC,CAAC;MAC/F,CAAC,MAAM,IAAI/B,IAAI,CAACC,gBAAgB,KAAK,MAAM,EAAE;QAC3C2B,KAAK,CAACvB,aAAa,GAAG,CAAC;UAAEwB,QAAQ,EAAE,KAAK;UAAEC,OAAO,EAAE,QAAQ;UAAEC,OAAO,EAAE,CAAC,MAAM,EAAE,QAAQ;QAAE,CAAC,CAAC;QAC3FH,KAAK,CAAClB,YAAY,GAAG,CAAC;UAAEmB,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,WAAW;UAAEC,OAAO,EAAE,CAAC,MAAM,EAAE,QAAQ;QAAE,CAAC,CAAC;MAC9F;IACF,CAAC;IACD,IAAMmL,UAAU,GAAG,SAAbA,UAAUA,CAAA,EAAS;MACvB,IAAIlN,IAAI,CAACW,cAAc,EAAE;QACvBiB,KAAK,CAAChB,SAAS,GAAG,CAAC;UAAEnH,IAAI,EAAE,OAAO;UAAEoI,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,UAAU;UAAEC,OAAO,EAAE,CAAC,MAAM,EAAE,QAAQ;QAAE,CAAC,CAAC;MACzG,CAAC,MAAM;QACLH,KAAK,CAAChB,SAAS,GAAG,CAAC;UAAEnH,IAAI,EAAE,OAAO;UAAEoI,QAAQ,EAAE,KAAK;UAAEC,OAAO,EAAE,UAAU;UAAEC,OAAO,EAAE,CAAC,MAAM,EAAE,QAAQ;QAAE,CAAC,CAAC;MAC1G;IACF,CAAC;IACD,IAAMkM,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAIC,KAAK,EAAK;MACpCpK,YAAY,CAACxL,KAAK,GAAG4V,KAAK;IAC5B,CAAC;IAED,IAAMC,YAAY,GAAG,SAAfA,YAAYA,CAAIhG,IAAI,EAAK;MAC7B,IAAIA,IAAI,EAAE;QACRR,eAAe,CAACQ,IAAI,CAAC3D,EAAE,CAAC;QACxBxE,IAAI,CAACY,SAAS,GAAGZ,IAAI,CAACY,SAAS,CAACwN,MAAM,CAAC,UAAC9T,CAAC;UAAA,OAAKA,CAAC,KAAK6N,IAAI,CAAC3D,EAAE;QAAA,EAAC;MAC9D,CAAC,MAAM;QACLxE,IAAI,CAACM,UAAU,GAAG,EAAE;QACpBN,IAAI,CAACO,UAAU,GAAG,EAAE;QACpBP,IAAI,CAACQ,MAAM,GAAG,EAAE;QAChBR,IAAI,CAACS,WAAW,GAAG,EAAE;MACvB;MACAgF,UAAU,CAACnN,KAAK,GAAG;QAAE4Q,QAAQ,EAAElJ,IAAI,CAACK,aAAa;QAAEQ,OAAO,EAAEb,IAAI,CAACa;MAAQ,CAAC;IAC5E,CAAC;IACD,IAAM8G,eAAe;MAAA,IAAA0G,KAAA,GAAAhQ,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAsR,SAAOjB,MAAM;QAAA,IAAAkB,gBAAA;QAAA,IAAAC,qBAAA,EAAArG,IAAA;QAAA,OAAAvQ,mBAAA,GAAAuB,IAAA,UAAAsV,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAjR,IAAA,GAAAiR,SAAA,CAAA5S,IAAA;YAAA;cAAA4S,SAAA,CAAA5S,IAAA;cAAA,OACZ4C,GAAG,CAACiJ,eAAe,CAAC;gBAAEiE,QAAQ,EAAEyB;cAAO,CAAC,CAAC;YAAA;cAAAmB,qBAAA,GAAAE,SAAA,CAAAnT,IAAA;cAAxD4M,IAAI,GAAAqG,qBAAA,CAAJrG,IAAI;cACZnI,IAAI,CAACM,UAAU,GAAG6H,IAAI,CAACwG,eAAe;cACtC3O,IAAI,CAACO,UAAU,IAAAgO,gBAAA,GAAGpG,IAAI,CAAC5H,UAAU,cAAAgO,gBAAA,uBAAfA,gBAAA,CAAiBK,KAAK;cACxC5O,IAAI,CAACQ,MAAM,GAAG2H,IAAI,CAAC3H,MAAM;cACzBR,IAAI,CAACS,WAAW,GAAG0H,IAAI,CAAC1H,WAAW;YAAA;YAAA;cAAA,OAAAiO,SAAA,CAAA9Q,IAAA;UAAA;QAAA,GAAA0Q,QAAA;MAAA,CACpC;MAAA,gBANK3G,eAAeA,CAAAkH,EAAA;QAAA,OAAAR,KAAA,CAAA9P,KAAA,OAAAD,SAAA;MAAA;IAAA,GAMpB;IACD,IAAMsJ,gBAAgB;MAAA,IAAAkH,MAAA,GAAAzQ,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAA+R,SAAOC,MAAM;QAAA,IAAAC,qBAAA,EAAA9G,IAAA;QAAA,OAAAvQ,mBAAA,GAAAuB,IAAA,UAAA+V,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA1R,IAAA,GAAA0R,SAAA,CAAArT,IAAA;YAAA;cAAAqT,SAAA,CAAArT,IAAA;cAAA,OACb4C,GAAG,CAACkJ,gBAAgB,CAACoH,MAAM,CAAC;YAAA;cAAAC,qBAAA,GAAAE,SAAA,CAAA5T,IAAA;cAA3C4M,IAAI,GAAA8G,qBAAA,CAAJ9G,IAAI;cACZ,IAAIA,IAAI,CAACxL,MAAM,EAAE;gBACf,IAAIwC,IAAI,CAAC7G,KAAK,CAACmP,eAAe,CAACC,QAAQ,CAAC,kBAAkB,CAAC,EAAE;kBAC3DzC,UAAU,CAAC3M,KAAK,GAAG,IAAI;kBACvB0H,IAAI,CAACU,YAAY,GAAGyH,IAAI,CAAC,CAAC,CAAC,CAAC3D,EAAE;gBAChC;cACF;cACAR,cAAc,CAAC1L,KAAK,GAAG6P,IAAI;YAAA;YAAA;cAAA,OAAAgH,SAAA,CAAAvR,IAAA;UAAA;QAAA,GAAAmR,QAAA;MAAA,CAC5B;MAAA,gBATKnH,gBAAgBA,CAAAwH,GAAA;QAAA,OAAAN,MAAA,CAAAvQ,KAAA,OAAAD,SAAA;MAAA;IAAA,GASrB;IACD,IAAM+Q,UAAU,GAAG,SAAbA,UAAUA,CAAIC,IAAI,EAAK;MAC3BvL,QAAQ,CAACzL,KAAK,GAAGgX,IAAI;IACvB,CAAC;IACD,IAAMC,sBAAsB,GAAG,SAAzBA,sBAAsBA,CAAA,EAAS;MACnC3K,uBAAuB,CAACtM,KAAK,CAACgE,IAAI,CAAC;QAAEkI,EAAE,EAAExC,IAAI,CAAC,CAAC;QAAE6C,UAAU,EAAE,EAAE;QAAEC,WAAW,EAAE;MAAG,CAAC,CAAC;IACrF,CAAC;IACD,IAAM0K,sBAAsB,GAAG,SAAzBA,sBAAsBA,CAAIhL,EAAE,EAAK;MACrCI,uBAAuB,CAACtM,KAAK,GAAGsM,uBAAuB,CAACtM,KAAK,CAAC8V,MAAM,CAAC,UAAA9T,CAAC;QAAA,OAAIA,CAAC,CAACkK,EAAE,KAAKA,EAAE;MAAA,EAAC;IACxF,CAAC;IACD,IAAMiL,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAA,EAAS;MAC7BlL,iBAAiB,CAACjM,KAAK,CAACgE,IAAI,CAAC;QAAEkI,EAAE,EAAExC,IAAI,CAAC,CAAC;QAAEyC,WAAW,EAAE,EAAE;QAAEC,YAAY,EAAE,EAAE;QAAEC,cAAc,EAAE;MAAG,CAAC,CAAC;IACrG,CAAC;IACD,IAAM+K,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAIlL,EAAE,EAAK;MAC/BD,iBAAiB,CAACjM,KAAK,GAAGiM,iBAAiB,CAACjM,KAAK,CAAC8V,MAAM,CAAC,UAAC9T,CAAC;QAAA,OAAKA,CAAC,CAACkK,EAAE,KAAKA,EAAE;MAAA,EAAC;IAC9E,CAAC;IACD,IAAMmL,UAAU;MAAA,IAAAC,MAAA,GAAAvR,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAA6S,UAAOC,MAAM,EAAErW,IAAI,EAAEsW,EAAE,EAAEC,KAAK;QAAA,IAAAC,kBAAA;QAAA,OAAArY,mBAAA,GAAAuB,IAAA,UAAA+W,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAA1S,IAAA,GAAA0S,UAAA,CAAArU,IAAA;YAAA;cAAA,IAC1CgU,MAAM;gBAAAK,UAAA,CAAArU,IAAA;gBAAA;cAAA;cAAA,OAAAqU,UAAA,CAAAzU,MAAA;YAAA;cAAA,MACPoI,YAAY,CAACxL,KAAK,GAAGoL,oBAAoB,CAACpL,KAAK,IAAImB,IAAI,IAAI,GAAG;gBAAA0W,UAAA,CAAArU,IAAA;gBAAA;cAAA;cAChEwD,SAAS,CAAC;gBAAE7F,IAAI,EAAE,SAAS;gBAAEqI,OAAO,EAAE,eAAe4B,oBAAoB,CAACpL,KAAK;cAAW,CAAC,CAAC;cAAA,OAAA6X,UAAA,CAAAzU,MAAA;YAAA;cAAA,MAG1FoI,YAAY,CAACxL,KAAK,GAAGsL,uBAAuB,CAACtL,KAAK,IAAIqH,KAAK,CAACoH,KAAK,CAACqJ,KAAK,IAAI,GAAG,IAAI3W,IAAI,IAAI,GAAG;gBAAA0W,UAAA,CAAArU,IAAA;gBAAA;cAAA;cAC/FwD,SAAS,CAAC;gBAAE7F,IAAI,EAAE,SAAS;gBAAEqI,OAAO,EAAE,cAAc8B,uBAAuB,CAACtL,KAAK;cAAW,CAAC,CAAC;cAAA,OAAA6X,UAAA,CAAAzU,MAAA;YAAA;cAG1FuU,kBAAkB,GAAGrL,uBAAuB,CAACtM,KAAK,CAAC+X,IAAI,CAC3D,UAAC/V,CAAC;gBAAA,OAAKA,CAAC,CAACuK,UAAU,KAAK,IAAI,IAAIvK,CAAC,CAACuK,UAAU,KAAKyL,SAAS,IAAIhW,CAAC,CAACuK,UAAU,CAAC0L,IAAI,CAAC,CAAC,KAAK,EAAE;cAAA,CAC1F,CAAC;cAAA,IACIN,kBAAkB;gBAAAE,UAAA,CAAArU,IAAA;gBAAA;cAAA;cACrBwD,SAAS,CAAC;gBAAE7F,IAAI,EAAE,SAAS;gBAAEqI,OAAO,EAAE;cAAqB,CAAC,CAAC;cAAC,OAAAqO,UAAA,CAAAzU,MAAA;YAAA;cAAAyU,UAAA,CAAArU,IAAA;cAAA,OAI1DgU,MAAM,CAACU,QAAQ,CAAC,UAACC,KAAK,EAAEC,MAAM,EAAK;gBACvC,IAAID,KAAK,EAAE;kBACT;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACAzH,UAAU,CAACvP,IAAI,EAAEsW,EAAE,EAAEC,KAAK,CAAC;gBAC7B,CAAC,MAAM;kBAAE1Q,SAAS,CAAC;oBAAE7F,IAAI,EAAE,SAAS;oBAAEqI,OAAO,EAAE;kBAAiB,CAAC,CAAC;gBAAC;cACrE,CAAC,CAAC;YAAA;YAAA;cAAA,OAAAqO,UAAA,CAAAvS,IAAA;UAAA;QAAA,GAAAiS,SAAA;MAAA,CACH;MAAA,gBAhCKF,UAAUA,CAAAgB,GAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA;QAAA,OAAAlB,MAAA,CAAArR,KAAA,OAAAD,SAAA;MAAA;IAAA,GAgCf;IACD,IAAMyS,YAAY,GAAG,SAAfA,YAAYA,CAAIhB,EAAE,EAAEC,KAAK,EAAK;MAClCL,UAAU,CAAC5P,OAAO,CAACzH,KAAK,EAAE,CAAC,EAAEyX,EAAE,EAAEC,KAAK,CAAC;IACzC,CAAC;IACD,IAAMhH,UAAU;MAAA,IAAAgI,MAAA,GAAA3S,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAiU,UAAOxX,IAAI,EAAEsW,EAAE,EAAEmB,KAAK;QAAA,IAAAC,OAAA,EAAAC,qBAAA,EAAAzD,IAAA;QAAA,OAAA/V,mBAAA,GAAAuB,IAAA,UAAAkY,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAA7T,IAAA,GAAA6T,UAAA,CAAAxV,IAAA;YAAA;cACnCqV,OAAO,GAAG;gBACZ3M,EAAE,EAAE7E,KAAK,CAACoH,KAAK,CAACvC,EAAE;gBAClBnD,UAAU,EAAE6P,KAAK,GAAGA,KAAK,CAAC/Q,cAAc,GAAG,EAAE;gBAAE;gBAC/CmB,YAAY,EAAE4P,KAAK,GAAGA,KAAK,CAAC9Q,gBAAgB,GAAG,EAAE;gBAAE;gBACnDH,gBAAgB,EAAED,IAAI,CAACC,gBAAgB;gBACvCC,KAAK,EAAEF,IAAI,CAACE,KAAK;gBAAE;gBACnBG,aAAa,EAAEL,IAAI,CAACC,gBAAgB,KAAK,cAAc,GAAGD,IAAI,CAACK,aAAa,GAAG,IAAI;gBACnFK,YAAY,EAAEV,IAAI,CAACC,gBAAgB,KAAK,MAAM,GAAGD,IAAI,CAACU,YAAY,GAAG,IAAI;gBACzEG,OAAO,EAAEb,IAAI,CAACa,OAAO;gBACrBF,cAAc,EAAEX,IAAI,CAACW,cAAc;gBACnCG,eAAe,EAAEd,IAAI,CAACc,eAAe;gBACrCC,iBAAiB,EAAEf,IAAI,CAACe,iBAAiB;gBACzCC,aAAa,EAAEhB,IAAI,CAACgB,aAAa;gBACjCC,iBAAiB,EAAEjB,IAAI,CAACiB,iBAAiB;gBACzCC,iBAAiB,EAAElB,IAAI,CAACkB,iBAAiB;gBACzCC,iBAAiB,EAAEnB,IAAI,CAACmB,iBAAiB;gBACzCoQ,aAAa,EAAExN,QAAQ,CAACzL,KAAK,CAACqQ,GAAG,CAAC,UAAArO,CAAC;kBAAA,OAAIA,CAAC,CAACkK,EAAE;gBAAA;cAC7C,CAAC;cACD,IAAI7E,KAAK,CAACoH,KAAK,CAACyK,MAAM,IAAI,GAAG,EAAE;gBAC7BL,OAAO,CAAC5P,KAAK,GAAG2P,KAAK,CAACO,aAAa,IAAI,EAAE;gBACzCN,OAAO,CAAC3P,MAAM,GAAG0P,KAAK,CAACQ,YAAY,IAAI,EAAE;gBACzCP,OAAO,CAAC1P,IAAI,GAAGyP,KAAK,CAACS,YAAY,IAAI,EAAE,EAAC;gBACxCR,OAAO,CAACzP,MAAM,GAAGwP,KAAK,CAACU,kBAAkB,CAACjJ,GAAG,CAAC,UAAArO,CAAC;kBAAA,OAAIA,CAAC;gBAAA,EAAC,CAACuX,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,EAAC;gBACtEV,OAAO,CAACxP,IAAI,GAAGuP,KAAK,CAACY,eAAe,CAACnJ,GAAG,CAAC,UAAArO,CAAC;kBAAA,OAAIA,CAAC;gBAAA,EAAC,CAACuX,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,EAAC;cACnE;cAACP,UAAA,CAAA7T,IAAA;cAAA6T,UAAA,CAAAxV,IAAA;cAAA,OAEwB4C,GAAG,CAACsK,UAAU,CAACrJ,KAAK,CAACoH,KAAK,CAACvC,EAAE,GAAG,gBAAgB,GAAG,eAAe,EAAE;gBACzFxE,IAAI,EAAEmR,OAAO;gBACbY,WAAW,EAAEtY,IAAI;gBACjBmH,SAAS,EAAEZ,IAAI,CAACW,cAAc,GAAGX,IAAI,CAACY,SAAS,GAAG,EAAE;gBACpDQ,mBAAmB,EAAEpB,IAAI,CAACoB,mBAAmB;gBAC7CkM,UAAU,EAAE/I,iBAAiB,CAACjM,KAAK,CAAC8V,MAAM,CAAC,UAAA9T,CAAC;kBAAA,OAAIA,CAAC,CAACmK,WAAW,CAACxC,OAAO,CAAC,gBAAgB,EAAE,EAAE,CAAC,IAAI3H,CAAC,CAACoK,YAAY,CAACzC,OAAO,CAAC,gBAAgB,EAAE,EAAE,CAAC,IAAI3H,CAAC,CAACqK,cAAc,CAAC1C,OAAO,CAAC,gBAAgB,EAAE,EAAE,CAAC;gBAAA,EAAC,CAAC0G,GAAG,CAAC,UAAArO,CAAC;kBAAA,OAAK;oBAAEiT,aAAa,EAAEjT,CAAC,CAACmK,WAAW;oBAAE+I,eAAe,EAAElT,CAAC,CAACoK,YAAY;oBAAE+I,gBAAgB,EAAEnT,CAAC,CAACqK;kBAAe,CAAC;gBAAA,CAAC,CAAC;gBAChTqN,mBAAmB,EAAEpN,uBAAuB,CAACtM,KAAK,CAACqQ,GAAG,CAAC,UAAArO,CAAC;kBAAA,OAAK;oBAAEuG,OAAO,EAAEvG,CAAC,CAACuK,UAAU;oBAAEoI,SAAS,EAAE3S,CAAC,CAACwK;kBAAY,CAAC;gBAAA,CAAC;cACnH,CAAC,CAAC;YAAA;cAAAsM,qBAAA,GAAAE,UAAA,CAAA/V,IAAA;cAPMoS,IAAI,GAAAyD,qBAAA,CAAJzD,IAAI;cAAA,MAQRA,IAAI,KAAK,GAAG;gBAAA2D,UAAA,CAAAxV,IAAA;gBAAA;cAAA;cAAA,KACV6D,KAAK,CAACoH,KAAK,CAACC,UAAU;gBAAAsK,UAAA,CAAAxV,IAAA;gBAAA;cAAA;cACxBmW,eAAe,CAAC,CAAC;cAAAX,UAAA,CAAAxV,IAAA;cAAA;YAAA;cAAA,KAEbiU,EAAE;gBAAAuB,UAAA,CAAAxV,IAAA;gBAAA;cAAA;cAAA,OAAAwV,UAAA,CAAA5V,MAAA,WACGqU,EAAE,CAAC,CAAC;YAAA;cAEbzQ,SAAS,CAAC;gBAAE7F,IAAI,EAAE,SAAS;gBAAEqI,OAAO,EAAEnC,KAAK,CAACoH,KAAK,CAACvC,EAAE,GAAG,MAAM,GAAG;cAAO,CAAC,CAAC;cACzE,IAAI7E,KAAK,CAACoH,KAAK,CAACvC,EAAE,IAAI7E,KAAK,CAACoH,KAAK,CAACQ,MAAM,IAAI5H,KAAK,CAACoH,KAAK,CAACC,UAAU,EAAE;gBAClE3H,YAAY,CAACsG,cAAc,CAAC;kBAC1BiI,cAAc,EAAE;oBAAEC,MAAM,EAAElO,KAAK,CAACoH,KAAK,CAAC+G,UAAU;oBAAEC,OAAO,EAAEpO,KAAK,CAACoH,KAAK,CAACiH;kBAAQ;gBACjF,CAAC,CAAC;cACJ,CAAC,MAAM;gBACL3O,YAAY,CAACsG,cAAc,CAAC;kBAC1BuM,SAAS,EAAE;oBAAEnV,IAAI,EAAE,MAAM;oBAAEoV,IAAI,EAAE;kBAAyB;gBAC5D,CAAC,CAAC;gBACFvS,KAAK,CAACwS,MAAM,CAAC,iBAAiB,EAAE,eAAe,CAAC;gBAChDtL,UAAU,CAAC,YAAM;kBACflH,KAAK,CAACwS,MAAM,CAAC,iBAAiB,EAAE,EAAE,CAAC;gBACrC,CAAC,EAAE,GAAG,CAAC;cACT;YAAC;cAAAd,UAAA,CAAAxV,IAAA;cAAA;YAAA;cAAAwV,UAAA,CAAA7T,IAAA;cAAA6T,UAAA,CAAA5D,EAAA,GAAA4D,UAAA;cAILzR,OAAO,CAACvH,KAAK,GAAG,KAAK;YAAA;YAAA;cAAA,OAAAgZ,UAAA,CAAA1T,IAAA;UAAA;QAAA,GAAAqT,SAAA;MAAA,CAExB;MAAA,gBA7DKjI,UAAUA,CAAAqJ,GAAA,EAAAC,GAAA,EAAAC,GAAA;QAAA,OAAAvB,MAAA,CAAAzS,KAAA,OAAAD,SAAA;MAAA;IAAA,GA6Df;IACD,IAAM2T,eAAe;MAAA,IAAAO,MAAA,GAAAnU,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAyV,UAAA;QAAA,IAAAC,sBAAA,EAAA/E,IAAA;QAAA,OAAA/V,mBAAA,GAAAuB,IAAA,UAAAwZ,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAAnV,IAAA,GAAAmV,UAAA,CAAA9W,IAAA;YAAA;cAAA8W,UAAA,CAAA9W,IAAA;cAAA,OACC4C,GAAG,CAACuT,eAAe,CAAC;gBAAErG,QAAQ,EAAEjM,KAAK,CAACoH,KAAK,CAACC;cAAW,CAAC,CAAC;YAAA;cAAA0L,sBAAA,GAAAE,UAAA,CAAArX,IAAA;cAAxEoS,IAAI,GAAA+E,sBAAA,CAAJ/E,IAAI;cACZ,IAAIA,IAAI,KAAK,GAAG,EAAE;gBAChBrO,SAAS,CAAC;kBAAE7F,IAAI,EAAE,SAAS;kBAAEqI,OAAO,EAAE;gBAAO,CAAC,CAAC;gBAC/CzC,YAAY,CAACsG,cAAc,CAAC;kBAAEiI,cAAc,EAAE;oBAAEC,MAAM,EAAElO,KAAK,CAACoH,KAAK,CAAC+G,UAAU;oBAAEC,OAAO,EAAEpO,KAAK,CAACoH,KAAK,CAACiH;kBAAQ;gBAAE,CAAC,CAAC;cACnH;YAAC;YAAA;cAAA,OAAA4E,UAAA,CAAAhV,IAAA;UAAA;QAAA,GAAA6U,SAAA;MAAA,CACF;MAAA,gBANKR,eAAeA,CAAA;QAAA,OAAAO,MAAA,CAAAjU,KAAA,OAAAD,SAAA;MAAA;IAAA,GAMpB;IACD,IAAMuU,SAAS,GAAG,SAAZA,SAASA,CAAA,EAAS;MACtB,IAAIlT,KAAK,CAACoH,KAAK,CAACvC,EAAE,IAAI7E,KAAK,CAACoH,KAAK,CAACQ,MAAM,IAAI5H,KAAK,CAACoH,KAAK,CAACC,UAAU,EAAE;QAClE3H,YAAY,CAACsG,cAAc,CAAC;UAAEiI,cAAc,EAAE;YAAEC,MAAM,EAAElO,KAAK,CAACoH,KAAK,CAAC+G,UAAU;YAAEC,OAAO,EAAEpO,KAAK,CAACoH,KAAK,CAACiH;UAAQ;QAAE,CAAC,CAAC;MACnH,CAAC,MAAM;QACLpO,KAAK,CAACwS,MAAM,CAAC,iBAAiB,EAAE,eAAe,CAAC;QAChDtL,UAAU,CAAC,YAAM;UACflH,KAAK,CAACwS,MAAM,CAAC,iBAAiB,EAAE,EAAE,CAAC;QACrC,CAAC,EAAE,GAAG,CAAC;MACT;IACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}