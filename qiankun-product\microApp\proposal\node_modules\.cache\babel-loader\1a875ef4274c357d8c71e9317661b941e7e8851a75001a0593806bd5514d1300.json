{"ast": null, "code": "function _regeneratorRuntime() { \"use strict\"; /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */ _regeneratorRuntime = function _regeneratorRuntime() { return e; }; var t, e = {}, r = Object.prototype, n = r.hasOwnProperty, o = Object.defineProperty || function (t, e, r) { t[e] = r.value; }, i = \"function\" == typeof Symbol ? Symbol : {}, a = i.iterator || \"@@iterator\", c = i.asyncIterator || \"@@asyncIterator\", u = i.toStringTag || \"@@toStringTag\"; function define(t, e, r) { return Object.defineProperty(t, e, { value: r, enumerable: !0, configurable: !0, writable: !0 }), t[e]; } try { define({}, \"\"); } catch (t) { define = function define(t, e, r) { return t[e] = r; }; } function wrap(t, e, r, n) { var i = e && e.prototype instanceof Generator ? e : Generator, a = Object.create(i.prototype), c = new Context(n || []); return o(a, \"_invoke\", { value: makeInvokeMethod(t, r, c) }), a; } function tryCatch(t, e, r) { try { return { type: \"normal\", arg: t.call(e, r) }; } catch (t) { return { type: \"throw\", arg: t }; } } e.wrap = wrap; var h = \"suspendedStart\", l = \"suspendedYield\", f = \"executing\", s = \"completed\", y = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} var p = {}; define(p, a, function () { return this; }); var d = Object.getPrototypeOf, v = d && d(d(values([]))); v && v !== r && n.call(v, a) && (p = v); var g = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(p); function defineIteratorMethods(t) { [\"next\", \"throw\", \"return\"].forEach(function (e) { define(t, e, function (t) { return this._invoke(e, t); }); }); } function AsyncIterator(t, e) { function invoke(r, o, i, a) { var c = tryCatch(t[r], t, o); if (\"throw\" !== c.type) { var u = c.arg, h = u.value; return h && \"object\" == typeof h && n.call(h, \"__await\") ? e.resolve(h.__await).then(function (t) { invoke(\"next\", t, i, a); }, function (t) { invoke(\"throw\", t, i, a); }) : e.resolve(h).then(function (t) { u.value = t, i(u); }, function (t) { return invoke(\"throw\", t, i, a); }); } a(c.arg); } var r; o(this, \"_invoke\", { value: function value(t, n) { function callInvokeWithMethodAndArg() { return new e(function (e, r) { invoke(t, n, e, r); }); } return r = r ? r.then(callInvokeWithMethodAndArg, callInvokeWithMethodAndArg) : callInvokeWithMethodAndArg(); } }); } function makeInvokeMethod(e, r, n) { var o = h; return function (i, a) { if (o === f) throw Error(\"Generator is already running\"); if (o === s) { if (\"throw\" === i) throw a; return { value: t, done: !0 }; } for (n.method = i, n.arg = a;;) { var c = n.delegate; if (c) { var u = maybeInvokeDelegate(c, n); if (u) { if (u === y) continue; return u; } } if (\"next\" === n.method) n.sent = n._sent = n.arg;else if (\"throw\" === n.method) { if (o === h) throw o = s, n.arg; n.dispatchException(n.arg); } else \"return\" === n.method && n.abrupt(\"return\", n.arg); o = f; var p = tryCatch(e, r, n); if (\"normal\" === p.type) { if (o = n.done ? s : l, p.arg === y) continue; return { value: p.arg, done: n.done }; } \"throw\" === p.type && (o = s, n.method = \"throw\", n.arg = p.arg); } }; } function maybeInvokeDelegate(e, r) { var n = r.method, o = e.iterator[n]; if (o === t) return r.delegate = null, \"throw\" === n && e.iterator.return && (r.method = \"return\", r.arg = t, maybeInvokeDelegate(e, r), \"throw\" === r.method) || \"return\" !== n && (r.method = \"throw\", r.arg = new TypeError(\"The iterator does not provide a '\" + n + \"' method\")), y; var i = tryCatch(o, e.iterator, r.arg); if (\"throw\" === i.type) return r.method = \"throw\", r.arg = i.arg, r.delegate = null, y; var a = i.arg; return a ? a.done ? (r[e.resultName] = a.value, r.next = e.nextLoc, \"return\" !== r.method && (r.method = \"next\", r.arg = t), r.delegate = null, y) : a : (r.method = \"throw\", r.arg = new TypeError(\"iterator result is not an object\"), r.delegate = null, y); } function pushTryEntry(t) { var e = { tryLoc: t[0] }; 1 in t && (e.catchLoc = t[1]), 2 in t && (e.finallyLoc = t[2], e.afterLoc = t[3]), this.tryEntries.push(e); } function resetTryEntry(t) { var e = t.completion || {}; e.type = \"normal\", delete e.arg, t.completion = e; } function Context(t) { this.tryEntries = [{ tryLoc: \"root\" }], t.forEach(pushTryEntry, this), this.reset(!0); } function values(e) { if (e || \"\" === e) { var r = e[a]; if (r) return r.call(e); if (\"function\" == typeof e.next) return e; if (!isNaN(e.length)) { var o = -1, i = function next() { for (; ++o < e.length;) if (n.call(e, o)) return next.value = e[o], next.done = !1, next; return next.value = t, next.done = !0, next; }; return i.next = i; } } throw new TypeError(typeof e + \" is not iterable\"); } return GeneratorFunction.prototype = GeneratorFunctionPrototype, o(g, \"constructor\", { value: GeneratorFunctionPrototype, configurable: !0 }), o(GeneratorFunctionPrototype, \"constructor\", { value: GeneratorFunction, configurable: !0 }), GeneratorFunction.displayName = define(GeneratorFunctionPrototype, u, \"GeneratorFunction\"), e.isGeneratorFunction = function (t) { var e = \"function\" == typeof t && t.constructor; return !!e && (e === GeneratorFunction || \"GeneratorFunction\" === (e.displayName || e.name)); }, e.mark = function (t) { return Object.setPrototypeOf ? Object.setPrototypeOf(t, GeneratorFunctionPrototype) : (t.__proto__ = GeneratorFunctionPrototype, define(t, u, \"GeneratorFunction\")), t.prototype = Object.create(g), t; }, e.awrap = function (t) { return { __await: t }; }, defineIteratorMethods(AsyncIterator.prototype), define(AsyncIterator.prototype, c, function () { return this; }), e.AsyncIterator = AsyncIterator, e.async = function (t, r, n, o, i) { void 0 === i && (i = Promise); var a = new AsyncIterator(wrap(t, r, n, o), i); return e.isGeneratorFunction(r) ? a : a.next().then(function (t) { return t.done ? t.value : a.next(); }); }, defineIteratorMethods(g), define(g, u, \"Generator\"), define(g, a, function () { return this; }), define(g, \"toString\", function () { return \"[object Generator]\"; }), e.keys = function (t) { var e = Object(t), r = []; for (var n in e) r.push(n); return r.reverse(), function next() { for (; r.length;) { var t = r.pop(); if (t in e) return next.value = t, next.done = !1, next; } return next.done = !0, next; }; }, e.values = values, Context.prototype = { constructor: Context, reset: function reset(e) { if (this.prev = 0, this.next = 0, this.sent = this._sent = t, this.done = !1, this.delegate = null, this.method = \"next\", this.arg = t, this.tryEntries.forEach(resetTryEntry), !e) for (var r in this) \"t\" === r.charAt(0) && n.call(this, r) && !isNaN(+r.slice(1)) && (this[r] = t); }, stop: function stop() { this.done = !0; var t = this.tryEntries[0].completion; if (\"throw\" === t.type) throw t.arg; return this.rval; }, dispatchException: function dispatchException(e) { if (this.done) throw e; var r = this; function handle(n, o) { return a.type = \"throw\", a.arg = e, r.next = n, o && (r.method = \"next\", r.arg = t), !!o; } for (var o = this.tryEntries.length - 1; o >= 0; --o) { var i = this.tryEntries[o], a = i.completion; if (\"root\" === i.tryLoc) return handle(\"end\"); if (i.tryLoc <= this.prev) { var c = n.call(i, \"catchLoc\"), u = n.call(i, \"finallyLoc\"); if (c && u) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } else if (c) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); } else { if (!u) throw Error(\"try statement without catch or finally\"); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } } } }, abrupt: function abrupt(t, e) { for (var r = this.tryEntries.length - 1; r >= 0; --r) { var o = this.tryEntries[r]; if (o.tryLoc <= this.prev && n.call(o, \"finallyLoc\") && this.prev < o.finallyLoc) { var i = o; break; } } i && (\"break\" === t || \"continue\" === t) && i.tryLoc <= e && e <= i.finallyLoc && (i = null); var a = i ? i.completion : {}; return a.type = t, a.arg = e, i ? (this.method = \"next\", this.next = i.finallyLoc, y) : this.complete(a); }, complete: function complete(t, e) { if (\"throw\" === t.type) throw t.arg; return \"break\" === t.type || \"continue\" === t.type ? this.next = t.arg : \"return\" === t.type ? (this.rval = this.arg = t.arg, this.method = \"return\", this.next = \"end\") : \"normal\" === t.type && e && (this.next = e), y; }, finish: function finish(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.finallyLoc === t) return this.complete(r.completion, r.afterLoc), resetTryEntry(r), y; } }, catch: function _catch(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.tryLoc === t) { var n = r.completion; if (\"throw\" === n.type) { var o = n.arg; resetTryEntry(r); } return o; } } throw Error(\"illegal catch attempt\"); }, delegateYield: function delegateYield(e, r, n) { return this.delegate = { iterator: values(e), resultName: r, nextLoc: n }, \"next\" === this.method && (this.arg = t), y; } }, e; }\nfunction asyncGeneratorStep(n, t, e, r, o, a, c) { try { var i = n[a](c), u = i.value; } catch (n) { return void e(n); } i.done ? t(u) : Promise.resolve(u).then(r, o); }\nfunction _asyncToGenerator(n) { return function () { var t = this, e = arguments; return new Promise(function (r, o) { var a = n.apply(t, e); function _next(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"next\", n); } function _throw(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"throw\", n); } _next(void 0); }); }; }\nfunction _toConsumableArray(r) { return _arrayWithoutHoles(r) || _iterableToArray(r) || _unsupportedIterableToArray(r) || _nonIterableSpread(); }\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(r, a) { if (r) { if (\"string\" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }\nfunction _iterableToArray(r) { if (\"undefined\" != typeof Symbol && null != r[Symbol.iterator] || null != r[\"@@iterator\"]) return Array.from(r); }\nfunction _arrayWithoutHoles(r) { if (Array.isArray(r)) return _arrayLikeToArray(r); }\nfunction _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }\nimport api from '@/api';\nimport { reactive, ref, onActivated, onDeactivated, onBeforeUnmount, watch } from 'vue';\nimport { whetherUseIntelligentize } from 'common/js/system_var.js';\nimport { qiankunMicro } from 'common/config/MicroGlobal';\nimport { ElMessage } from 'element-plus';\nimport { format } from 'common/js/time.js';\nimport SuggestRecommendType from '@/components/SuggestRecommendType/SuggestRecommendType.vue';\nimport SuggestRecommendUnit from '@/components/SuggestRecommendUnit/SuggestRecommendUnit.vue';\nimport ReviewSimilarityQuery from './ReviewSimilarityQuery';\nvar __default__ = {\n  name: 'SuggestReviewDetail'\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  props: {\n    id: {\n      type: String,\n      default: ''\n    },\n    name: {\n      type: String,\n      default: '提案审查'\n    },\n    content: {\n      type: String,\n      default: ''\n    },\n    SuggestBigType: {\n      type: String,\n      default: ''\n    },\n    SuggestSmallType: {\n      type: String,\n      default: ''\n    },\n    isLock: {\n      type: Boolean,\n      default: false\n    },\n    hopeHandleOfficeIds: {\n      type: Array,\n      default: function _default() {\n        return [];\n      }\n    },\n    lockVo: {\n      type: Object,\n      default: function _default() {\n        return {};\n      }\n    },\n    queryType: {\n      type: String,\n      default: ''\n    },\n    signId: {\n      type: String,\n      default: ''\n    },\n    bigThemeId: {\n      type: String,\n      default: ''\n    },\n    smallThemeId: {\n      type: String,\n      default: ''\n    },\n    jordan: {\n      type: String,\n      default: ''\n    },\n    james: {\n      type: String,\n      default: ''\n    },\n    kobe: {\n      type: String,\n      default: ''\n    },\n    duncan: {\n      type: String,\n      default: ''\n    },\n    wade: {\n      type: String,\n      default: ''\n    }\n  },\n  emits: ['callback'],\n  setup(__props, _ref) {\n    var __expose = _ref.expose,\n      __emit = _ref.emit;\n    __expose();\n    var props = __props;\n    var emit = __emit;\n    var isReviewResult = ref('');\n    var formRef = ref();\n    var form = reactive({\n      reviewResult: '',\n      // 审查结果\n      rejectReason: '',\n      SuggestBigType: '',\n      // 提案大类\n      SuggestSmallType: '',\n      // 提案小类\n      reviewOpinion: '',\n      // 审查意见\n      transactType: '',\n      // 请选择办理方式\n      mainHandleOfficeId: [],\n      handleOfficeIds: []\n    });\n    var rules = reactive({\n      reviewResult: [{\n        required: true,\n        message: '请选择审查结果',\n        trigger: ['blur', 'change']\n      }],\n      rejectReason: [{\n        required: false,\n        message: '请选择不予接收理由',\n        trigger: ['blur', 'change']\n      }],\n      SuggestBigType: [{\n        required: true,\n        message: '请选择提案大类',\n        trigger: ['blur', 'change']\n      }],\n      mainHandleOfficeId: [{\n        type: 'array',\n        required: false,\n        message: '请选择主办单位',\n        trigger: ['blur', 'change']\n      }],\n      handleOfficeIds: [{\n        type: 'array',\n        required: false,\n        message: '请选择分办单位',\n        trigger: ['blur', 'change']\n      }]\n    });\n    var unitParams = ref({});\n    var reviewResult = ref([]);\n    var SuggestBigType = ref([]);\n    var SuggestSmallType = ref([]);\n    var suggestionRejectReason = ref([]);\n    var rejectName = ref('');\n    var elTypeShow = ref(false);\n    var visibleTypeShow = ref(false);\n    var elUnitShow = ref(false);\n    var visibleUnitShow = ref(false);\n    var suggestId = ref('');\n    // const elAiChatClass = AiChatClass()\n    onActivated(function () {\n      form.SuggestBigType = props.bigThemeId;\n      form.SuggestSmallType = props.smallThemeId;\n      form.reviewOpinion = props.james || '';\n      form.reviewResult = props.jordan || '';\n      if (form.reviewResult == 'prepareSubmitHandle') {\n        isReviewResult.value = 'success';\n      }\n      form.transactType = props.kobe || '';\n      form.mainHandleOfficeId = [props.duncan] || '';\n      console.log('props.wade==>', props.wade);\n      setTimeout(function () {\n        form.handleOfficeIds = [props.wade] || '';\n      }, 2000);\n      qiankunMicro.setGlobalState({\n        AiChatCode: 'ai-assistant-chat'\n      });\n      qiankunMicro.setGlobalState({\n        AiChatWindow: true\n      });\n      qiankunMicro.setGlobalState({\n        AiChatParams: {\n          businessId: props.id\n        }\n      });\n      dictionaryData();\n      suggestionThemeSelect();\n      if (props.id) {\n        suggestionNextNodes();\n        suggestId.value = props.id;\n      }\n      window.addEventListener('beforeunload', function (e) {\n        suggestionUnlock();\n      });\n      if (props.signId == '1') {\n        delete rules.reviewResult;\n        delete rules.rejectReason;\n      }\n    });\n    var typeCallback = function typeCallback(isElIsShow, isVisibleIsShow) {\n      if (props.signId != '1') {\n        elTypeShow.value = isElIsShow;\n        visibleTypeShow.value = isVisibleIsShow;\n        return;\n      }\n    };\n    var typeSelect = function typeSelect(item, id) {\n      if (id) {\n        form.SuggestBigType = id;\n        SuggestBigTypeChange();\n        form.SuggestSmallType = item._id;\n      } else {\n        form.SuggestBigType = item._id;\n        SuggestBigTypeChange();\n      }\n    };\n    var unitCallback = function unitCallback(isElIsShow, isVisibleIsShow) {\n      elUnitShow.value = isElIsShow;\n      visibleUnitShow.value = isVisibleIsShow;\n    };\n    var unitSelect = function unitSelect(item) {\n      if (form.transactType === 'main_assist') {\n        if (!form.mainHandleOfficeId.length) {\n          if (!form.handleOfficeIds.includes(item.id)) {\n            form.mainHandleOfficeId = [item.id];\n            ElMessage({\n              type: 'success',\n              message: `已为您将【${item.name}】添加到主办单位`\n            });\n          }\n        } else {\n          // if (!form.mainHandleOfficeId.includes(item.id) && !form.handleOfficeIds.includes(item.id)) {\n          //   form.handleOfficeIds = [...form.handleOfficeIds, item.id]\n          //   ElMessage({ type: 'success', message: `当前已选择主办单位，已为您将【${item.name}】添加到协办单位` })\n          // }\n        }\n      } else if (form.transactType === 'publish') {\n        if (!form.handleOfficeIds.includes(item.id)) {\n          form.handleOfficeIds = [].concat(_toConsumableArray(form.handleOfficeIds), [item.id]);\n          ElMessage({\n            type: 'success',\n            message: `已为您将${item.name}添加到分办单位`\n          });\n        }\n      }\n    };\n    var suggestionUnlock = /*#__PURE__*/function () {\n      var _ref2 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee() {\n        var res, data;\n        return _regeneratorRuntime().wrap(function _callee$(_context) {\n          while (1) switch (_context.prev = _context.next) {\n            case 0:\n              if (!props.isLock) {\n                _context.next = 2;\n                break;\n              }\n              return _context.abrupt(\"return\");\n            case 2:\n              _context.next = 4;\n              return api.suggestionUnlock({\n                ids: [suggestId.value]\n              });\n            case 4:\n              res = _context.sent;\n              data = res.data;\n              console.log(data);\n            case 7:\n            case \"end\":\n              return _context.stop();\n          }\n        }, _callee);\n      }));\n      return function suggestionUnlock() {\n        return _ref2.apply(this, arguments);\n      };\n    }();\n    var suggestionNextNodes = /*#__PURE__*/function () {\n      var _ref3 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee2() {\n        var res, data, index, item;\n        return _regeneratorRuntime().wrap(function _callee2$(_context2) {\n          while (1) switch (_context2.prev = _context2.next) {\n            case 0:\n              _context2.next = 2;\n              return api.suggestionNextNodes({\n                suggestionId: props.id\n              });\n            case 2:\n              res = _context2.sent;\n              data = res.data;\n              if (props.signId == '2') {\n                reviewResult.value = [{\n                  \"nodeId\": \"prepareSubmitHandle\",\n                  \"nodeName\": \"立案\",\n                  \"formType\": \"success\"\n                }, {\n                  \"nodeId\": \"exchangeLetter\",\n                  \"nodeName\": \"转来信\",\n                  \"formType\": \"other\"\n                }, {\n                  \"nodeId\": \"exchangeSocial\",\n                  \"nodeName\": \"转社情民意\",\n                  \"formType\": \"other\"\n                }, {\n                  \"nodeId\": \"rejectReceive\",\n                  \"nodeName\": \"不予立案\",\n                  \"formType\": \"noAccept\"\n                }, {\n                  \"nodeId\": \"cancelSuggestion\",\n                  \"nodeName\": \"撤案\",\n                  \"formType\": \"other\"\n                }, {\n                  \"nodeId\": \"returnSubmit\",\n                  \"nodeName\": \"退回\",\n                  \"formType\": \"other\"\n                }];\n              } else {\n                reviewResult.value = data;\n              }\n              for (index = 0; index < data.length; index++) {\n                item = data[index];\n                if (item.formType === 'noAccept') {\n                  rejectName.value = item.nodeName;\n                }\n              }\n            case 6:\n            case \"end\":\n              return _context2.stop();\n          }\n        }, _callee2);\n      }));\n      return function suggestionNextNodes() {\n        return _ref3.apply(this, arguments);\n      };\n    }();\n    var reviewResultChange = function reviewResultChange() {\n      isReviewResult.value = '';\n      rules.rejectReason = [{\n        required: false,\n        message: `请选择${rejectName.value}理由`,\n        trigger: ['blur', 'change']\n      }];\n      for (var index = 0; index < reviewResult.value.length; index++) {\n        var item = reviewResult.value[index];\n        if (item.nodeId === form.reviewResult) {\n          isReviewResult.value = item.formType;\n          if (item.formType === 'noAccept') {\n            rules.rejectReason = [{\n              required: true,\n              message: `请选择${rejectName.value}理由`,\n              trigger: ['blur', 'change']\n            }];\n          }\n        }\n      }\n    };\n    var dictionaryData = /*#__PURE__*/function () {\n      var _ref4 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee3() {\n        var res, data;\n        return _regeneratorRuntime().wrap(function _callee3$(_context3) {\n          while (1) switch (_context3.prev = _context3.next) {\n            case 0:\n              _context3.next = 2;\n              return api.dictionaryData({\n                dictCodes: ['suggestion_reject_reason']\n              });\n            case 2:\n              res = _context3.sent;\n              data = res.data;\n              suggestionRejectReason.value = data.suggestion_reject_reason;\n            case 5:\n            case \"end\":\n              return _context3.stop();\n          }\n        }, _callee3);\n      }));\n      return function dictionaryData() {\n        return _ref4.apply(this, arguments);\n      };\n    }();\n    var suggestionThemeSelect = /*#__PURE__*/function () {\n      var _ref5 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee4() {\n        var res, data;\n        return _regeneratorRuntime().wrap(function _callee4$(_context4) {\n          while (1) switch (_context4.prev = _context4.next) {\n            case 0:\n              _context4.next = 2;\n              return api.suggestionThemeSelect({\n                query: {\n                  isUsing: 1\n                }\n              });\n            case 2:\n              res = _context4.sent;\n              data = res.data;\n              SuggestBigType.value = data;\n              SuggestBigTypeChange();\n            case 6:\n            case \"end\":\n              return _context4.stop();\n          }\n        }, _callee4);\n      }));\n      return function suggestionThemeSelect() {\n        return _ref5.apply(this, arguments);\n      };\n    }();\n    var SuggestBigTypeChange = function SuggestBigTypeChange() {\n      if (form.SuggestBigType) {\n        for (var index = 0; index < SuggestBigType.value.length; index++) {\n          var item = SuggestBigType.value[index];\n          if (item.id === form.SuggestBigType) {\n            if (!item.children.map(function (v) {\n              return v.id;\n            }).includes(form.SuggestSmallType)) {\n              form.SuggestSmallType = '';\n            }\n            SuggestSmallType.value = item.children;\n          }\n        }\n      } else {\n        form.SuggestSmallType = '';\n        SuggestSmallType.value = [];\n      }\n    };\n    var transactTypeChange = function transactTypeChange() {\n      if (form.transactType === 'main_assist') {\n        rules.mainHandleOfficeId = [{\n          type: 'array',\n          required: true,\n          message: '请选择主办单位',\n          trigger: ['blur', 'change']\n        }];\n        rules.handleOfficeIds = [{\n          type: 'array',\n          required: false,\n          message: '请选择分办单位',\n          trigger: ['blur', 'change']\n        }];\n      } else if (form.transactType === 'publish') {\n        rules.mainHandleOfficeId = [{\n          type: 'array',\n          required: false,\n          message: '请选择主办单位',\n          trigger: ['blur', 'change']\n        }];\n        rules.handleOfficeIds = [{\n          type: 'array',\n          required: true,\n          message: '请选择分办单位',\n          trigger: ['blur', 'change']\n        }];\n      } else {\n        rules.mainHandleOfficeId = [{\n          type: 'array',\n          required: false,\n          message: '请选择主办单位',\n          trigger: ['blur', 'change']\n        }];\n        rules.handleOfficeIds = [{\n          type: 'array',\n          required: false,\n          message: '请选择分办单位',\n          trigger: ['blur', 'change']\n        }];\n      }\n    };\n    var submitForm = /*#__PURE__*/function () {\n      var _ref6 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee5(formEl) {\n        return _regeneratorRuntime().wrap(function _callee5$(_context5) {\n          while (1) switch (_context5.prev = _context5.next) {\n            case 0:\n              if (formEl) {\n                _context5.next = 2;\n                break;\n              }\n              return _context5.abrupt(\"return\");\n            case 2:\n              _context5.next = 4;\n              return formEl.validate(function (valid, fields) {\n                if (valid) {\n                  emit('editCallback', suggestionComplete, form);\n                } else {\n                  ElMessage({\n                    type: 'warning',\n                    message: '请根据提示信息完善字段内容！'\n                  });\n                }\n              });\n            case 4:\n            case \"end\":\n              return _context5.stop();\n          }\n        }, _callee5);\n      }));\n      return function submitForm(_x) {\n        return _ref6.apply(this, arguments);\n      };\n    }();\n    var suggestionComplete = /*#__PURE__*/function () {\n      var _ref7 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee6() {\n        var _yield$api$suggestion, code;\n        return _regeneratorRuntime().wrap(function _callee6$(_context6) {\n          while (1) switch (_context6.prev = _context6.next) {\n            case 0:\n              _context6.next = 2;\n              return api.suggestionComplete({\n                suggestionId: props.id,\n                nextNodeId: props.signId == '1' ? 'review' : props.signId == '2' ? 'prepareVerify' : form.reviewResult,\n                // 审查结果\n                bigThemeId: form.SuggestBigType,\n                // 提案大类\n                smallThemeId: form.SuggestSmallType,\n                // 提案小类\n                variable: {\n                  handleContent: form.reviewOpinion,\n                  // 审查意见\n                  spareDict: isReviewResult.value === 'noAccept' ? form.rejectReason : ''\n                },\n                handleOfficeType: form.transactType,\n                // 办理方式\n                mainHandleOfficeId: form.mainHandleOfficeId.join(''),\n                // 主办单位\n                handleOfficeIds: form.handleOfficeIds.length > 0 && form.handleOfficeIds[0] ? form.handleOfficeIds : [] // 协办或分办单位\n              });\n            case 2:\n              _yield$api$suggestion = _context6.sent;\n              code = _yield$api$suggestion.code;\n              if (code === 200) {\n                ElMessage({\n                  type: 'success',\n                  message: '审查成功'\n                });\n                emit('callback');\n              }\n            case 5:\n            case \"end\":\n              return _context6.stop();\n          }\n        }, _callee6);\n      }));\n      return function suggestionComplete() {\n        return _ref7.apply(this, arguments);\n      };\n    }();\n    var resetForm = function resetForm() {\n      emit('callback');\n    };\n    onDeactivated(function () {\n      qiankunMicro.setGlobalState({\n        AiChatWindow: false\n      });\n      qiankunMicro.setGlobalState({\n        AiChatCode: 'test_chat'\n      });\n      qiankunMicro.setGlobalState({\n        AiChatParams: {}\n      });\n      qiankunMicro.setGlobalState({\n        AiChatContent: ''\n      });\n      // elAiChatClass.AiChatConfig({ AiChatCode: 'test_chat', AiChatWindow: false })\n      // elAiChatClass.AiChatHistory()\n      suggestionUnlock();\n    });\n    onBeforeUnmount(function () {\n      qiankunMicro.setGlobalState({\n        AiChatWindow: false\n      });\n      qiankunMicro.setGlobalState({\n        AiChatCode: 'test_chat'\n      });\n      qiankunMicro.setGlobalState({\n        AiChatParams: {}\n      });\n      qiankunMicro.setGlobalState({\n        AiChatContent: ''\n      });\n      // elAiChatClass.AiChatConfig({ AiChatCode: 'test_chat', AiChatWindow: false })\n      // elAiChatClass.AiChatHistory()\n      // suggestionUnlock()\n    });\n    watch(function () {\n      return [props.SuggestBigType, props.SuggestSmallType];\n    }, function () {\n      form.SuggestBigType = props.SuggestBigType;\n      form.SuggestSmallType = props.SuggestSmallType;\n      SuggestBigTypeChange();\n    }, {\n      immediate: true\n    });\n    watch(function () {\n      return [props.hopeHandleOfficeIds, props.content];\n    }, function () {\n      // if (props.content) {\n      //   elAiChatClass.AiChatConfig({\n      //     AiChatCode: 'ai-assistant-chat',\n      //     AiChatModuleId: props.id,\n      //     AiChatWindow: true,\n      //     AiChatContent: props.content\n      //   })\n      //   elAiChatClass.AiChatHistory('ai-assistant-chat', props.id)\n      // }\n      qiankunMicro.setGlobalState({\n        AiChatContent: props.content\n      });\n      var selectUnit = (props === null || props === void 0 ? void 0 : props.hopeHandleOfficeIds) || [];\n      unitParams.value = {\n        selectUnit: JSON.stringify(selectUnit),\n        content: props.content\n      };\n    }, {\n      immediate: true\n    });\n    var __returned__ = {\n      props,\n      emit,\n      isReviewResult,\n      formRef,\n      form,\n      rules,\n      unitParams,\n      reviewResult,\n      SuggestBigType,\n      SuggestSmallType,\n      suggestionRejectReason,\n      rejectName,\n      elTypeShow,\n      visibleTypeShow,\n      elUnitShow,\n      visibleUnitShow,\n      suggestId,\n      typeCallback,\n      typeSelect,\n      unitCallback,\n      unitSelect,\n      suggestionUnlock,\n      suggestionNextNodes,\n      reviewResultChange,\n      dictionaryData,\n      suggestionThemeSelect,\n      SuggestBigTypeChange,\n      transactTypeChange,\n      submitForm,\n      suggestionComplete,\n      resetForm,\n      get api() {\n        return api;\n      },\n      reactive,\n      ref,\n      onActivated,\n      onDeactivated,\n      onBeforeUnmount,\n      watch,\n      get whetherUseIntelligentize() {\n        return whetherUseIntelligentize;\n      },\n      get qiankunMicro() {\n        return qiankunMicro;\n      },\n      get ElMessage() {\n        return ElMessage;\n      },\n      get format() {\n        return format;\n      },\n      SuggestRecommendType,\n      SuggestRecommendUnit,\n      get ReviewSimilarityQuery() {\n        return ReviewSimilarityQuery;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["_regeneratorRuntime", "e", "t", "r", "Object", "prototype", "n", "hasOwnProperty", "o", "defineProperty", "value", "i", "Symbol", "a", "iterator", "c", "asyncIterator", "u", "toStringTag", "define", "enumerable", "configurable", "writable", "wrap", "Generator", "create", "Context", "makeInvokeMethod", "tryCatch", "type", "arg", "call", "h", "l", "f", "s", "y", "GeneratorFunction", "GeneratorFunctionPrototype", "p", "d", "getPrototypeOf", "v", "values", "g", "defineIteratorMethods", "for<PERSON>ach", "_invoke", "AsyncIterator", "invoke", "resolve", "__await", "then", "callInvokeWithMethodAndArg", "Error", "done", "method", "delegate", "maybeInvokeDelegate", "sent", "_sent", "dispatchException", "abrupt", "return", "TypeError", "resultName", "next", "nextLoc", "pushTryEntry", "tryLoc", "catchLoc", "finallyLoc", "afterLoc", "tryEntries", "push", "resetTryEntry", "completion", "reset", "isNaN", "length", "displayName", "isGeneratorFunction", "constructor", "name", "mark", "setPrototypeOf", "__proto__", "awrap", "async", "Promise", "keys", "reverse", "pop", "prev", "char<PERSON>t", "slice", "stop", "rval", "handle", "complete", "finish", "catch", "_catch", "<PERSON><PERSON><PERSON>", "asyncGeneratorStep", "_asyncToGenerator", "arguments", "apply", "_next", "_throw", "_toConsumableArray", "_arrayWithoutHoles", "_iterableToArray", "_unsupportedIterableToArray", "_nonIterableSpread", "_arrayLikeToArray", "toString", "Array", "from", "test", "isArray", "api", "reactive", "ref", "onActivated", "onDeactivated", "onBeforeUnmount", "watch", "whetherUseIntelligentize", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ElMessage", "format", "SuggestRecommendType", "SuggestRecommendUnit", "ReviewSimilarityQuery", "__default__", "props", "__props", "emit", "__emit", "isReviewResult", "formRef", "form", "reviewResult", "rejectReason", "SuggestBigType", "SuggestSmallType", "reviewOpinion", "transactType", "mainHandleOfficeId", "handleOfficeIds", "rules", "required", "message", "trigger", "unitParams", "suggestionRejectReason", "rejectName", "elTypeShow", "visibleTypeShow", "elUnitShow", "visibleUnitShow", "suggestId", "bigThemeId", "smallThemeId", "james", "jordan", "kobe", "duncan", "console", "log", "wade", "setTimeout", "setGlobalState", "AiChatCode", "AiChatWindow", "AiChatParams", "businessId", "id", "dictionaryData", "suggestionThemeSelect", "suggestionNextNodes", "window", "addEventListener", "<PERSON><PERSON><PERSON><PERSON>", "signId", "typeCallback", "isElIsShow", "isVisibleIsShow", "typeSelect", "item", "SuggestBigTypeChange", "_id", "unitCallback", "unitSelect", "includes", "concat", "_ref2", "_callee", "res", "data", "_callee$", "_context", "isLock", "ids", "_ref3", "_callee2", "index", "_callee2$", "_context2", "suggestionId", "formType", "nodeName", "reviewResultChange", "nodeId", "_ref4", "_callee3", "_callee3$", "_context3", "dictCodes", "suggestion_reject_reason", "_ref5", "_callee4", "_callee4$", "_context4", "query", "isUsing", "children", "map", "transactTypeChange", "submitForm", "_ref6", "_callee5", "formEl", "_callee5$", "_context5", "validate", "valid", "fields", "suggestionComplete", "_x", "_ref7", "_callee6", "_yield$api$suggestion", "code", "_callee6$", "_context6", "nextNodeId", "variable", "handleContent", "spareDict", "handleOfficeType", "join", "resetForm", "AiChatContent", "immediate", "hopeHandleOfficeIds", "content", "selectUnit", "JSON", "stringify"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/microApp/proposal/src/views/SuggestReview/component/SuggestReviewDetail.vue"], "sourcesContent": ["<template>\r\n  <div class=\"SuggestReviewDetail\">\r\n    <div class=\"SuggestReviewDetailNameBody\">\r\n      <div class=\"SuggestReviewDetailName\">\r\n        <div>{{ props.name }}</div>\r\n      </div>\r\n    </div>\r\n    <el-form ref=\"formRef\" :model=\"form\" :rules=\"rules\" inline label-position=\"top\" class=\"globalForm\">\r\n      <el-form-item label=\"审查结果\" prop=\"reviewResult\" class=\"globalFormTitle\" v-show=\"props.signId != '1'\">\r\n        <el-radio-group v-model=\"form.reviewResult\" @change=\"reviewResultChange\">\r\n          <el-radio v-for=\"item in reviewResult\" :disabled=\"props.isLock\" :key=\"item.nodeId\" :label=\"item.nodeId\">{{\r\n            item.nodeName }}</el-radio>\r\n        </el-radio-group>\r\n        <template v-if=\"whetherUseIntelligentize\">\r\n          <intelligent-assistant v-model:elIsShow=\"elTypeShow\" v-model=\"visibleTypeShow\">\r\n            <SuggestRecommendType :id=\"props.id\" :content=\"props.content\" @callback=\"typeCallback\" @select=\"typeSelect\">\r\n            </SuggestRecommendType>\r\n          </intelligent-assistant>\r\n        </template>\r\n      </el-form-item>\r\n      <el-form-item :label=\"`${rejectName}理由`\"\r\n        v-show=\"isReviewResult === 'noAccept' && props.signId != '1' && props.signId != '2'\" prop=\"rejectReason\"\r\n        class=\"globalFormTitle\">\r\n        <el-select v-model=\"form.rejectReason\" :placeholder=\"`请选择${rejectName}理由`\" clearable>\r\n          <el-option v-for=\"item in suggestionRejectReason\" :key=\"item.key\" :label=\"item.name\" :value=\"item.key\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"提案大类\" prop=\"SuggestBigType\">\r\n        <el-select v-model=\"form.SuggestBigType\" placeholder=\"请选择提案大类\" @change=\"SuggestBigTypeChange\"\r\n          :disabled=\"props.isLock\" clearable>\r\n          <el-option v-for=\"item in SuggestBigType\" :key=\"item.id\" :label=\"item.name\" :value=\"item.id\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"提案小类\">\r\n        <el-select v-model=\"form.SuggestSmallType\" placeholder=\"请选择提案小类\" :disabled=\"props.isLock\" clearable>\r\n          <el-option v-for=\"item in SuggestSmallType\" :key=\"item.id\" :label=\"item.name\" :value=\"item.id\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"审查意见\" class=\"globalFormTitle\" v-if=\"props.signId != '1'\">\r\n        <el-input v-model=\"form.reviewOpinion\" placeholder=\"请输入审查意见\" :disabled=\"props.isLock\" type=\"textarea\" :rows=\"5\"\r\n          clearable />\r\n      </el-form-item>\r\n      <template v-if=\"isReviewResult === 'success' && props.signId != '1'\">\r\n        <el-form-item label=\" 办理方式\">\r\n          <el-select v-model=\"form.transactType\" placeholder=\"请选择办理方式\" @change=\"transactTypeChange\" clearable>\r\n            <el-option label=\"主办\" value=\"main_assist\" />\r\n            <el-option label=\"分办\" value=\"publish\" />\r\n          </el-select>\r\n        </el-form-item>\r\n      </template>\r\n      <template v-if=\"isReviewResult === 'success' && form.transactType === 'main_assist' && props.signId != '1'\">\r\n        <el-form-item label=\"主办单位\" prop=\"mainHandleOfficeId\" class=\"globalFormTitle\">\r\n          <suggest-simple-select-unit v-model=\"form.mainHandleOfficeId\" :filterId=\"form.handleOfficeIds\"\r\n            :max=\"1\"></suggest-simple-select-unit>\r\n          <template v-if=\"whetherUseIntelligentize\">\r\n            <intelligent-assistant v-model:elIsShow=\"elUnitShow\" v-model=\"visibleUnitShow\">\r\n              <SuggestRecommendUnit :params=\"unitParams\" @callback=\"unitCallback\" @select=\"unitSelect\">\r\n              </SuggestRecommendUnit>\r\n            </intelligent-assistant>\r\n          </template>\r\n        </el-form-item>\r\n      </template>\r\n      <!-- <template v-if=\"isReviewResult === 'success' && form.transactType === 'main_assist' && props.signId != '1'\">\r\n        <el-form-item label=\"协办单位\" class=\"globalFormTitle\">\r\n          <suggest-simple-select-unit v-model=\"form.handleOfficeIds\"\r\n            :filterId=\"form.mainHandleOfficeId\"></suggest-simple-select-unit>\r\n        </el-form-item>\r\n      </template> -->\r\n      <template v-if=\"isReviewResult === 'success' && form.transactType === 'publish' && props.signId != '1'\">\r\n        <el-form-item label=\"分办单位\" prop=\"handleOfficeIds\" class=\"globalFormTitle\">\r\n          <suggest-simple-select-unit v-model=\"form.handleOfficeIds\"></suggest-simple-select-unit>\r\n          <template v-if=\"whetherUseIntelligentize\">\r\n            <intelligent-assistant v-model:elIsShow=\"elUnitShow\" v-model=\"visibleUnitShow\">\r\n              <SuggestRecommendUnit :params=\"unitParams\" @callback=\"unitCallback\" @select=\"unitSelect\">\r\n              </SuggestRecommendUnit>\r\n            </intelligent-assistant>\r\n          </template>\r\n        </el-form-item>\r\n      </template>\r\n      <ReviewSimilarityQuery :id=\"props.id\" :content=\"props.content\" @callback=\"resetForm\"></ReviewSimilarityQuery>\r\n      <div class=\"globalFormButton\">\r\n        <el-button type=\"primary\" :disabled=\"props.isLock\" @click=\"submitForm(formRef)\">提交</el-button>\r\n        <el-button @click=\"resetForm\">取消</el-button>\r\n      </div>\r\n      <div class=\"globalFormButton\" style=\"margin-top: 10px;\" v-if=\"props.isLock\">\r\n        <div> 提案已被锁定（由{{ props.lockVo.lockUserName }}于{{ format(props.lockVo.lockDate) }}） 锁定 </div>\r\n      </div>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'SuggestReviewDetail' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { reactive, ref, onActivated, onDeactivated, onBeforeUnmount, watch } from 'vue'\r\nimport { whetherUseIntelligentize } from 'common/js/system_var.js'\r\nimport { qiankunMicro } from 'common/config/MicroGlobal'\r\nimport { ElMessage } from 'element-plus'\r\nimport { format } from 'common/js/time.js'\r\nimport SuggestRecommendType from '@/components/SuggestRecommendType/SuggestRecommendType.vue'\r\nimport SuggestRecommendUnit from '@/components/SuggestRecommendUnit/SuggestRecommendUnit.vue'\r\nimport ReviewSimilarityQuery from './ReviewSimilarityQuery'\r\nconst props = defineProps({\r\n  id: { type: String, default: '' },\r\n  name: { type: String, default: '提案审查' },\r\n  content: { type: String, default: '' },\r\n  SuggestBigType: { type: String, default: '' },\r\n  SuggestSmallType: { type: String, default: '' },\r\n  isLock: { type: Boolean, default: false },\r\n  hopeHandleOfficeIds: { type: Array, default: () => ([]) },\r\n  lockVo: { type: Object, default: () => ({}) },\r\n  queryType: { type: String, default: '' },\r\n  signId: { type: String, default: '' },\r\n  bigThemeId: { type: String, default: '' },\r\n  smallThemeId: { type: String, default: '' },\r\n  jordan: { type: String, default: '' },\r\n  james: { type: String, default: '' },\r\n  kobe: { type: String, default: '' },\r\n  duncan: { type: String, default: '' },\r\n  wade: { type: String, default: '' }\r\n})\r\nconst emit = defineEmits(['callback'])\r\nconst isReviewResult = ref('')\r\nconst formRef = ref()\r\nconst form = reactive({\r\n  reviewResult: '', // 审查结果\r\n  rejectReason: '',\r\n  SuggestBigType: '', // 提案大类\r\n  SuggestSmallType: '', // 提案小类\r\n  reviewOpinion: '', // 审查意见\r\n  transactType: '', // 请选择办理方式\r\n  mainHandleOfficeId: [],\r\n  handleOfficeIds: []\r\n})\r\nconst rules = reactive({\r\n  reviewResult: [{ required: true, message: '请选择审查结果', trigger: ['blur', 'change'] }],\r\n  rejectReason: [{ required: false, message: '请选择不予接收理由', trigger: ['blur', 'change'] }],\r\n  SuggestBigType: [{ required: true, message: '请选择提案大类', trigger: ['blur', 'change'] }],\r\n  mainHandleOfficeId: [{ type: 'array', required: false, message: '请选择主办单位', trigger: ['blur', 'change'] }],\r\n  handleOfficeIds: [{ type: 'array', required: false, message: '请选择分办单位', trigger: ['blur', 'change'] }]\r\n})\r\nconst unitParams = ref({})\r\nconst reviewResult = ref([])\r\nconst SuggestBigType = ref([])\r\nconst SuggestSmallType = ref([])\r\nconst suggestionRejectReason = ref([])\r\nconst rejectName = ref('')\r\n\r\nconst elTypeShow = ref(false)\r\nconst visibleTypeShow = ref(false)\r\nconst elUnitShow = ref(false)\r\nconst visibleUnitShow = ref(false)\r\nconst suggestId = ref('')\r\n// const elAiChatClass = AiChatClass()\r\nonActivated(() => {\r\n  form.SuggestBigType = props.bigThemeId\r\n  form.SuggestSmallType = props.smallThemeId\r\n  form.reviewOpinion = props.james || ''\r\n  form.reviewResult = props.jordan || ''\r\n  if (form.reviewResult == 'prepareSubmitHandle') {\r\n    isReviewResult.value = 'success'\r\n  }\r\n  form.transactType = props.kobe || ''\r\n  form.mainHandleOfficeId = [props.duncan] || ''\r\n  console.log('props.wade==>', props.wade)\r\n  setTimeout(() => {\r\n    form.handleOfficeIds = [props.wade] || ''\r\n  }, 2000);\r\n  qiankunMicro.setGlobalState({ AiChatCode: 'ai-assistant-chat' })\r\n  qiankunMicro.setGlobalState({ AiChatWindow: true })\r\n  qiankunMicro.setGlobalState({ AiChatParams: { businessId: props.id } })\r\n  dictionaryData()\r\n  suggestionThemeSelect()\r\n  if (props.id) {\r\n    suggestionNextNodes()\r\n    suggestId.value = props.id\r\n  }\r\n  window.addEventListener('beforeunload', (e) => { suggestionUnlock() })\r\n  if (props.signId == '1') {\r\n    delete rules.reviewResult\r\n    delete rules.rejectReason\r\n  }\r\n})\r\n\r\nconst typeCallback = (isElIsShow, isVisibleIsShow) => {\r\n  if (props.signId != '1') {\r\n    elTypeShow.value = isElIsShow\r\n    visibleTypeShow.value = isVisibleIsShow\r\n    return\r\n  }\r\n}\r\nconst typeSelect = (item, id) => {\r\n  if (id) {\r\n    form.SuggestBigType = id\r\n    SuggestBigTypeChange()\r\n    form.SuggestSmallType = item._id\r\n  } else {\r\n    form.SuggestBigType = item._id\r\n    SuggestBigTypeChange()\r\n  }\r\n}\r\nconst unitCallback = (isElIsShow, isVisibleIsShow) => {\r\n  elUnitShow.value = isElIsShow\r\n  visibleUnitShow.value = isVisibleIsShow\r\n}\r\nconst unitSelect = (item) => {\r\n  if (form.transactType === 'main_assist') {\r\n    if (!form.mainHandleOfficeId.length) {\r\n      if (!form.handleOfficeIds.includes(item.id)) {\r\n        form.mainHandleOfficeId = [item.id]\r\n        ElMessage({ type: 'success', message: `已为您将【${item.name}】添加到主办单位` })\r\n      }\r\n    } else {\r\n      // if (!form.mainHandleOfficeId.includes(item.id) && !form.handleOfficeIds.includes(item.id)) {\r\n      //   form.handleOfficeIds = [...form.handleOfficeIds, item.id]\r\n      //   ElMessage({ type: 'success', message: `当前已选择主办单位，已为您将【${item.name}】添加到协办单位` })\r\n      // }\r\n    }\r\n  } else if (form.transactType === 'publish') {\r\n    if (!form.handleOfficeIds.includes(item.id)) {\r\n      form.handleOfficeIds = [...form.handleOfficeIds, item.id]\r\n      ElMessage({ type: 'success', message: `已为您将${item.name}添加到分办单位` })\r\n    }\r\n  }\r\n}\r\n\r\nconst suggestionUnlock = async () => {\r\n  if (props.isLock) return\r\n  const res = await api.suggestionUnlock({ ids: [suggestId.value] })\r\n  var { data } = res\r\n  console.log(data)\r\n}\r\nconst suggestionNextNodes = async () => {\r\n  const res = await api.suggestionNextNodes({ suggestionId: props.id })\r\n  var { data } = res\r\n  if (props.signId == '2') {\r\n    reviewResult.value = [\r\n      {\r\n        \"nodeId\": \"prepareSubmitHandle\",\r\n        \"nodeName\": \"立案\",\r\n        \"formType\": \"success\"\r\n      },\r\n      {\r\n        \"nodeId\": \"exchangeLetter\",\r\n        \"nodeName\": \"转来信\",\r\n        \"formType\": \"other\"\r\n      },\r\n      {\r\n        \"nodeId\": \"exchangeSocial\",\r\n        \"nodeName\": \"转社情民意\",\r\n        \"formType\": \"other\"\r\n      },\r\n      {\r\n        \"nodeId\": \"rejectReceive\",\r\n        \"nodeName\": \"不予立案\",\r\n        \"formType\": \"noAccept\"\r\n      },\r\n      {\r\n        \"nodeId\": \"cancelSuggestion\",\r\n        \"nodeName\": \"撤案\",\r\n        \"formType\": \"other\"\r\n      },\r\n      {\r\n        \"nodeId\": \"returnSubmit\",\r\n        \"nodeName\": \"退回\",\r\n        \"formType\": \"other\"\r\n      }\r\n    ]\r\n  } else {\r\n    reviewResult.value = data\r\n  }\r\n  for (let index = 0; index < data.length; index++) {\r\n    const item = data[index]\r\n    if (item.formType === 'noAccept') {\r\n      rejectName.value = item.nodeName\r\n    }\r\n  }\r\n}\r\nconst reviewResultChange = () => {\r\n  isReviewResult.value = ''\r\n  rules.rejectReason = [{ required: false, message: `请选择${rejectName.value}理由`, trigger: ['blur', 'change'] }]\r\n  for (let index = 0; index < reviewResult.value.length; index++) {\r\n    const item = reviewResult.value[index]\r\n    if (item.nodeId === form.reviewResult) {\r\n      isReviewResult.value = item.formType\r\n      if (item.formType === 'noAccept') {\r\n        rules.rejectReason = [{ required: true, message: `请选择${rejectName.value}理由`, trigger: ['blur', 'change'] }]\r\n      }\r\n    }\r\n  }\r\n}\r\nconst dictionaryData = async () => {\r\n  const res = await api.dictionaryData({ dictCodes: ['suggestion_reject_reason'] })\r\n  var { data } = res\r\n  suggestionRejectReason.value = data.suggestion_reject_reason\r\n}\r\nconst suggestionThemeSelect = async () => {\r\n  const res = await api.suggestionThemeSelect({ query: { isUsing: 1 } })\r\n  var { data } = res\r\n  SuggestBigType.value = data\r\n  SuggestBigTypeChange()\r\n}\r\nconst SuggestBigTypeChange = () => {\r\n  if (form.SuggestBigType) {\r\n    for (let index = 0; index < SuggestBigType.value.length; index++) {\r\n      const item = SuggestBigType.value[index]\r\n      if (item.id === form.SuggestBigType) {\r\n        if (!item.children.map(v => v.id).includes(form.SuggestSmallType)) {\r\n          form.SuggestSmallType = ''\r\n        }\r\n        SuggestSmallType.value = item.children\r\n      }\r\n    }\r\n  } else {\r\n    form.SuggestSmallType = ''\r\n    SuggestSmallType.value = []\r\n  }\r\n}\r\nconst transactTypeChange = () => {\r\n  if (form.transactType === 'main_assist') {\r\n    rules.mainHandleOfficeId = [{ type: 'array', required: true, message: '请选择主办单位', trigger: ['blur', 'change'] }]\r\n    rules.handleOfficeIds = [{ type: 'array', required: false, message: '请选择分办单位', trigger: ['blur', 'change'] }]\r\n  } else if (form.transactType === 'publish') {\r\n    rules.mainHandleOfficeId = [{ type: 'array', required: false, message: '请选择主办单位', trigger: ['blur', 'change'] }]\r\n    rules.handleOfficeIds = [{ type: 'array', required: true, message: '请选择分办单位', trigger: ['blur', 'change'] }]\r\n  } else {\r\n    rules.mainHandleOfficeId = [{ type: 'array', required: false, message: '请选择主办单位', trigger: ['blur', 'change'] }]\r\n    rules.handleOfficeIds = [{ type: 'array', required: false, message: '请选择分办单位', trigger: ['blur', 'change'] }]\r\n  }\r\n}\r\nconst submitForm = async (formEl) => {\r\n  if (!formEl) return\r\n  await formEl.validate((valid, fields) => {\r\n    if (valid) { emit('editCallback', suggestionComplete, form) } else { ElMessage({ type: 'warning', message: '请根据提示信息完善字段内容！' }) }\r\n  })\r\n}\r\n\r\nconst suggestionComplete = async () => {\r\n  const { code } = await api.suggestionComplete({\r\n    suggestionId: props.id,\r\n    nextNodeId: props.signId == '1' ? 'review' : props.signId == '2' ? 'prepareVerify' : form.reviewResult, // 审查结果\r\n    bigThemeId: form.SuggestBigType, // 提案大类\r\n    smallThemeId: form.SuggestSmallType, // 提案小类\r\n    variable: {\r\n      handleContent: form.reviewOpinion, // 审查意见\r\n      spareDict: isReviewResult.value === 'noAccept' ? form.rejectReason : ''\r\n    },\r\n    handleOfficeType: form.transactType, // 办理方式\r\n    mainHandleOfficeId: form.mainHandleOfficeId.join(''), // 主办单位\r\n    handleOfficeIds: form.handleOfficeIds.length > 0 && form.handleOfficeIds[0] ? form.handleOfficeIds : [] // 协办或分办单位\r\n  })\r\n  if (code === 200) {\r\n    ElMessage({ type: 'success', message: '审查成功' })\r\n    emit('callback')\r\n  }\r\n}\r\nconst resetForm = () => { emit('callback') }\r\n\r\nonDeactivated(() => {\r\n  qiankunMicro.setGlobalState({ AiChatWindow: false })\r\n  qiankunMicro.setGlobalState({ AiChatCode: 'test_chat' })\r\n  qiankunMicro.setGlobalState({ AiChatParams: {} })\r\n  qiankunMicro.setGlobalState({ AiChatContent: '' })\r\n  // elAiChatClass.AiChatConfig({ AiChatCode: 'test_chat', AiChatWindow: false })\r\n  // elAiChatClass.AiChatHistory()\r\n  suggestionUnlock()\r\n})\r\nonBeforeUnmount(() => {\r\n  qiankunMicro.setGlobalState({ AiChatWindow: false })\r\n  qiankunMicro.setGlobalState({ AiChatCode: 'test_chat' })\r\n  qiankunMicro.setGlobalState({ AiChatParams: {} })\r\n  qiankunMicro.setGlobalState({ AiChatContent: '' })\r\n  // elAiChatClass.AiChatConfig({ AiChatCode: 'test_chat', AiChatWindow: false })\r\n  // elAiChatClass.AiChatHistory()\r\n  // suggestionUnlock()\r\n})\r\nwatch(() => [props.SuggestBigType, props.SuggestSmallType], () => {\r\n  form.SuggestBigType = props.SuggestBigType\r\n  form.SuggestSmallType = props.SuggestSmallType\r\n  SuggestBigTypeChange()\r\n}, { immediate: true })\r\nwatch(() => [props.hopeHandleOfficeIds, props.content], () => {\r\n  // if (props.content) {\r\n  //   elAiChatClass.AiChatConfig({\r\n  //     AiChatCode: 'ai-assistant-chat',\r\n  //     AiChatModuleId: props.id,\r\n  //     AiChatWindow: true,\r\n  //     AiChatContent: props.content\r\n  //   })\r\n  //   elAiChatClass.AiChatHistory('ai-assistant-chat', props.id)\r\n  // }\r\n  qiankunMicro.setGlobalState({ AiChatContent: props.content })\r\n  const selectUnit = props?.hopeHandleOfficeIds || []\r\n  unitParams.value = { selectUnit: JSON.stringify(selectUnit), content: props.content }\r\n}, { immediate: true })\r\n</script>\r\n<style lang=\"scss\">\r\n.SuggestReviewDetail {\r\n  width: 100%;\r\n  height: 100%;\r\n\r\n  .SuggestReviewDetailNameBody {\r\n    padding: 0 var(--zy-distance-one);\r\n    padding-top: var(--zy-distance-one);\r\n\r\n    .SuggestReviewDetailName {\r\n      width: 100%;\r\n      color: var(--zy-el-color-primary);\r\n      font-size: var(--zy-name-font-size);\r\n      line-height: var(--zy-line-height);\r\n      font-weight: bold;\r\n      position: relative;\r\n      text-align: center;\r\n\r\n      div {\r\n        display: inline-block;\r\n        background-color: #fff;\r\n        position: relative;\r\n        z-index: 2;\r\n        padding: 0 20px;\r\n      }\r\n\r\n      &::after {\r\n        content: \"\";\r\n        position: absolute;\r\n        top: 50%;\r\n        left: 0;\r\n        transform: translateY(-50%);\r\n        width: 100%;\r\n        height: 1px;\r\n        background-color: var(--zy-el-color-primary);\r\n      }\r\n    }\r\n  }\r\n\r\n  .suggest-simple-select-unit {\r\n    box-shadow: 0 0 0 1px var(--zy-el-input-border-color, var(--zy-el-border-color)) inset;\r\n    border-radius: var(--zy-el-input-border-radius, var(--zy-el-border-radius-base));\r\n  }\r\n}\r\n</style>\r\n"], "mappings": "+CA+FA,qJAAAA,mBAAA,YAAAA,oBAAA,WAAAC,CAAA,SAAAC,CAAA,EAAAD,CAAA,OAAAE,CAAA,GAAAC,MAAA,CAAAC,SAAA,EAAAC,CAAA,GAAAH,CAAA,CAAAI,cAAA,EAAAC,CAAA,GAAAJ,MAAA,CAAAK,cAAA,cAAAP,CAAA,EAAAD,CAAA,EAAAE,CAAA,IAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,CAAAO,KAAA,KAAAC,CAAA,wBAAAC,MAAA,GAAAA,MAAA,OAAAC,CAAA,GAAAF,CAAA,CAAAG,QAAA,kBAAAC,CAAA,GAAAJ,CAAA,CAAAK,aAAA,uBAAAC,CAAA,GAAAN,CAAA,CAAAO,WAAA,8BAAAC,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAC,MAAA,CAAAK,cAAA,CAAAP,CAAA,EAAAD,CAAA,IAAAS,KAAA,EAAAP,CAAA,EAAAiB,UAAA,MAAAC,YAAA,MAAAC,QAAA,SAAApB,CAAA,CAAAD,CAAA,WAAAkB,MAAA,mBAAAjB,CAAA,IAAAiB,MAAA,YAAAA,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,gBAAAoB,KAAArB,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAK,CAAA,GAAAV,CAAA,IAAAA,CAAA,CAAAI,SAAA,YAAAmB,SAAA,GAAAvB,CAAA,GAAAuB,SAAA,EAAAX,CAAA,GAAAT,MAAA,CAAAqB,MAAA,CAAAd,CAAA,CAAAN,SAAA,GAAAU,CAAA,OAAAW,OAAA,CAAApB,CAAA,gBAAAE,CAAA,CAAAK,CAAA,eAAAH,KAAA,EAAAiB,gBAAA,CAAAzB,CAAA,EAAAC,CAAA,EAAAY,CAAA,MAAAF,CAAA,aAAAe,SAAA1B,CAAA,EAAAD,CAAA,EAAAE,CAAA,mBAAA0B,IAAA,YAAAC,GAAA,EAAA5B,CAAA,CAAA6B,IAAA,CAAA9B,CAAA,EAAAE,CAAA,cAAAD,CAAA,aAAA2B,IAAA,WAAAC,GAAA,EAAA5B,CAAA,QAAAD,CAAA,CAAAsB,IAAA,GAAAA,IAAA,MAAAS,CAAA,qBAAAC,CAAA,qBAAAC,CAAA,gBAAAC,CAAA,gBAAAC,CAAA,gBAAAZ,UAAA,cAAAa,kBAAA,cAAAC,2BAAA,SAAAC,CAAA,OAAApB,MAAA,CAAAoB,CAAA,EAAA1B,CAAA,qCAAA2B,CAAA,GAAApC,MAAA,CAAAqC,cAAA,EAAAC,CAAA,GAAAF,CAAA,IAAAA,CAAA,CAAAA,CAAA,CAAAG,MAAA,QAAAD,CAAA,IAAAA,CAAA,KAAAvC,CAAA,IAAAG,CAAA,CAAAyB,IAAA,CAAAW,CAAA,EAAA7B,CAAA,MAAA0B,CAAA,GAAAG,CAAA,OAAAE,CAAA,GAAAN,0BAAA,CAAAjC,SAAA,GAAAmB,SAAA,CAAAnB,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAc,CAAA,YAAAM,sBAAA3C,CAAA,gCAAA4C,OAAA,WAAA7C,CAAA,IAAAkB,MAAA,CAAAjB,CAAA,EAAAD,CAAA,YAAAC,CAAA,gBAAA6C,OAAA,CAAA9C,CAAA,EAAAC,CAAA,sBAAA8C,cAAA9C,CAAA,EAAAD,CAAA,aAAAgD,OAAA9C,CAAA,EAAAK,CAAA,EAAAG,CAAA,EAAAE,CAAA,QAAAE,CAAA,GAAAa,QAAA,CAAA1B,CAAA,CAAAC,CAAA,GAAAD,CAAA,EAAAM,CAAA,mBAAAO,CAAA,CAAAc,IAAA,QAAAZ,CAAA,GAAAF,CAAA,CAAAe,GAAA,EAAAE,CAAA,GAAAf,CAAA,CAAAP,KAAA,SAAAsB,CAAA,uBAAAA,CAAA,IAAA1B,CAAA,CAAAyB,IAAA,CAAAC,CAAA,eAAA/B,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,CAAAmB,OAAA,EAAAC,IAAA,WAAAlD,CAAA,IAAA+C,MAAA,SAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,gBAAAX,CAAA,IAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,QAAAZ,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,EAAAoB,IAAA,WAAAlD,CAAA,IAAAe,CAAA,CAAAP,KAAA,GAAAR,CAAA,EAAAS,CAAA,CAAAM,CAAA,gBAAAf,CAAA,WAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,SAAAA,CAAA,CAAAE,CAAA,CAAAe,GAAA,SAAA3B,CAAA,EAAAK,CAAA,oBAAAE,KAAA,WAAAA,MAAAR,CAAA,EAAAI,CAAA,aAAA+C,2BAAA,eAAApD,CAAA,WAAAA,CAAA,EAAAE,CAAA,IAAA8C,MAAA,CAAA/C,CAAA,EAAAI,CAAA,EAAAL,CAAA,EAAAE,CAAA,gBAAAA,CAAA,GAAAA,CAAA,GAAAA,CAAA,CAAAiD,IAAA,CAAAC,0BAAA,EAAAA,0BAAA,IAAAA,0BAAA,qBAAA1B,iBAAA1B,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAE,CAAA,GAAAwB,CAAA,mBAAArB,CAAA,EAAAE,CAAA,QAAAL,CAAA,KAAA0B,CAAA,QAAAoB,KAAA,sCAAA9C,CAAA,KAAA2B,CAAA,oBAAAxB,CAAA,QAAAE,CAAA,WAAAH,KAAA,EAAAR,CAAA,EAAAqD,IAAA,eAAAjD,CAAA,CAAAkD,MAAA,GAAA7C,CAAA,EAAAL,CAAA,CAAAwB,GAAA,GAAAjB,CAAA,UAAAE,CAAA,GAAAT,CAAA,CAAAmD,QAAA,MAAA1C,CAAA,QAAAE,CAAA,GAAAyC,mBAAA,CAAA3C,CAAA,EAAAT,CAAA,OAAAW,CAAA,QAAAA,CAAA,KAAAmB,CAAA,mBAAAnB,CAAA,qBAAAX,CAAA,CAAAkD,MAAA,EAAAlD,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAsD,KAAA,GAAAtD,CAAA,CAAAwB,GAAA,sBAAAxB,CAAA,CAAAkD,MAAA,QAAAhD,CAAA,KAAAwB,CAAA,QAAAxB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAwB,GAAA,EAAAxB,CAAA,CAAAuD,iBAAA,CAAAvD,CAAA,CAAAwB,GAAA,uBAAAxB,CAAA,CAAAkD,MAAA,IAAAlD,CAAA,CAAAwD,MAAA,WAAAxD,CAAA,CAAAwB,GAAA,GAAAtB,CAAA,GAAA0B,CAAA,MAAAK,CAAA,GAAAX,QAAA,CAAA3B,CAAA,EAAAE,CAAA,EAAAG,CAAA,oBAAAiC,CAAA,CAAAV,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAiD,IAAA,GAAApB,CAAA,GAAAF,CAAA,EAAAM,CAAA,CAAAT,GAAA,KAAAM,CAAA,qBAAA1B,KAAA,EAAA6B,CAAA,CAAAT,GAAA,EAAAyB,IAAA,EAAAjD,CAAA,CAAAiD,IAAA,kBAAAhB,CAAA,CAAAV,IAAA,KAAArB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAkD,MAAA,YAAAlD,CAAA,CAAAwB,GAAA,GAAAS,CAAA,CAAAT,GAAA,mBAAA4B,oBAAAzD,CAAA,EAAAE,CAAA,QAAAG,CAAA,GAAAH,CAAA,CAAAqD,MAAA,EAAAhD,CAAA,GAAAP,CAAA,CAAAa,QAAA,CAAAR,CAAA,OAAAE,CAAA,KAAAN,CAAA,SAAAC,CAAA,CAAAsD,QAAA,qBAAAnD,CAAA,IAAAL,CAAA,CAAAa,QAAA,CAAAiD,MAAA,KAAA5D,CAAA,CAAAqD,MAAA,aAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,EAAAwD,mBAAA,CAAAzD,CAAA,EAAAE,CAAA,eAAAA,CAAA,CAAAqD,MAAA,kBAAAlD,CAAA,KAAAH,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,uCAAA1D,CAAA,iBAAA8B,CAAA,MAAAzB,CAAA,GAAAiB,QAAA,CAAApB,CAAA,EAAAP,CAAA,CAAAa,QAAA,EAAAX,CAAA,CAAA2B,GAAA,mBAAAnB,CAAA,CAAAkB,IAAA,SAAA1B,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,GAAAnB,CAAA,CAAAmB,GAAA,EAAA3B,CAAA,CAAAsD,QAAA,SAAArB,CAAA,MAAAvB,CAAA,GAAAF,CAAA,CAAAmB,GAAA,SAAAjB,CAAA,GAAAA,CAAA,CAAA0C,IAAA,IAAApD,CAAA,CAAAF,CAAA,CAAAgE,UAAA,IAAApD,CAAA,CAAAH,KAAA,EAAAP,CAAA,CAAA+D,IAAA,GAAAjE,CAAA,CAAAkE,OAAA,eAAAhE,CAAA,CAAAqD,MAAA,KAAArD,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,GAAAC,CAAA,CAAAsD,QAAA,SAAArB,CAAA,IAAAvB,CAAA,IAAAV,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,sCAAA7D,CAAA,CAAAsD,QAAA,SAAArB,CAAA,cAAAgC,aAAAlE,CAAA,QAAAD,CAAA,KAAAoE,MAAA,EAAAnE,CAAA,YAAAA,CAAA,KAAAD,CAAA,CAAAqE,QAAA,GAAApE,CAAA,WAAAA,CAAA,KAAAD,CAAA,CAAAsE,UAAA,GAAArE,CAAA,KAAAD,CAAA,CAAAuE,QAAA,GAAAtE,CAAA,WAAAuE,UAAA,CAAAC,IAAA,CAAAzE,CAAA,cAAA0E,cAAAzE,CAAA,QAAAD,CAAA,GAAAC,CAAA,CAAA0E,UAAA,QAAA3E,CAAA,CAAA4B,IAAA,oBAAA5B,CAAA,CAAA6B,GAAA,EAAA5B,CAAA,CAAA0E,UAAA,GAAA3E,CAAA,aAAAyB,QAAAxB,CAAA,SAAAuE,UAAA,MAAAJ,MAAA,aAAAnE,CAAA,CAAA4C,OAAA,CAAAsB,YAAA,cAAAS,KAAA,iBAAAlC,OAAA1C,CAAA,QAAAA,CAAA,WAAAA,CAAA,QAAAE,CAAA,GAAAF,CAAA,CAAAY,CAAA,OAAAV,CAAA,SAAAA,CAAA,CAAA4B,IAAA,CAAA9B,CAAA,4BAAAA,CAAA,CAAAiE,IAAA,SAAAjE,CAAA,OAAA6E,KAAA,CAAA7E,CAAA,CAAA8E,MAAA,SAAAvE,CAAA,OAAAG,CAAA,YAAAuD,KAAA,aAAA1D,CAAA,GAAAP,CAAA,CAAA8E,MAAA,OAAAzE,CAAA,CAAAyB,IAAA,CAAA9B,CAAA,EAAAO,CAAA,UAAA0D,IAAA,CAAAxD,KAAA,GAAAT,CAAA,CAAAO,CAAA,GAAA0D,IAAA,CAAAX,IAAA,OAAAW,IAAA,SAAAA,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,YAAAvD,CAAA,CAAAuD,IAAA,GAAAvD,CAAA,gBAAAqD,SAAA,QAAA/D,CAAA,iCAAAoC,iBAAA,CAAAhC,SAAA,GAAAiC,0BAAA,EAAA9B,CAAA,CAAAoC,CAAA,mBAAAlC,KAAA,EAAA4B,0BAAA,EAAAjB,YAAA,SAAAb,CAAA,CAAA8B,0BAAA,mBAAA5B,KAAA,EAAA2B,iBAAA,EAAAhB,YAAA,SAAAgB,iBAAA,CAAA2C,WAAA,GAAA7D,MAAA,CAAAmB,0BAAA,EAAArB,CAAA,wBAAAhB,CAAA,CAAAgF,mBAAA,aAAA/E,CAAA,QAAAD,CAAA,wBAAAC,CAAA,IAAAA,CAAA,CAAAgF,WAAA,WAAAjF,CAAA,KAAAA,CAAA,KAAAoC,iBAAA,6BAAApC,CAAA,CAAA+E,WAAA,IAAA/E,CAAA,CAAAkF,IAAA,OAAAlF,CAAA,CAAAmF,IAAA,aAAAlF,CAAA,WAAAE,MAAA,CAAAiF,cAAA,GAAAjF,MAAA,CAAAiF,cAAA,CAAAnF,CAAA,EAAAoC,0BAAA,KAAApC,CAAA,CAAAoF,SAAA,GAAAhD,0BAAA,EAAAnB,MAAA,CAAAjB,CAAA,EAAAe,CAAA,yBAAAf,CAAA,CAAAG,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAmB,CAAA,GAAA1C,CAAA,KAAAD,CAAA,CAAAsF,KAAA,aAAArF,CAAA,aAAAiD,OAAA,EAAAjD,CAAA,OAAA2C,qBAAA,CAAAG,aAAA,CAAA3C,SAAA,GAAAc,MAAA,CAAA6B,aAAA,CAAA3C,SAAA,EAAAU,CAAA,iCAAAd,CAAA,CAAA+C,aAAA,GAAAA,aAAA,EAAA/C,CAAA,CAAAuF,KAAA,aAAAtF,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,EAAAG,CAAA,eAAAA,CAAA,KAAAA,CAAA,GAAA8E,OAAA,OAAA5E,CAAA,OAAAmC,aAAA,CAAAzB,IAAA,CAAArB,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,GAAAG,CAAA,UAAAV,CAAA,CAAAgF,mBAAA,CAAA9E,CAAA,IAAAU,CAAA,GAAAA,CAAA,CAAAqD,IAAA,GAAAd,IAAA,WAAAlD,CAAA,WAAAA,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAQ,KAAA,GAAAG,CAAA,CAAAqD,IAAA,WAAArB,qBAAA,CAAAD,CAAA,GAAAzB,MAAA,CAAAyB,CAAA,EAAA3B,CAAA,gBAAAE,MAAA,CAAAyB,CAAA,EAAA/B,CAAA,iCAAAM,MAAA,CAAAyB,CAAA,6DAAA3C,CAAA,CAAAyF,IAAA,aAAAxF,CAAA,QAAAD,CAAA,GAAAG,MAAA,CAAAF,CAAA,GAAAC,CAAA,gBAAAG,CAAA,IAAAL,CAAA,EAAAE,CAAA,CAAAuE,IAAA,CAAApE,CAAA,UAAAH,CAAA,CAAAwF,OAAA,aAAAzB,KAAA,WAAA/D,CAAA,CAAA4E,MAAA,SAAA7E,CAAA,GAAAC,CAAA,CAAAyF,GAAA,QAAA1F,CAAA,IAAAD,CAAA,SAAAiE,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,WAAAA,IAAA,CAAAX,IAAA,OAAAW,IAAA,QAAAjE,CAAA,CAAA0C,MAAA,GAAAA,MAAA,EAAAjB,OAAA,CAAArB,SAAA,KAAA6E,WAAA,EAAAxD,OAAA,EAAAmD,KAAA,WAAAA,MAAA5E,CAAA,aAAA4F,IAAA,WAAA3B,IAAA,WAAAP,IAAA,QAAAC,KAAA,GAAA1D,CAAA,OAAAqD,IAAA,YAAAE,QAAA,cAAAD,MAAA,gBAAA1B,GAAA,GAAA5B,CAAA,OAAAuE,UAAA,CAAA3B,OAAA,CAAA6B,aAAA,IAAA1E,CAAA,WAAAE,CAAA,kBAAAA,CAAA,CAAA2F,MAAA,OAAAxF,CAAA,CAAAyB,IAAA,OAAA5B,CAAA,MAAA2E,KAAA,EAAA3E,CAAA,CAAA4F,KAAA,cAAA5F,CAAA,IAAAD,CAAA,MAAA8F,IAAA,WAAAA,KAAA,SAAAzC,IAAA,WAAArD,CAAA,QAAAuE,UAAA,IAAAG,UAAA,kBAAA1E,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,cAAAmE,IAAA,KAAApC,iBAAA,WAAAA,kBAAA5D,CAAA,aAAAsD,IAAA,QAAAtD,CAAA,MAAAE,CAAA,kBAAA+F,OAAA5F,CAAA,EAAAE,CAAA,WAAAK,CAAA,CAAAgB,IAAA,YAAAhB,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAE,CAAA,CAAA+D,IAAA,GAAA5D,CAAA,EAAAE,CAAA,KAAAL,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,KAAAM,CAAA,aAAAA,CAAA,QAAAiE,UAAA,CAAAM,MAAA,MAAAvE,CAAA,SAAAA,CAAA,QAAAG,CAAA,QAAA8D,UAAA,CAAAjE,CAAA,GAAAK,CAAA,GAAAF,CAAA,CAAAiE,UAAA,iBAAAjE,CAAA,CAAA0D,MAAA,SAAA6B,MAAA,aAAAvF,CAAA,CAAA0D,MAAA,SAAAwB,IAAA,QAAA9E,CAAA,GAAAT,CAAA,CAAAyB,IAAA,CAAApB,CAAA,eAAAM,CAAA,GAAAX,CAAA,CAAAyB,IAAA,CAAApB,CAAA,qBAAAI,CAAA,IAAAE,CAAA,aAAA4E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,gBAAAuB,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,cAAAxD,CAAA,aAAA8E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,qBAAArD,CAAA,QAAAqC,KAAA,qDAAAuC,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,YAAAT,MAAA,WAAAA,OAAA5D,CAAA,EAAAD,CAAA,aAAAE,CAAA,QAAAsE,UAAA,CAAAM,MAAA,MAAA5E,CAAA,SAAAA,CAAA,QAAAK,CAAA,QAAAiE,UAAA,CAAAtE,CAAA,OAAAK,CAAA,CAAA6D,MAAA,SAAAwB,IAAA,IAAAvF,CAAA,CAAAyB,IAAA,CAAAvB,CAAA,wBAAAqF,IAAA,GAAArF,CAAA,CAAA+D,UAAA,QAAA5D,CAAA,GAAAH,CAAA,aAAAG,CAAA,iBAAAT,CAAA,mBAAAA,CAAA,KAAAS,CAAA,CAAA0D,MAAA,IAAApE,CAAA,IAAAA,CAAA,IAAAU,CAAA,CAAA4D,UAAA,KAAA5D,CAAA,cAAAE,CAAA,GAAAF,CAAA,GAAAA,CAAA,CAAAiE,UAAA,cAAA/D,CAAA,CAAAgB,IAAA,GAAA3B,CAAA,EAAAW,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAU,CAAA,SAAA6C,MAAA,gBAAAU,IAAA,GAAAvD,CAAA,CAAA4D,UAAA,EAAAnC,CAAA,SAAA+D,QAAA,CAAAtF,CAAA,MAAAsF,QAAA,WAAAA,SAAAjG,CAAA,EAAAD,CAAA,oBAAAC,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,qBAAA5B,CAAA,CAAA2B,IAAA,mBAAA3B,CAAA,CAAA2B,IAAA,QAAAqC,IAAA,GAAAhE,CAAA,CAAA4B,GAAA,gBAAA5B,CAAA,CAAA2B,IAAA,SAAAoE,IAAA,QAAAnE,GAAA,GAAA5B,CAAA,CAAA4B,GAAA,OAAA0B,MAAA,kBAAAU,IAAA,yBAAAhE,CAAA,CAAA2B,IAAA,IAAA5B,CAAA,UAAAiE,IAAA,GAAAjE,CAAA,GAAAmC,CAAA,KAAAgE,MAAA,WAAAA,OAAAlG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAoE,UAAA,KAAArE,CAAA,cAAAiG,QAAA,CAAAhG,CAAA,CAAAyE,UAAA,EAAAzE,CAAA,CAAAqE,QAAA,GAAAG,aAAA,CAAAxE,CAAA,GAAAiC,CAAA,OAAAiE,KAAA,WAAAC,OAAApG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAkE,MAAA,KAAAnE,CAAA,QAAAI,CAAA,GAAAH,CAAA,CAAAyE,UAAA,kBAAAtE,CAAA,CAAAuB,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAwB,GAAA,EAAA6C,aAAA,CAAAxE,CAAA,YAAAK,CAAA,YAAA8C,KAAA,8BAAAiD,aAAA,WAAAA,cAAAtG,CAAA,EAAAE,CAAA,EAAAG,CAAA,gBAAAmD,QAAA,KAAA3C,QAAA,EAAA6B,MAAA,CAAA1C,CAAA,GAAAgE,UAAA,EAAA9D,CAAA,EAAAgE,OAAA,EAAA7D,CAAA,oBAAAkD,MAAA,UAAA1B,GAAA,GAAA5B,CAAA,GAAAkC,CAAA,OAAAnC,CAAA;AAAA,SAAAuG,mBAAAlG,CAAA,EAAAJ,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAK,CAAA,EAAAK,CAAA,EAAAE,CAAA,cAAAJ,CAAA,GAAAL,CAAA,CAAAO,CAAA,EAAAE,CAAA,GAAAE,CAAA,GAAAN,CAAA,CAAAD,KAAA,WAAAJ,CAAA,gBAAAL,CAAA,CAAAK,CAAA,KAAAK,CAAA,CAAA4C,IAAA,GAAArD,CAAA,CAAAe,CAAA,IAAAwE,OAAA,CAAAvC,OAAA,CAAAjC,CAAA,EAAAmC,IAAA,CAAAjD,CAAA,EAAAK,CAAA;AAAA,SAAAiG,kBAAAnG,CAAA,6BAAAJ,CAAA,SAAAD,CAAA,GAAAyG,SAAA,aAAAjB,OAAA,WAAAtF,CAAA,EAAAK,CAAA,QAAAK,CAAA,GAAAP,CAAA,CAAAqG,KAAA,CAAAzG,CAAA,EAAAD,CAAA,YAAA2G,MAAAtG,CAAA,IAAAkG,kBAAA,CAAA3F,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAoG,KAAA,EAAAC,MAAA,UAAAvG,CAAA,cAAAuG,OAAAvG,CAAA,IAAAkG,kBAAA,CAAA3F,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAoG,KAAA,EAAAC,MAAA,WAAAvG,CAAA,KAAAsG,KAAA;AAAA,SAAAE,mBAAA3G,CAAA,WAAA4G,kBAAA,CAAA5G,CAAA,KAAA6G,gBAAA,CAAA7G,CAAA,KAAA8G,2BAAA,CAAA9G,CAAA,KAAA+G,kBAAA;AAAA,SAAAA,mBAAA,cAAAlD,SAAA;AAAA,SAAAiD,4BAAA9G,CAAA,EAAAU,CAAA,QAAAV,CAAA,2BAAAA,CAAA,SAAAgH,iBAAA,CAAAhH,CAAA,EAAAU,CAAA,OAAAX,CAAA,MAAAkH,QAAA,CAAArF,IAAA,CAAA5B,CAAA,EAAA4F,KAAA,6BAAA7F,CAAA,IAAAC,CAAA,CAAA+E,WAAA,KAAAhF,CAAA,GAAAC,CAAA,CAAA+E,WAAA,CAAAC,IAAA,aAAAjF,CAAA,cAAAA,CAAA,GAAAmH,KAAA,CAAAC,IAAA,CAAAnH,CAAA,oBAAAD,CAAA,+CAAAqH,IAAA,CAAArH,CAAA,IAAAiH,iBAAA,CAAAhH,CAAA,EAAAU,CAAA;AAAA,SAAAmG,iBAAA7G,CAAA,8BAAAS,MAAA,YAAAT,CAAA,CAAAS,MAAA,CAAAE,QAAA,aAAAX,CAAA,uBAAAkH,KAAA,CAAAC,IAAA,CAAAnH,CAAA;AAAA,SAAA4G,mBAAA5G,CAAA,QAAAkH,KAAA,CAAAG,OAAA,CAAArH,CAAA,UAAAgH,iBAAA,CAAAhH,CAAA;AAAA,SAAAgH,kBAAAhH,CAAA,EAAAU,CAAA,aAAAA,CAAA,IAAAA,CAAA,GAAAV,CAAA,CAAA4E,MAAA,MAAAlE,CAAA,GAAAV,CAAA,CAAA4E,MAAA,YAAA9E,CAAA,MAAAK,CAAA,GAAA+G,KAAA,CAAAxG,CAAA,GAAAZ,CAAA,GAAAY,CAAA,EAAAZ,CAAA,IAAAK,CAAA,CAAAL,CAAA,IAAAE,CAAA,CAAAF,CAAA,UAAAK,CAAA;AADA,OAAOmH,GAAG,MAAM,OAAO;AACvB,SAASC,QAAQ,EAAEC,GAAG,EAAEC,WAAW,EAAEC,aAAa,EAAEC,eAAe,EAAEC,KAAK,QAAQ,KAAK;AACvF,SAASC,wBAAwB,QAAQ,yBAAyB;AAClE,SAASC,YAAY,QAAQ,2BAA2B;AACxD,SAASC,SAAS,QAAQ,cAAc;AACxC,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,OAAOC,oBAAoB,MAAM,4DAA4D;AAC7F,OAAOC,oBAAoB,MAAM,4DAA4D;AAC7F,OAAOC,qBAAqB,MAAM,yBAAyB;AAX3D,IAAAC,WAAA,GAAe;EAAEpD,IAAI,EAAE;AAAsB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAY9C,IAAMqD,KAAK,GAAGC,OAkBZ;IACF,IAAMC,IAAI,GAAGC,MAAyB;IACtC,IAAMC,cAAc,GAAGjB,GAAG,CAAC,EAAE,CAAC;IAC9B,IAAMkB,OAAO,GAAGlB,GAAG,CAAC,CAAC;IACrB,IAAMmB,IAAI,GAAGpB,QAAQ,CAAC;MACpBqB,YAAY,EAAE,EAAE;MAAE;MAClBC,YAAY,EAAE,EAAE;MAChBC,cAAc,EAAE,EAAE;MAAE;MACpBC,gBAAgB,EAAE,EAAE;MAAE;MACtBC,aAAa,EAAE,EAAE;MAAE;MACnBC,YAAY,EAAE,EAAE;MAAE;MAClBC,kBAAkB,EAAE,EAAE;MACtBC,eAAe,EAAE;IACnB,CAAC,CAAC;IACF,IAAMC,KAAK,GAAG7B,QAAQ,CAAC;MACrBqB,YAAY,EAAE,CAAC;QAAES,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,SAAS;QAAEC,OAAO,EAAE,CAAC,MAAM,EAAE,QAAQ;MAAE,CAAC,CAAC;MACnFV,YAAY,EAAE,CAAC;QAAEQ,QAAQ,EAAE,KAAK;QAAEC,OAAO,EAAE,WAAW;QAAEC,OAAO,EAAE,CAAC,MAAM,EAAE,QAAQ;MAAE,CAAC,CAAC;MACtFT,cAAc,EAAE,CAAC;QAAEO,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,SAAS;QAAEC,OAAO,EAAE,CAAC,MAAM,EAAE,QAAQ;MAAE,CAAC,CAAC;MACrFL,kBAAkB,EAAE,CAAC;QAAExH,IAAI,EAAE,OAAO;QAAE2H,QAAQ,EAAE,KAAK;QAAEC,OAAO,EAAE,SAAS;QAAEC,OAAO,EAAE,CAAC,MAAM,EAAE,QAAQ;MAAE,CAAC,CAAC;MACzGJ,eAAe,EAAE,CAAC;QAAEzH,IAAI,EAAE,OAAO;QAAE2H,QAAQ,EAAE,KAAK;QAAEC,OAAO,EAAE,SAAS;QAAEC,OAAO,EAAE,CAAC,MAAM,EAAE,QAAQ;MAAE,CAAC;IACvG,CAAC,CAAC;IACF,IAAMC,UAAU,GAAGhC,GAAG,CAAC,CAAC,CAAC,CAAC;IAC1B,IAAMoB,YAAY,GAAGpB,GAAG,CAAC,EAAE,CAAC;IAC5B,IAAMsB,cAAc,GAAGtB,GAAG,CAAC,EAAE,CAAC;IAC9B,IAAMuB,gBAAgB,GAAGvB,GAAG,CAAC,EAAE,CAAC;IAChC,IAAMiC,sBAAsB,GAAGjC,GAAG,CAAC,EAAE,CAAC;IACtC,IAAMkC,UAAU,GAAGlC,GAAG,CAAC,EAAE,CAAC;IAE1B,IAAMmC,UAAU,GAAGnC,GAAG,CAAC,KAAK,CAAC;IAC7B,IAAMoC,eAAe,GAAGpC,GAAG,CAAC,KAAK,CAAC;IAClC,IAAMqC,UAAU,GAAGrC,GAAG,CAAC,KAAK,CAAC;IAC7B,IAAMsC,eAAe,GAAGtC,GAAG,CAAC,KAAK,CAAC;IAClC,IAAMuC,SAAS,GAAGvC,GAAG,CAAC,EAAE,CAAC;IACzB;IACAC,WAAW,CAAC,YAAM;MAChBkB,IAAI,CAACG,cAAc,GAAGT,KAAK,CAAC2B,UAAU;MACtCrB,IAAI,CAACI,gBAAgB,GAAGV,KAAK,CAAC4B,YAAY;MAC1CtB,IAAI,CAACK,aAAa,GAAGX,KAAK,CAAC6B,KAAK,IAAI,EAAE;MACtCvB,IAAI,CAACC,YAAY,GAAGP,KAAK,CAAC8B,MAAM,IAAI,EAAE;MACtC,IAAIxB,IAAI,CAACC,YAAY,IAAI,qBAAqB,EAAE;QAC9CH,cAAc,CAAClI,KAAK,GAAG,SAAS;MAClC;MACAoI,IAAI,CAACM,YAAY,GAAGZ,KAAK,CAAC+B,IAAI,IAAI,EAAE;MACpCzB,IAAI,CAACO,kBAAkB,GAAG,CAACb,KAAK,CAACgC,MAAM,CAAC,IAAI,EAAE;MAC9CC,OAAO,CAACC,GAAG,CAAC,eAAe,EAAElC,KAAK,CAACmC,IAAI,CAAC;MACxCC,UAAU,CAAC,YAAM;QACf9B,IAAI,CAACQ,eAAe,GAAG,CAACd,KAAK,CAACmC,IAAI,CAAC,IAAI,EAAE;MAC3C,CAAC,EAAE,IAAI,CAAC;MACR1C,YAAY,CAAC4C,cAAc,CAAC;QAAEC,UAAU,EAAE;MAAoB,CAAC,CAAC;MAChE7C,YAAY,CAAC4C,cAAc,CAAC;QAAEE,YAAY,EAAE;MAAK,CAAC,CAAC;MACnD9C,YAAY,CAAC4C,cAAc,CAAC;QAAEG,YAAY,EAAE;UAAEC,UAAU,EAAEzC,KAAK,CAAC0C;QAAG;MAAE,CAAC,CAAC;MACvEC,cAAc,CAAC,CAAC;MAChBC,qBAAqB,CAAC,CAAC;MACvB,IAAI5C,KAAK,CAAC0C,EAAE,EAAE;QACZG,mBAAmB,CAAC,CAAC;QACrBnB,SAAS,CAACxJ,KAAK,GAAG8H,KAAK,CAAC0C,EAAE;MAC5B;MACAI,MAAM,CAACC,gBAAgB,CAAC,cAAc,EAAE,UAACtL,CAAC,EAAK;QAAEuL,gBAAgB,CAAC,CAAC;MAAC,CAAC,CAAC;MACtE,IAAIhD,KAAK,CAACiD,MAAM,IAAI,GAAG,EAAE;QACvB,OAAOlC,KAAK,CAACR,YAAY;QACzB,OAAOQ,KAAK,CAACP,YAAY;MAC3B;IACF,CAAC,CAAC;IAEF,IAAM0C,YAAY,GAAG,SAAfA,YAAYA,CAAIC,UAAU,EAAEC,eAAe,EAAK;MACpD,IAAIpD,KAAK,CAACiD,MAAM,IAAI,GAAG,EAAE;QACvB3B,UAAU,CAACpJ,KAAK,GAAGiL,UAAU;QAC7B5B,eAAe,CAACrJ,KAAK,GAAGkL,eAAe;QACvC;MACF;IACF,CAAC;IACD,IAAMC,UAAU,GAAG,SAAbA,UAAUA,CAAIC,IAAI,EAAEZ,EAAE,EAAK;MAC/B,IAAIA,EAAE,EAAE;QACNpC,IAAI,CAACG,cAAc,GAAGiC,EAAE;QACxBa,oBAAoB,CAAC,CAAC;QACtBjD,IAAI,CAACI,gBAAgB,GAAG4C,IAAI,CAACE,GAAG;MAClC,CAAC,MAAM;QACLlD,IAAI,CAACG,cAAc,GAAG6C,IAAI,CAACE,GAAG;QAC9BD,oBAAoB,CAAC,CAAC;MACxB;IACF,CAAC;IACD,IAAME,YAAY,GAAG,SAAfA,YAAYA,CAAIN,UAAU,EAAEC,eAAe,EAAK;MACpD5B,UAAU,CAACtJ,KAAK,GAAGiL,UAAU;MAC7B1B,eAAe,CAACvJ,KAAK,GAAGkL,eAAe;IACzC,CAAC;IACD,IAAMM,UAAU,GAAG,SAAbA,UAAUA,CAAIJ,IAAI,EAAK;MAC3B,IAAIhD,IAAI,CAACM,YAAY,KAAK,aAAa,EAAE;QACvC,IAAI,CAACN,IAAI,CAACO,kBAAkB,CAACtE,MAAM,EAAE;UACnC,IAAI,CAAC+D,IAAI,CAACQ,eAAe,CAAC6C,QAAQ,CAACL,IAAI,CAACZ,EAAE,CAAC,EAAE;YAC3CpC,IAAI,CAACO,kBAAkB,GAAG,CAACyC,IAAI,CAACZ,EAAE,CAAC;YACnChD,SAAS,CAAC;cAAErG,IAAI,EAAE,SAAS;cAAE4H,OAAO,EAAE,QAAQqC,IAAI,CAAC3G,IAAI;YAAW,CAAC,CAAC;UACtE;QACF,CAAC,MAAM;UACL;UACA;UACA;UACA;QAAA;MAEJ,CAAC,MAAM,IAAI2D,IAAI,CAACM,YAAY,KAAK,SAAS,EAAE;QAC1C,IAAI,CAACN,IAAI,CAACQ,eAAe,CAAC6C,QAAQ,CAACL,IAAI,CAACZ,EAAE,CAAC,EAAE;UAC3CpC,IAAI,CAACQ,eAAe,MAAA8C,MAAA,CAAAtF,kBAAA,CAAOgC,IAAI,CAACQ,eAAe,IAAEwC,IAAI,CAACZ,EAAE,EAAC;UACzDhD,SAAS,CAAC;YAAErG,IAAI,EAAE,SAAS;YAAE4H,OAAO,EAAE,OAAOqC,IAAI,CAAC3G,IAAI;UAAU,CAAC,CAAC;QACpE;MACF;IACF,CAAC;IAED,IAAMqG,gBAAgB;MAAA,IAAAa,KAAA,GAAA5F,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAkH,QAAA;QAAA,IAAAC,GAAA,EAAAC,IAAA;QAAA,OAAAxM,mBAAA,GAAAuB,IAAA,UAAAkL,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAA7G,IAAA,GAAA6G,QAAA,CAAAxI,IAAA;YAAA;cAAA,KACnBsE,KAAK,CAACmE,MAAM;gBAAAD,QAAA,CAAAxI,IAAA;gBAAA;cAAA;cAAA,OAAAwI,QAAA,CAAA5I,MAAA;YAAA;cAAA4I,QAAA,CAAAxI,IAAA;cAAA,OACEuD,GAAG,CAAC+D,gBAAgB,CAAC;gBAAEoB,GAAG,EAAE,CAAC1C,SAAS,CAACxJ,KAAK;cAAE,CAAC,CAAC;YAAA;cAA5D6L,GAAG,GAAAG,QAAA,CAAA/I,IAAA;cACH6I,IAAI,GAAKD,GAAG,CAAZC,IAAI;cACV/B,OAAO,CAACC,GAAG,CAAC8B,IAAI,CAAC;YAAA;YAAA;cAAA,OAAAE,QAAA,CAAA1G,IAAA;UAAA;QAAA,GAAAsG,OAAA;MAAA,CAClB;MAAA,gBALKd,gBAAgBA,CAAA;QAAA,OAAAa,KAAA,CAAA1F,KAAA,OAAAD,SAAA;MAAA;IAAA,GAKrB;IACD,IAAM2E,mBAAmB;MAAA,IAAAwB,KAAA,GAAApG,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAA0H,SAAA;QAAA,IAAAP,GAAA,EAAAC,IAAA,EAAAO,KAAA,EAAAjB,IAAA;QAAA,OAAA9L,mBAAA,GAAAuB,IAAA,UAAAyL,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAApH,IAAA,GAAAoH,SAAA,CAAA/I,IAAA;YAAA;cAAA+I,SAAA,CAAA/I,IAAA;cAAA,OACRuD,GAAG,CAAC4D,mBAAmB,CAAC;gBAAE6B,YAAY,EAAE1E,KAAK,CAAC0C;cAAG,CAAC,CAAC;YAAA;cAA/DqB,GAAG,GAAAU,SAAA,CAAAtJ,IAAA;cACH6I,IAAI,GAAKD,GAAG,CAAZC,IAAI;cACV,IAAIhE,KAAK,CAACiD,MAAM,IAAI,GAAG,EAAE;gBACvB1C,YAAY,CAACrI,KAAK,GAAG,CACnB;kBACE,QAAQ,EAAE,qBAAqB;kBAC/B,UAAU,EAAE,IAAI;kBAChB,UAAU,EAAE;gBACd,CAAC,EACD;kBACE,QAAQ,EAAE,gBAAgB;kBAC1B,UAAU,EAAE,KAAK;kBACjB,UAAU,EAAE;gBACd,CAAC,EACD;kBACE,QAAQ,EAAE,gBAAgB;kBAC1B,UAAU,EAAE,OAAO;kBACnB,UAAU,EAAE;gBACd,CAAC,EACD;kBACE,QAAQ,EAAE,eAAe;kBACzB,UAAU,EAAE,MAAM;kBAClB,UAAU,EAAE;gBACd,CAAC,EACD;kBACE,QAAQ,EAAE,kBAAkB;kBAC5B,UAAU,EAAE,IAAI;kBAChB,UAAU,EAAE;gBACd,CAAC,EACD;kBACE,QAAQ,EAAE,cAAc;kBACxB,UAAU,EAAE,IAAI;kBAChB,UAAU,EAAE;gBACd,CAAC,CACF;cACH,CAAC,MAAM;gBACLqI,YAAY,CAACrI,KAAK,GAAG8L,IAAI;cAC3B;cACA,KAASO,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGP,IAAI,CAACzH,MAAM,EAAEgI,KAAK,EAAE,EAAE;gBAC1CjB,IAAI,GAAGU,IAAI,CAACO,KAAK,CAAC;gBACxB,IAAIjB,IAAI,CAACqB,QAAQ,KAAK,UAAU,EAAE;kBAChCtD,UAAU,CAACnJ,KAAK,GAAGoL,IAAI,CAACsB,QAAQ;gBAClC;cACF;YAAC;YAAA;cAAA,OAAAH,SAAA,CAAAjH,IAAA;UAAA;QAAA,GAAA8G,QAAA;MAAA,CACF;MAAA,gBA7CKzB,mBAAmBA,CAAA;QAAA,OAAAwB,KAAA,CAAAlG,KAAA,OAAAD,SAAA;MAAA;IAAA,GA6CxB;IACD,IAAM2G,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAA,EAAS;MAC/BzE,cAAc,CAAClI,KAAK,GAAG,EAAE;MACzB6I,KAAK,CAACP,YAAY,GAAG,CAAC;QAAEQ,QAAQ,EAAE,KAAK;QAAEC,OAAO,EAAE,MAAMI,UAAU,CAACnJ,KAAK,IAAI;QAAEgJ,OAAO,EAAE,CAAC,MAAM,EAAE,QAAQ;MAAE,CAAC,CAAC;MAC5G,KAAK,IAAIqD,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGhE,YAAY,CAACrI,KAAK,CAACqE,MAAM,EAAEgI,KAAK,EAAE,EAAE;QAC9D,IAAMjB,IAAI,GAAG/C,YAAY,CAACrI,KAAK,CAACqM,KAAK,CAAC;QACtC,IAAIjB,IAAI,CAACwB,MAAM,KAAKxE,IAAI,CAACC,YAAY,EAAE;UACrCH,cAAc,CAAClI,KAAK,GAAGoL,IAAI,CAACqB,QAAQ;UACpC,IAAIrB,IAAI,CAACqB,QAAQ,KAAK,UAAU,EAAE;YAChC5D,KAAK,CAACP,YAAY,GAAG,CAAC;cAAEQ,QAAQ,EAAE,IAAI;cAAEC,OAAO,EAAE,MAAMI,UAAU,CAACnJ,KAAK,IAAI;cAAEgJ,OAAO,EAAE,CAAC,MAAM,EAAE,QAAQ;YAAE,CAAC,CAAC;UAC7G;QACF;MACF;IACF,CAAC;IACD,IAAMyB,cAAc;MAAA,IAAAoC,KAAA,GAAA9G,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAoI,SAAA;QAAA,IAAAjB,GAAA,EAAAC,IAAA;QAAA,OAAAxM,mBAAA,GAAAuB,IAAA,UAAAkM,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA7H,IAAA,GAAA6H,SAAA,CAAAxJ,IAAA;YAAA;cAAAwJ,SAAA,CAAAxJ,IAAA;cAAA,OACHuD,GAAG,CAAC0D,cAAc,CAAC;gBAAEwC,SAAS,EAAE,CAAC,0BAA0B;cAAE,CAAC,CAAC;YAAA;cAA3EpB,GAAG,GAAAmB,SAAA,CAAA/J,IAAA;cACH6I,IAAI,GAAKD,GAAG,CAAZC,IAAI;cACV5C,sBAAsB,CAAClJ,KAAK,GAAG8L,IAAI,CAACoB,wBAAwB;YAAA;YAAA;cAAA,OAAAF,SAAA,CAAA1H,IAAA;UAAA;QAAA,GAAAwH,QAAA;MAAA,CAC7D;MAAA,gBAJKrC,cAAcA,CAAA;QAAA,OAAAoC,KAAA,CAAA5G,KAAA,OAAAD,SAAA;MAAA;IAAA,GAInB;IACD,IAAM0E,qBAAqB;MAAA,IAAAyC,KAAA,GAAApH,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAA0I,SAAA;QAAA,IAAAvB,GAAA,EAAAC,IAAA;QAAA,OAAAxM,mBAAA,GAAAuB,IAAA,UAAAwM,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAnI,IAAA,GAAAmI,SAAA,CAAA9J,IAAA;YAAA;cAAA8J,SAAA,CAAA9J,IAAA;cAAA,OACVuD,GAAG,CAAC2D,qBAAqB,CAAC;gBAAE6C,KAAK,EAAE;kBAAEC,OAAO,EAAE;gBAAE;cAAE,CAAC,CAAC;YAAA;cAAhE3B,GAAG,GAAAyB,SAAA,CAAArK,IAAA;cACH6I,IAAI,GAAKD,GAAG,CAAZC,IAAI;cACVvD,cAAc,CAACvI,KAAK,GAAG8L,IAAI;cAC3BT,oBAAoB,CAAC,CAAC;YAAA;YAAA;cAAA,OAAAiC,SAAA,CAAAhI,IAAA;UAAA;QAAA,GAAA8H,QAAA;MAAA,CACvB;MAAA,gBALK1C,qBAAqBA,CAAA;QAAA,OAAAyC,KAAA,CAAAlH,KAAA,OAAAD,SAAA;MAAA;IAAA,GAK1B;IACD,IAAMqF,oBAAoB,GAAG,SAAvBA,oBAAoBA,CAAA,EAAS;MACjC,IAAIjD,IAAI,CAACG,cAAc,EAAE;QACvB,KAAK,IAAI8D,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAG9D,cAAc,CAACvI,KAAK,CAACqE,MAAM,EAAEgI,KAAK,EAAE,EAAE;UAChE,IAAMjB,IAAI,GAAG7C,cAAc,CAACvI,KAAK,CAACqM,KAAK,CAAC;UACxC,IAAIjB,IAAI,CAACZ,EAAE,KAAKpC,IAAI,CAACG,cAAc,EAAE;YACnC,IAAI,CAAC6C,IAAI,CAACqC,QAAQ,CAACC,GAAG,CAAC,UAAA1L,CAAC;cAAA,OAAIA,CAAC,CAACwI,EAAE;YAAA,EAAC,CAACiB,QAAQ,CAACrD,IAAI,CAACI,gBAAgB,CAAC,EAAE;cACjEJ,IAAI,CAACI,gBAAgB,GAAG,EAAE;YAC5B;YACAA,gBAAgB,CAACxI,KAAK,GAAGoL,IAAI,CAACqC,QAAQ;UACxC;QACF;MACF,CAAC,MAAM;QACLrF,IAAI,CAACI,gBAAgB,GAAG,EAAE;QAC1BA,gBAAgB,CAACxI,KAAK,GAAG,EAAE;MAC7B;IACF,CAAC;IACD,IAAM2N,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAA,EAAS;MAC/B,IAAIvF,IAAI,CAACM,YAAY,KAAK,aAAa,EAAE;QACvCG,KAAK,CAACF,kBAAkB,GAAG,CAAC;UAAExH,IAAI,EAAE,OAAO;UAAE2H,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,SAAS;UAAEC,OAAO,EAAE,CAAC,MAAM,EAAE,QAAQ;QAAE,CAAC,CAAC;QAC/GH,KAAK,CAACD,eAAe,GAAG,CAAC;UAAEzH,IAAI,EAAE,OAAO;UAAE2H,QAAQ,EAAE,KAAK;UAAEC,OAAO,EAAE,SAAS;UAAEC,OAAO,EAAE,CAAC,MAAM,EAAE,QAAQ;QAAE,CAAC,CAAC;MAC/G,CAAC,MAAM,IAAIZ,IAAI,CAACM,YAAY,KAAK,SAAS,EAAE;QAC1CG,KAAK,CAACF,kBAAkB,GAAG,CAAC;UAAExH,IAAI,EAAE,OAAO;UAAE2H,QAAQ,EAAE,KAAK;UAAEC,OAAO,EAAE,SAAS;UAAEC,OAAO,EAAE,CAAC,MAAM,EAAE,QAAQ;QAAE,CAAC,CAAC;QAChHH,KAAK,CAACD,eAAe,GAAG,CAAC;UAAEzH,IAAI,EAAE,OAAO;UAAE2H,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,SAAS;UAAEC,OAAO,EAAE,CAAC,MAAM,EAAE,QAAQ;QAAE,CAAC,CAAC;MAC9G,CAAC,MAAM;QACLH,KAAK,CAACF,kBAAkB,GAAG,CAAC;UAAExH,IAAI,EAAE,OAAO;UAAE2H,QAAQ,EAAE,KAAK;UAAEC,OAAO,EAAE,SAAS;UAAEC,OAAO,EAAE,CAAC,MAAM,EAAE,QAAQ;QAAE,CAAC,CAAC;QAChHH,KAAK,CAACD,eAAe,GAAG,CAAC;UAAEzH,IAAI,EAAE,OAAO;UAAE2H,QAAQ,EAAE,KAAK;UAAEC,OAAO,EAAE,SAAS;UAAEC,OAAO,EAAE,CAAC,MAAM,EAAE,QAAQ;QAAE,CAAC,CAAC;MAC/G;IACF,CAAC;IACD,IAAM4E,UAAU;MAAA,IAAAC,KAAA,GAAA9H,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAoJ,SAAOC,MAAM;QAAA,OAAAzO,mBAAA,GAAAuB,IAAA,UAAAmN,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA9I,IAAA,GAAA8I,SAAA,CAAAzK,IAAA;YAAA;cAAA,IACzBuK,MAAM;gBAAAE,SAAA,CAAAzK,IAAA;gBAAA;cAAA;cAAA,OAAAyK,SAAA,CAAA7K,MAAA;YAAA;cAAA6K,SAAA,CAAAzK,IAAA;cAAA,OACLuK,MAAM,CAACG,QAAQ,CAAC,UAACC,KAAK,EAAEC,MAAM,EAAK;gBACvC,IAAID,KAAK,EAAE;kBAAEnG,IAAI,CAAC,cAAc,EAAEqG,kBAAkB,EAAEjG,IAAI,CAAC;gBAAC,CAAC,MAAM;kBAAEZ,SAAS,CAAC;oBAAErG,IAAI,EAAE,SAAS;oBAAE4H,OAAO,EAAE;kBAAiB,CAAC,CAAC;gBAAC;cACjI,CAAC,CAAC;YAAA;YAAA;cAAA,OAAAkF,SAAA,CAAA3I,IAAA;UAAA;QAAA,GAAAwI,QAAA;MAAA,CACH;MAAA,gBALKF,UAAUA,CAAAU,EAAA;QAAA,OAAAT,KAAA,CAAA5H,KAAA,OAAAD,SAAA;MAAA;IAAA,GAKf;IAED,IAAMqI,kBAAkB;MAAA,IAAAE,KAAA,GAAAxI,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAA8J,SAAA;QAAA,IAAAC,qBAAA,EAAAC,IAAA;QAAA,OAAApP,mBAAA,GAAAuB,IAAA,UAAA8N,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAzJ,IAAA,GAAAyJ,SAAA,CAAApL,IAAA;YAAA;cAAAoL,SAAA,CAAApL,IAAA;cAAA,OACFuD,GAAG,CAACsH,kBAAkB,CAAC;gBAC5C7B,YAAY,EAAE1E,KAAK,CAAC0C,EAAE;gBACtBqE,UAAU,EAAE/G,KAAK,CAACiD,MAAM,IAAI,GAAG,GAAG,QAAQ,GAAGjD,KAAK,CAACiD,MAAM,IAAI,GAAG,GAAG,eAAe,GAAG3C,IAAI,CAACC,YAAY;gBAAE;gBACxGoB,UAAU,EAAErB,IAAI,CAACG,cAAc;gBAAE;gBACjCmB,YAAY,EAAEtB,IAAI,CAACI,gBAAgB;gBAAE;gBACrCsG,QAAQ,EAAE;kBACRC,aAAa,EAAE3G,IAAI,CAACK,aAAa;kBAAE;kBACnCuG,SAAS,EAAE9G,cAAc,CAAClI,KAAK,KAAK,UAAU,GAAGoI,IAAI,CAACE,YAAY,GAAG;gBACvE,CAAC;gBACD2G,gBAAgB,EAAE7G,IAAI,CAACM,YAAY;gBAAE;gBACrCC,kBAAkB,EAAEP,IAAI,CAACO,kBAAkB,CAACuG,IAAI,CAAC,EAAE,CAAC;gBAAE;gBACtDtG,eAAe,EAAER,IAAI,CAACQ,eAAe,CAACvE,MAAM,GAAG,CAAC,IAAI+D,IAAI,CAACQ,eAAe,CAAC,CAAC,CAAC,GAAGR,IAAI,CAACQ,eAAe,GAAG,EAAE,CAAC;cAC1G,CAAC,CAAC;YAAA;cAAA6F,qBAAA,GAAAG,SAAA,CAAA3L,IAAA;cAZMyL,IAAI,GAAAD,qBAAA,CAAJC,IAAI;cAaZ,IAAIA,IAAI,KAAK,GAAG,EAAE;gBAChBlH,SAAS,CAAC;kBAAErG,IAAI,EAAE,SAAS;kBAAE4H,OAAO,EAAE;gBAAO,CAAC,CAAC;gBAC/Cf,IAAI,CAAC,UAAU,CAAC;cAClB;YAAC;YAAA;cAAA,OAAA4G,SAAA,CAAAtJ,IAAA;UAAA;QAAA,GAAAkJ,QAAA;MAAA,CACF;MAAA,gBAlBKH,kBAAkBA,CAAA;QAAA,OAAAE,KAAA,CAAAtI,KAAA,OAAAD,SAAA;MAAA;IAAA,GAkBvB;IACD,IAAMmJ,SAAS,GAAG,SAAZA,SAASA,CAAA,EAAS;MAAEnH,IAAI,CAAC,UAAU,CAAC;IAAC,CAAC;IAE5Cb,aAAa,CAAC,YAAM;MAClBI,YAAY,CAAC4C,cAAc,CAAC;QAAEE,YAAY,EAAE;MAAM,CAAC,CAAC;MACpD9C,YAAY,CAAC4C,cAAc,CAAC;QAAEC,UAAU,EAAE;MAAY,CAAC,CAAC;MACxD7C,YAAY,CAAC4C,cAAc,CAAC;QAAEG,YAAY,EAAE,CAAC;MAAE,CAAC,CAAC;MACjD/C,YAAY,CAAC4C,cAAc,CAAC;QAAEiF,aAAa,EAAE;MAAG,CAAC,CAAC;MAClD;MACA;MACAtE,gBAAgB,CAAC,CAAC;IACpB,CAAC,CAAC;IACF1D,eAAe,CAAC,YAAM;MACpBG,YAAY,CAAC4C,cAAc,CAAC;QAAEE,YAAY,EAAE;MAAM,CAAC,CAAC;MACpD9C,YAAY,CAAC4C,cAAc,CAAC;QAAEC,UAAU,EAAE;MAAY,CAAC,CAAC;MACxD7C,YAAY,CAAC4C,cAAc,CAAC;QAAEG,YAAY,EAAE,CAAC;MAAE,CAAC,CAAC;MACjD/C,YAAY,CAAC4C,cAAc,CAAC;QAAEiF,aAAa,EAAE;MAAG,CAAC,CAAC;MAClD;MACA;MACA;IACF,CAAC,CAAC;IACF/H,KAAK,CAAC;MAAA,OAAM,CAACS,KAAK,CAACS,cAAc,EAAET,KAAK,CAACU,gBAAgB,CAAC;IAAA,GAAE,YAAM;MAChEJ,IAAI,CAACG,cAAc,GAAGT,KAAK,CAACS,cAAc;MAC1CH,IAAI,CAACI,gBAAgB,GAAGV,KAAK,CAACU,gBAAgB;MAC9C6C,oBAAoB,CAAC,CAAC;IACxB,CAAC,EAAE;MAAEgE,SAAS,EAAE;IAAK,CAAC,CAAC;IACvBhI,KAAK,CAAC;MAAA,OAAM,CAACS,KAAK,CAACwH,mBAAmB,EAAExH,KAAK,CAACyH,OAAO,CAAC;IAAA,GAAE,YAAM;MAC5D;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAhI,YAAY,CAAC4C,cAAc,CAAC;QAAEiF,aAAa,EAAEtH,KAAK,CAACyH;MAAQ,CAAC,CAAC;MAC7D,IAAMC,UAAU,GAAG,CAAA1H,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEwH,mBAAmB,KAAI,EAAE;MACnDrG,UAAU,CAACjJ,KAAK,GAAG;QAAEwP,UAAU,EAAEC,IAAI,CAACC,SAAS,CAACF,UAAU,CAAC;QAAED,OAAO,EAAEzH,KAAK,CAACyH;MAAQ,CAAC;IACvF,CAAC,EAAE;MAAEF,SAAS,EAAE;IAAK,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}