<template>
  <div class="SuggestStatistics">
    <xyl-statistics-label v-model="labelId" @change="handleLabel">
      <xyl-statistics-label-item v-for="item in labelList" :key="item.key" :value="item.key">
        {{ item.name }}
      </xyl-statistics-label-item>
    </xyl-statistics-label>
    <el-scrollbar class="SuggestStatisticsBody">
      <div class="SuggestStatisticsChart" v-show="chartTypeObj[labelId] !== 'no'">
        <div class="SuggestStatisticsChartType">
          <div class="SuggestStatisticsChartTypeItem" :class="{ 'is-active': isActive === '1' }"
            @click="handleChartType('1')">
            默认
          </div>
          <div class="SuggestStatisticsChartTypeItem" :class="{ 'is-active': isActive === '2' }"
            @click="handleChartType('2')">
            饼状
          </div>
          <div class="SuggestStatisticsChartTypeItem" :class="{ 'is-active': isActive === '3' }"
            @click="handleChartType('3')">
            柱状
          </div>
        </div>
        <div v-if="!chartData.length" class="SuggestStatisticsChartEmpty">
          <el-empty :image-size="100" />
        </div>
        <PieChart v-show="(isActive === '2' || (isActive === '1' && chartTypeObj[labelId] === '1')) && chartData.length"
          :data="chartData"></PieChart>
        <ColumnChart
          v-show="(isActive === '3' || (isActive === '1' && chartTypeObj[labelId] === '2')) && chartData.length"
          :data="chartData"></ColumnChart>
      </div>
      <div class="globalLayout" :class="{ 'is-active': chartTypeObj[labelId] === 'no' }">
        <xyl-search-button @queryClick="handleQuery" @resetClick="handleReset" @handleButton="handleButton"
          :buttonList="buttonList">
          <template #search>
            <el-input v-model="keyword" placeholder="请输入关键词" @keyup.enter="handleQuery" clearable />
            <el-select v-model="termYearId" placeholder="请选择届次" multiple collapse-tags clearable>
              <el-option v-for="item in termYearData" :key="item.key" :label="item.name" :value="item.key" />
            </el-select>
            <el-select v-model="zhuxieban" v-if="labelId == 'office_answer_type'" placeholder="请选择办理方式" clearable>
              <el-option label="主办" value="1" />
              <!-- <el-option label="协办" value="2" /> -->
            </el-select>
            <el-select v-model="suggestMeetingType" placeholder="请选择提案类型" clearable>
              <el-option v-for="item in suggestionMeetingTypeData" :key="item.id" :label="item.name" :value="item.id" />
            </el-select>
            <el-select v-model="suggestStutas"
              v-if="['delegation', 'npc_member', 'big_theme', 'small_theme', 'about_some'].includes(labelId)"
              placeholder="请选择提案状态" collapse-tags multiple clearable>
              <el-option v-for="item in suggestStutasTypeData" :key="item.id" :label="item.label" :value="item.id" />
            </el-select>

            <!-- <el-checkbox v-model="isReceive"
                         @change="handleQuery">立案提案</el-checkbox> -->
          </template>
        </xyl-search-button>
        <div class="globalTable">
          <el-table border ref="tableRef" :data="tableData" :summary-method="getSummaries" show-summary
            @sort-change="handleSortChange">
            <TableTree v-for="item in tableHead" :tableColumn="item" @change="handleChange" @details="handleDetails"
              :key="item.headCode + labelId"></TableTree>
          </el-table>
        </div>
      </div>
    </el-scrollbar>
  </div>
</template>
<script>
export default { name: 'SuggestStatistics' }
</script>
<script setup>
import api from '@/api'
import { ref, onActivated, nextTick } from 'vue'
import { GlobalExportExcel } from 'common/Excel/GlobalExportExcel'
import { qiankunMicro } from 'common/config/MicroGlobal'
import { ElMessage } from 'element-plus'
import TableTree from './TableTree.vue'
import PieChart from './PieChart.vue'
import ColumnChart from './ColumnChart.vue'
import { useRoute } from 'vue-router'
const route = useRoute()
const { exportExcelTree } = GlobalExportExcel()
const buttonList = [{ id: 'export', name: '导出Excel', type: 'primary', has: '' }]
const labelId = ref('')
const labelObj = ref({})
const labelList = ref([])
const chartTypeObj = ref({})
const isActive = ref('1')
const keyword = ref('')
const termYearId = ref([])
const termYearData = ref([])
const isReceive = ref(false)
const tableRef = ref()
const tableHead = ref([])
const tableData = ref([])
const chartData = ref([])
const sortData = ref({})
const zhuxieban = ref('')
const suggestStutas = ref([])
const suggestStutasTypeData = ref([])
const suggestMeetingType = ref('all')
const suggestionMeetingTypeData = ref([])

onActivated(() => {
  termYearSelect()
  getsuggestStutas()
  suggestionMeetingType()
})
const getsuggestStutas = async () => {
  const { data } = await api.globalJson('/customColumn/selector/id_prop_proposal_current_node_id')
  suggestStutasTypeData.value = data
}
const suggestionMeetingType = async () => {
  const { data } = await api.suggestionMeetingType()
  suggestionMeetingTypeData.value = data.map((v) => ({ ...v, id: v.id ? v.id : 'all' }))
}

// 获取当前届次
const termYearCurrent = async () => {
  const { data } = await api.termYearCurrent({ termYearType: 'cppcc_member' })
  if (!termYearId.value.length) {
    termYearId.value = [data.id]
  }
  dictionaryData()
}
const termYearSelect = async () => {
  const { data } = await api.termYearSelect({ termYearType: 'cppcc_member' })
  termYearData.value = data
  termYearCurrent()
}
const dictionaryData = async () => {
  const { data } = await api.dictionaryData({ dictCodes: ['proposal_statistics'] })
  chartTypeObj.value = {}
  labelObj.value = {}
  labelList.value = []
  if (data?.proposal_statistics?.length) {
    for (let index = 0; index < data?.proposal_statistics?.length || 0; index++) {
      const item = data.proposal_statistics[index]
      const itemArr = item.id.split('=')
      labelObj.value[itemArr[0]] = item.name
      chartTypeObj.value[itemArr[0]] = itemArr[1] || 'no'
      labelList.value.push({ key: itemArr[0], type: itemArr[1] || 'no', name: item.name })
      if (!labelId.value && !index) {
        labelId.value = itemArr[0] || ''
      }
    }
    if (route.query.labelId) {
      labelId.value = route.query.labelId
    }
    suggestionStatistics()
  } else {
    labelId.value = ''
  }
}
const handleLabel = () => {
  tableHead.value = []
  tableData.value = []
  tableRef.value?.doLayout()
  tableRef.value?.clearSort()
  nextTick(() => {
    suggestionStatistics()
  })
}
const getSummaries = (param) => {
  const { columns, data } = param
  const sums = []
  const showarr = [
    'handle_office',
    'office_handle_status',
    'office_answer_type',
    'office_handle_info',
    'group_contact_num',
    'group_answer_open'
  ]
  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = '合计 ' + (showarr.includes(labelId.value) ? `(${data.length})` : '')
      return
    }
    const values = data.map((item) => {
      const keys = column.property.split('.')
      return keys.length === 1 ? Number(item[column.property]) : item[keys[0]]?.amount
    })
    if (!values.every((value) => Number.isNaN(value))) {
      sums[index] = values.reduce((prev, curr) => {
        const value = Number(curr)
        if (!Number.isNaN(value)) {
          return prev + curr
        } else {
          return prev
        }
      }, 0)
    } else {
      sums[index] = ''
    }
  })
  return sums
}
const handleQuery = () => {
  suggestionStatistics()
}
const handleReset = () => {
  keyword.value = ''
  termYearId.value = []
  suggestStutas.value = []
  suggestMeetingType.value = 'all'
  termYearCurrent()
}
const handleSortChange = ({ prop, order }) => {
  sortData.value = { prop, order }
}
const handleButton = (isType) => {
  switch (isType) {
    case 'export':
      if (!tableData.value.length) return ElMessage({ type: 'info', message: '暂无统计数据可导出！' })
      handleExportExcel()
      break
    default:
      break
  }
}
const handleExportExcel = () => {
  let excelData = []
  const newTableData = []
  for (let index = 0; index < tableData.value.length; index++) {
    const item = tableData.value[index]
    newTableData.push(filterTableData(item))
  }
  const key = sortData.value.prop.split('.')[0]
  if (sortData.value.order === 'descending') {
    excelData = newTableData.sort((a, b) => b[key] - a[key])
  } else if (sortData.value.order === 'ascending') {
    excelData = newTableData.sort((a, b) => a[key] - b[key])
  } else {
    excelData = newTableData
  }
  exportExcelTree(filtertableHead(tableHead.value), excelData, `${labelObj.value[labelId.value]}统计`)
}
const suggestionStatistics = async () => {
  console.log(termYearId.value)
  const { data } = await api.suggestionStatistics({
    countView: labelId.value,
    // keyword: keyword.value,
    termYearId: termYearId.value.join(','),
    isReceive: isReceive.value ? 1 : 0,
    zhuxieban: zhuxieban.value,
    query: {
      suggestMeetingType: suggestMeetingType.value === 'all' ? null : suggestMeetingType.value
    },
    currentNodeIds: suggestStutas.value
  })
  const filterKey = data.tableHeader[0]?.headCode || ''
  const getTableData = keyword.value
    ? data.tableData?.filter((v) => (filterKey ? v[filterKey].includes(keyword.value) : true))
    : data.tableData
  tableHead.value = data.tableHeader
  tableData.value = getTableData
  nextTick(() => {
    tableRef.value?.doLayout()
    nextTick(() => {
      for (let index = 0; index < data.tableHeader.length; index++) {
        const item = data.tableHeader[index]
        if (index === 1) {
          tableRef.value?.sort(`${item.headCode}.amount`, 'descending')
        }
      }
    })
  })
  chartData.value = []
  if (chartTypeObj.value[labelId.value] !== 'no') {
    for (let index = 0; index < getTableData.length; index++) {
      const item = getTableData[index]
      var chartObj = {}
      for (let i = 0; i < data.tableHeader.length; i++) {
        const row = data.tableHeader[i]
        if (row.showType) {
          if (row.showType === 'link') {
            chartObj.value = item[row.headCode]?.amount
          }
        } else {
          chartObj.name = item[row.headCode]
        }
      }
      chartData.value.push(chartObj)
    }
  }
}
const whetherDataType = (obj) => {
  var toString = Object.prototype.toString
  var map = {
    '[object Boolean]': 'boolean',
    '[object Number]': 'number',
    '[object String]': 'string',
    '[object Function]': 'function',
    '[object Array]': 'array',
    '[object Date]': 'date',
    '[object RegExp]': 'regExp',
    '[object Undefined]': 'undefined',
    '[object Null]': 'null',
    '[object Object]': 'object'
  }
  return map[toString.call(obj)]
}
const filterTableData = (row) => {
  var rowObj = {}
  for (let key in row) {
    const type = whetherDataType(row[key])
    if (type === 'array') {
      rowObj[key] = row[key].map((v) => v.serialNumber).join('、')
    } else if (type === 'object') {
      rowObj[key] = row[key]?.amount
    } else {
      rowObj[key] = row[key]
    }
  }
  return rowObj
}
const filtertableHead = (data) =>
  data.map((v) => ({ id: v.headCode, key: v.headCode, label: v.headName, children: filtertableHead(v.children || []) }))
const handleChartType = (type) => {
  isActive.value = type
}
const handleChange = (item, column) => {
  qiankunMicro.setGlobalState({
    openRoute: {
      name: '提案统计列表',
      path: '/proposal/SuggestStatisticsList',
      query: { ids: JSON.stringify(item[column.headCode]?.proposalIds) }
    }
  })
}
const handleDetails = (item, row) => {
  qiankunMicro.setGlobalState({
    openRoute: { name: '提案详情', path: '/proposal/SuggestDetail', query: { id: row.proposalId } }
  })
}
</script>
<style lang="scss">
.SuggestStatistics {
  width: 100%;
  height: 100%;
  background: var(--zy-el-color-info-light-9);
  display: flex;
  justify-content: space-between;

  .SuggestStatisticsBody {
    width: calc(100% - 190px);
    height: 100%;

    .zy-el-scrollbar__view {
      height: 100%;
    }

    .SuggestStatisticsChart {
      width: 100%;
      height: 300px;
      margin-bottom: 10px;
      background-color: #fff;

      .SuggestStatisticsChartType {
        display: flex;
        align-items: center;
        width: 100%;
        height: var(--zy-height);

        .SuggestStatisticsChartTypeItem {
          line-height: var(--zy-line-height);
          font-size: var(--zy-text-font-size);
          color: var(--zy-el-text-color-regular);
          padding-left: 20px;
          margin-left: 20px;
          position: relative;
          cursor: pointer;

          &::after {
            content: '';
            position: absolute;
            width: 20px;
            height: 20px;
            top: 50%;
            left: 0;
            transform: translateY(-50%);
          }

          &:nth-child(1) {
            &::after {
              content: '';
              position: absolute;
              width: 20px;
              height: 20px;
              top: 50%;
              left: 0;
              transform: translateY(-50%);
              background: url('../../assets/img/default_chart.png') no-repeat;
              background-color: var(--zy-el-text-color-regular);
              background-size: 100% 100%;
            }
          }

          &:nth-child(2) {
            &::after {
              content: '';
              position: absolute;
              width: 20px;
              height: 20px;
              top: 50%;
              left: 0;
              transform: translateY(-50%);
              background: url('../../assets/img/pie_chart.png') no-repeat;
              background-color: var(--zy-el-text-color-regular);
              background-size: 100% 100%;
            }
          }

          &:nth-child(3) {
            &::after {
              content: '';
              position: absolute;
              width: 20px;
              height: 20px;
              top: 50%;
              left: 0;
              transform: translateY(-50%);
              background: url('../../assets/img/column_chart.png') no-repeat;
              background-color: var(--zy-el-text-color-regular);
              background-size: 100% 100%;
            }
          }

          &.is-active {
            color: var(--zy-el-color-primary);

            &::after {
              background-color: var(--zy-el-color-primary);
            }
          }
        }
      }

      .SuggestStatisticsChartEmpty {
        width: 100%;
        height: calc(100% - var(--zy-height));

        .zy-el-empty {
          height: 100%;
        }
      }

      .PieChart {
        height: calc(100% - var(--zy-height));
      }

      .ColumnChart {
        height: calc(100% - var(--zy-height));
      }
    }

    .globalLayout {
      width: 100%;
      height: calc(100% - 310px);
      min-height: 500px;
      padding: 0 20px;
      background-color: #fff;

      .xyl-search-button {
        .xyl-button {
          width: calc(100% - 880px);
        }

        .xyl-search {
          width: 880px;

          .zy-el-select {
            margin-left: 20px;
          }

          .zy-el-checkbox {
            margin-left: 20px;
          }
        }
      }

      .globalTable {
        width: 100%;
        height: calc(100% - (var(--zy-height) + (var(--zy-distance-four) * 2)));

        .zy-el-table__cell {
          border-right: var(--zy-el-table-border) !important;
          border-bottom: var(--zy-el-table-border) !important;
        }

        .SuggestStatisticsLinkList {
          margin-right: var(--zy-distance-two);
        }
      }
    }

    .globalLayout.is-active {
      height: 100%;
    }
  }
}
</style>
