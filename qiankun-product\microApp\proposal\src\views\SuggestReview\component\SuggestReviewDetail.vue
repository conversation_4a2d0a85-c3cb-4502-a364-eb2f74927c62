<template>
  <div class="SuggestReviewDetail">
    <div class="SuggestReviewDetailNameBody">
      <div class="SuggestReviewDetailName">
        <div>{{ props.name }}</div>
      </div>
    </div>
    <el-form ref="formRef" :model="form" :rules="rules" inline label-position="top" class="globalForm">
      <el-form-item label="审查结果" prop="reviewResult" class="globalFormTitle" v-show="props.signId != '1'">
        <el-radio-group v-model="form.reviewResult" @change="reviewResultChange">
          <el-radio v-for="item in reviewResult" :disabled="props.isLock" :key="item.nodeId" :label="item.nodeId">{{
            item.nodeName }}</el-radio>
        </el-radio-group>
        <template v-if="whetherUseIntelligentize">
          <intelligent-assistant v-model:elIsShow="elTypeShow" v-model="visibleTypeShow">
            <SuggestRecommendType :id="props.id" :content="props.content" @callback="typeCallback" @select="typeSelect">
            </SuggestRecommendType>
          </intelligent-assistant>
        </template>
      </el-form-item>
      <el-form-item :label="`${rejectName}理由`"
        v-show="isReviewResult === 'noAccept' && props.signId != '1' && props.signId != '2'" prop="rejectReason"
        class="globalFormTitle">
        <el-select v-model="form.rejectReason" :placeholder="`请选择${rejectName}理由`" clearable>
          <el-option v-for="item in suggestionRejectReason" :key="item.key" :label="item.name" :value="item.key" />
        </el-select>
      </el-form-item>
      <el-form-item label="提案大类" prop="SuggestBigType">
        <el-select v-model="form.SuggestBigType" placeholder="请选择提案大类" @change="SuggestBigTypeChange"
          :disabled="props.isLock" clearable>
          <el-option v-for="item in SuggestBigType" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="提案小类">
        <el-select v-model="form.SuggestSmallType" placeholder="请选择提案小类" :disabled="props.isLock" clearable>
          <el-option v-for="item in SuggestSmallType" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="审查意见" class="globalFormTitle" v-if="props.signId != '1'">
        <el-input v-model="form.reviewOpinion" placeholder="请输入审查意见" :disabled="props.isLock" type="textarea" :rows="5"
          clearable />
      </el-form-item>
      <template v-if="isReviewResult === 'success' && props.signId != '1'">
        <el-form-item label=" 办理方式">
          <el-select v-model="form.transactType" placeholder="请选择办理方式" @change="transactTypeChange" clearable>
            <el-option label="主办" value="main_assist" />
            <el-option label="分办" value="publish" />
          </el-select>
        </el-form-item>
      </template>
      <template v-if="isReviewResult === 'success' && form.transactType === 'main_assist' && props.signId != '1'">
        <el-form-item label="主办单位" prop="mainHandleOfficeId" class="globalFormTitle">
          <suggest-simple-select-unit v-model="form.mainHandleOfficeId" :filterId="form.handleOfficeIds"
            :max="1"></suggest-simple-select-unit>
          <template v-if="whetherUseIntelligentize">
            <intelligent-assistant v-model:elIsShow="elUnitShow" v-model="visibleUnitShow">
              <SuggestRecommendUnit :params="unitParams" @callback="unitCallback" @select="unitSelect">
              </SuggestRecommendUnit>
            </intelligent-assistant>
          </template>
        </el-form-item>
      </template>
      <!-- <template v-if="isReviewResult === 'success' && form.transactType === 'main_assist' && props.signId != '1'">
        <el-form-item label="协办单位" class="globalFormTitle">
          <suggest-simple-select-unit v-model="form.handleOfficeIds"
            :filterId="form.mainHandleOfficeId"></suggest-simple-select-unit>
        </el-form-item>
      </template> -->
      <template v-if="isReviewResult === 'success' && form.transactType === 'publish' && props.signId != '1'">
        <el-form-item label="分办单位" prop="handleOfficeIds" class="globalFormTitle">
          <suggest-simple-select-unit v-model="form.handleOfficeIds"></suggest-simple-select-unit>
          <template v-if="whetherUseIntelligentize">
            <intelligent-assistant v-model:elIsShow="elUnitShow" v-model="visibleUnitShow">
              <SuggestRecommendUnit :params="unitParams" @callback="unitCallback" @select="unitSelect">
              </SuggestRecommendUnit>
            </intelligent-assistant>
          </template>
        </el-form-item>
      </template>
      <ReviewSimilarityQuery :id="props.id" :content="props.content" @callback="resetForm"></ReviewSimilarityQuery>
      <div class="globalFormButton">
        <el-button type="primary" :disabled="props.isLock" @click="submitForm(formRef)">提交</el-button>
        <el-button @click="resetForm">取消</el-button>
      </div>
      <div class="globalFormButton" style="margin-top: 10px;" v-if="props.isLock">
        <div> 提案已被锁定（由{{ props.lockVo.lockUserName }}于{{ format(props.lockVo.lockDate) }}） 锁定 </div>
      </div>
    </el-form>
  </div>
</template>
<script>
export default { name: 'SuggestReviewDetail' }
</script>
<script setup>
import api from '@/api'
import { reactive, ref, onActivated, onDeactivated, onBeforeUnmount, watch } from 'vue'
import { whetherUseIntelligentize } from 'common/js/system_var.js'
import { qiankunMicro } from 'common/config/MicroGlobal'
import { ElMessage } from 'element-plus'
import { format } from 'common/js/time.js'
import SuggestRecommendType from '@/components/SuggestRecommendType/SuggestRecommendType.vue'
import SuggestRecommendUnit from '@/components/SuggestRecommendUnit/SuggestRecommendUnit.vue'
import ReviewSimilarityQuery from './ReviewSimilarityQuery'
const props = defineProps({
  id: { type: String, default: '' },
  name: { type: String, default: '提案审查' },
  content: { type: String, default: '' },
  SuggestBigType: { type: String, default: '' },
  SuggestSmallType: { type: String, default: '' },
  isLock: { type: Boolean, default: false },
  hopeHandleOfficeIds: { type: Array, default: () => ([]) },
  lockVo: { type: Object, default: () => ({}) },
  queryType: { type: String, default: '' },
  signId: { type: String, default: '' },
  bigThemeId: { type: String, default: '' },
  smallThemeId: { type: String, default: '' },
  jordan: { type: String, default: '' },
  james: { type: String, default: '' },
  kobe: { type: String, default: '' },
  duncan: { type: String, default: '' },
  wade: { type: String, default: '' }
})
const emit = defineEmits(['callback'])
const isReviewResult = ref('')
const formRef = ref()
const form = reactive({
  reviewResult: '', // 审查结果
  rejectReason: '',
  SuggestBigType: '', // 提案大类
  SuggestSmallType: '', // 提案小类
  reviewOpinion: '', // 审查意见
  transactType: '', // 请选择办理方式
  mainHandleOfficeId: [],
  handleOfficeIds: []
})
const rules = reactive({
  reviewResult: [{ required: true, message: '请选择审查结果', trigger: ['blur', 'change'] }],
  rejectReason: [{ required: false, message: '请选择不予接收理由', trigger: ['blur', 'change'] }],
  SuggestBigType: [{ required: true, message: '请选择提案大类', trigger: ['blur', 'change'] }],
  mainHandleOfficeId: [{ type: 'array', required: false, message: '请选择主办单位', trigger: ['blur', 'change'] }],
  handleOfficeIds: [{ type: 'array', required: false, message: '请选择分办单位', trigger: ['blur', 'change'] }]
})
const unitParams = ref({})
const reviewResult = ref([])
const SuggestBigType = ref([])
const SuggestSmallType = ref([])
const suggestionRejectReason = ref([])
const rejectName = ref('')

const elTypeShow = ref(false)
const visibleTypeShow = ref(false)
const elUnitShow = ref(false)
const visibleUnitShow = ref(false)
const suggestId = ref('')
// const elAiChatClass = AiChatClass()
onActivated(() => {
  form.SuggestBigType = props.bigThemeId
  form.SuggestSmallType = props.smallThemeId
  form.reviewOpinion = props.james || ''
  form.reviewResult = props.jordan || ''
  if (form.reviewResult == 'prepareSubmitHandle') {
    isReviewResult.value = 'success'
  }
  form.transactType = props.kobe || ''
  form.mainHandleOfficeId = [props.duncan] || ''
  console.log('props.wade==>', props.wade)
  setTimeout(() => {
    form.handleOfficeIds = [props.wade] || ''
  }, 2000);
  qiankunMicro.setGlobalState({ AiChatCode: 'ai-assistant-chat' })
  qiankunMicro.setGlobalState({ AiChatWindow: true })
  qiankunMicro.setGlobalState({ AiChatParams: { businessId: props.id } })
  dictionaryData()
  suggestionThemeSelect()
  if (props.id) {
    suggestionNextNodes()
    suggestId.value = props.id
  }
  window.addEventListener('beforeunload', (e) => { suggestionUnlock() })
  if (props.signId == '1') {
    delete rules.reviewResult
    delete rules.rejectReason
  }
})

const typeCallback = (isElIsShow, isVisibleIsShow) => {
  if (props.signId != '1') {
    elTypeShow.value = isElIsShow
    visibleTypeShow.value = isVisibleIsShow
    return
  }
}
const typeSelect = (item, id) => {
  if (id) {
    form.SuggestBigType = id
    SuggestBigTypeChange()
    form.SuggestSmallType = item._id
  } else {
    form.SuggestBigType = item._id
    SuggestBigTypeChange()
  }
}
const unitCallback = (isElIsShow, isVisibleIsShow) => {
  elUnitShow.value = isElIsShow
  visibleUnitShow.value = isVisibleIsShow
}
const unitSelect = (item) => {
  if (form.transactType === 'main_assist') {
    if (!form.mainHandleOfficeId.length) {
      if (!form.handleOfficeIds.includes(item.id)) {
        form.mainHandleOfficeId = [item.id]
        ElMessage({ type: 'success', message: `已为您将【${item.name}】添加到主办单位` })
      }
    } else {
      // if (!form.mainHandleOfficeId.includes(item.id) && !form.handleOfficeIds.includes(item.id)) {
      //   form.handleOfficeIds = [...form.handleOfficeIds, item.id]
      //   ElMessage({ type: 'success', message: `当前已选择主办单位，已为您将【${item.name}】添加到协办单位` })
      // }
    }
  } else if (form.transactType === 'publish') {
    if (!form.handleOfficeIds.includes(item.id)) {
      form.handleOfficeIds = [...form.handleOfficeIds, item.id]
      ElMessage({ type: 'success', message: `已为您将${item.name}添加到分办单位` })
    }
  }
}

const suggestionUnlock = async () => {
  if (props.isLock) return
  const res = await api.suggestionUnlock({ ids: [suggestId.value] })
  var { data } = res
  console.log(data)
}
const suggestionNextNodes = async () => {
  const res = await api.suggestionNextNodes({ suggestionId: props.id })
  var { data } = res
  if (props.signId == '2') {
    reviewResult.value = [
      {
        "nodeId": "prepareSubmitHandle",
        "nodeName": "立案",
        "formType": "success"
      },
      {
        "nodeId": "exchangeLetter",
        "nodeName": "转来信",
        "formType": "other"
      },
      {
        "nodeId": "exchangeSocial",
        "nodeName": "转社情民意",
        "formType": "other"
      },
      {
        "nodeId": "rejectReceive",
        "nodeName": "不予立案",
        "formType": "noAccept"
      },
      {
        "nodeId": "cancelSuggestion",
        "nodeName": "撤案",
        "formType": "other"
      },
      {
        "nodeId": "returnSubmit",
        "nodeName": "退回",
        "formType": "other"
      }
    ]
  } else {
    reviewResult.value = data
  }
  for (let index = 0; index < data.length; index++) {
    const item = data[index]
    if (item.formType === 'noAccept') {
      rejectName.value = item.nodeName
    }
  }
}
const reviewResultChange = () => {
  isReviewResult.value = ''
  rules.rejectReason = [{ required: false, message: `请选择${rejectName.value}理由`, trigger: ['blur', 'change'] }]
  for (let index = 0; index < reviewResult.value.length; index++) {
    const item = reviewResult.value[index]
    if (item.nodeId === form.reviewResult) {
      isReviewResult.value = item.formType
      if (item.formType === 'noAccept') {
        rules.rejectReason = [{ required: true, message: `请选择${rejectName.value}理由`, trigger: ['blur', 'change'] }]
      }
    }
  }
}
const dictionaryData = async () => {
  const res = await api.dictionaryData({ dictCodes: ['suggestion_reject_reason'] })
  var { data } = res
  suggestionRejectReason.value = data.suggestion_reject_reason
}
const suggestionThemeSelect = async () => {
  const res = await api.suggestionThemeSelect({ query: { isUsing: 1 } })
  var { data } = res
  SuggestBigType.value = data
  SuggestBigTypeChange()
}
const SuggestBigTypeChange = () => {
  if (form.SuggestBigType) {
    for (let index = 0; index < SuggestBigType.value.length; index++) {
      const item = SuggestBigType.value[index]
      if (item.id === form.SuggestBigType) {
        if (!item.children.map(v => v.id).includes(form.SuggestSmallType)) {
          form.SuggestSmallType = ''
        }
        SuggestSmallType.value = item.children
      }
    }
  } else {
    form.SuggestSmallType = ''
    SuggestSmallType.value = []
  }
}
const transactTypeChange = () => {
  if (form.transactType === 'main_assist') {
    rules.mainHandleOfficeId = [{ type: 'array', required: true, message: '请选择主办单位', trigger: ['blur', 'change'] }]
    rules.handleOfficeIds = [{ type: 'array', required: false, message: '请选择分办单位', trigger: ['blur', 'change'] }]
  } else if (form.transactType === 'publish') {
    rules.mainHandleOfficeId = [{ type: 'array', required: false, message: '请选择主办单位', trigger: ['blur', 'change'] }]
    rules.handleOfficeIds = [{ type: 'array', required: true, message: '请选择分办单位', trigger: ['blur', 'change'] }]
  } else {
    rules.mainHandleOfficeId = [{ type: 'array', required: false, message: '请选择主办单位', trigger: ['blur', 'change'] }]
    rules.handleOfficeIds = [{ type: 'array', required: false, message: '请选择分办单位', trigger: ['blur', 'change'] }]
  }
}
const submitForm = async (formEl) => {
  if (!formEl) return
  await formEl.validate((valid, fields) => {
    if (valid) { emit('editCallback', suggestionComplete, form) } else { ElMessage({ type: 'warning', message: '请根据提示信息完善字段内容！' }) }
  })
}

const suggestionComplete = async () => {
  const { code } = await api.suggestionComplete({
    suggestionId: props.id,
    nextNodeId: props.signId == '1' ? 'review' : props.signId == '2' ? 'prepareVerify' : form.reviewResult, // 审查结果
    bigThemeId: form.SuggestBigType, // 提案大类
    smallThemeId: form.SuggestSmallType, // 提案小类
    variable: {
      handleContent: form.reviewOpinion, // 审查意见
      spareDict: isReviewResult.value === 'noAccept' ? form.rejectReason : ''
    },
    handleOfficeType: form.transactType, // 办理方式
    mainHandleOfficeId: form.mainHandleOfficeId.join(''), // 主办单位
    handleOfficeIds: form.handleOfficeIds.length > 0 && form.handleOfficeIds[0] ? form.handleOfficeIds : [] // 协办或分办单位
  })
  if (code === 200) {
    ElMessage({ type: 'success', message: '审查成功' })
    emit('callback')
  }
}
const resetForm = () => { emit('callback') }

onDeactivated(() => {
  qiankunMicro.setGlobalState({ AiChatWindow: false })
  qiankunMicro.setGlobalState({ AiChatCode: 'test_chat' })
  qiankunMicro.setGlobalState({ AiChatParams: {} })
  qiankunMicro.setGlobalState({ AiChatContent: '' })
  // elAiChatClass.AiChatConfig({ AiChatCode: 'test_chat', AiChatWindow: false })
  // elAiChatClass.AiChatHistory()
  suggestionUnlock()
})
onBeforeUnmount(() => {
  qiankunMicro.setGlobalState({ AiChatWindow: false })
  qiankunMicro.setGlobalState({ AiChatCode: 'test_chat' })
  qiankunMicro.setGlobalState({ AiChatParams: {} })
  qiankunMicro.setGlobalState({ AiChatContent: '' })
  // elAiChatClass.AiChatConfig({ AiChatCode: 'test_chat', AiChatWindow: false })
  // elAiChatClass.AiChatHistory()
  // suggestionUnlock()
})
watch(() => [props.SuggestBigType, props.SuggestSmallType], () => {
  form.SuggestBigType = props.SuggestBigType
  form.SuggestSmallType = props.SuggestSmallType
  SuggestBigTypeChange()
}, { immediate: true })
watch(() => [props.hopeHandleOfficeIds, props.content], () => {
  // if (props.content) {
  //   elAiChatClass.AiChatConfig({
  //     AiChatCode: 'ai-assistant-chat',
  //     AiChatModuleId: props.id,
  //     AiChatWindow: true,
  //     AiChatContent: props.content
  //   })
  //   elAiChatClass.AiChatHistory('ai-assistant-chat', props.id)
  // }
  qiankunMicro.setGlobalState({ AiChatContent: props.content })
  const selectUnit = props?.hopeHandleOfficeIds || []
  unitParams.value = { selectUnit: JSON.stringify(selectUnit), content: props.content }
}, { immediate: true })
</script>
<style lang="scss">
.SuggestReviewDetail {
  width: 100%;
  height: 100%;

  .SuggestReviewDetailNameBody {
    padding: 0 var(--zy-distance-one);
    padding-top: var(--zy-distance-one);

    .SuggestReviewDetailName {
      width: 100%;
      color: var(--zy-el-color-primary);
      font-size: var(--zy-name-font-size);
      line-height: var(--zy-line-height);
      font-weight: bold;
      position: relative;
      text-align: center;

      div {
        display: inline-block;
        background-color: #fff;
        position: relative;
        z-index: 2;
        padding: 0 20px;
      }

      &::after {
        content: "";
        position: absolute;
        top: 50%;
        left: 0;
        transform: translateY(-50%);
        width: 100%;
        height: 1px;
        background-color: var(--zy-el-color-primary);
      }
    }
  }

  .suggest-simple-select-unit {
    box-shadow: 0 0 0 1px var(--zy-el-input-border-color, var(--zy-el-border-color)) inset;
    border-radius: var(--zy-el-input-border-radius, var(--zy-el-border-radius-base));
  }
}
</style>
