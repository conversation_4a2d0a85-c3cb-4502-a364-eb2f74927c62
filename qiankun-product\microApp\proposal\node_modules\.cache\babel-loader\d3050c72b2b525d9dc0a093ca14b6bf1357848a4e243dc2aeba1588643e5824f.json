{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, openBlock as _openBlock, createBlock as _createBlock, createCommentVNode as _createCommentVNode, createTextVNode as _createTextVNode, createElementVNode as _createElementVNode, createElementBlock as _createElementBlock } from \"vue\";\nvar _hoisted_1 = {\n  class: \"SelectHandlingUnit\"\n};\nvar _hoisted_2 = {\n  class: \"globalFormButton\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_option = _resolveComponent(\"el-option\");\n  var _component_el_select = _resolveComponent(\"el-select\");\n  var _component_el_form_item = _resolveComponent(\"el-form-item\");\n  var _component_suggest_simple_select_unit = _resolveComponent(\"suggest-simple-select-unit\");\n  var _component_el_button = _resolveComponent(\"el-button\");\n  var _component_el_form = _resolveComponent(\"el-form\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_el_form, {\n    ref: \"formRef\",\n    model: $setup.form,\n    rules: $setup.rules,\n    inline: \"\",\n    \"label-position\": \"top\",\n    class: \"globalForm\"\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_el_form_item, {\n        label: \"办理方式\",\n        prop: \"transactType\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_el_select, {\n            modelValue: $setup.form.transactType,\n            \"onUpdate:modelValue\": _cache[0] || (_cache[0] = function ($event) {\n              return $setup.form.transactType = $event;\n            }),\n            placeholder: \"请选择办理方式\",\n            onChange: $setup.transactTypeChange,\n            clearable: \"\"\n          }, {\n            default: _withCtx(function () {\n              return [_createVNode(_component_el_option, {\n                label: \"主办\",\n                value: \"main_assist\"\n              }), _createVNode(_component_el_option, {\n                label: \"分办\",\n                value: \"publish\"\n              })];\n            }),\n            _: 1 /* STABLE */\n          }, 8 /* PROPS */, [\"modelValue\"])];\n        }),\n        _: 1 /* STABLE */\n      }), $setup.form.transactType === 'main_assist' ? (_openBlock(), _createBlock(_component_el_form_item, {\n        key: 0,\n        label: \"主办单位\",\n        prop: \"mainHandleOfficeId\",\n        class: \"globalFormTitle\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_suggest_simple_select_unit, {\n            modelValue: $setup.form.mainHandleOfficeId,\n            \"onUpdate:modelValue\": _cache[1] || (_cache[1] = function ($event) {\n              return $setup.form.mainHandleOfficeId = $event;\n            }),\n            filterId: $setup.form.handleOfficeIds,\n            max: 1\n          }, null, 8 /* PROPS */, [\"modelValue\", \"filterId\"])];\n        }),\n        _: 1 /* STABLE */\n      })) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" <template v-if=\\\"form.transactType === 'main_assist'\\\">\\r\\n        <el-form-item label=\\\"协办单位\\\"\\r\\n                      class=\\\"globalFormTitle\\\">\\r\\n          <suggest-simple-select-unit v-model=\\\"form.handleOfficeIds\\\"\\r\\n                                      :filterId=\\\"form.mainHandleOfficeId\\\"></suggest-simple-select-unit>\\r\\n        </el-form-item>\\r\\n      </template> \"), $setup.form.transactType === 'publish' ? (_openBlock(), _createBlock(_component_el_form_item, {\n        key: 1,\n        label: \"分办单位\",\n        prop: \"handleOfficeIds\",\n        class: \"globalFormTitle\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_suggest_simple_select_unit, {\n            modelValue: $setup.form.handleOfficeIds,\n            \"onUpdate:modelValue\": _cache[2] || (_cache[2] = function ($event) {\n              return $setup.form.handleOfficeIds = $event;\n            })\n          }, null, 8 /* PROPS */, [\"modelValue\"])];\n        }),\n        _: 1 /* STABLE */\n      })) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_el_button, {\n        type: \"primary\",\n        onClick: _cache[3] || (_cache[3] = function ($event) {\n          return $setup.submitForm($setup.formRef);\n        })\n      }, {\n        default: _withCtx(function () {\n          return _cache[4] || (_cache[4] = [_createTextVNode(\"提交\")]);\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_button, {\n        onClick: $setup.resetForm\n      }, {\n        default: _withCtx(function () {\n          return _cache[5] || (_cache[5] = [_createTextVNode(\"取消\")]);\n        }),\n        _: 1 /* STABLE */\n      })])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"model\", \"rules\"])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_form", "ref", "model", "$setup", "form", "rules", "inline", "default", "_withCtx", "_component_el_form_item", "label", "prop", "_component_el_select", "modelValue", "transactType", "_cache", "$event", "placeholder", "onChange", "transactTypeChange", "clearable", "_component_el_option", "value", "_", "_createBlock", "key", "_component_suggest_simple_select_unit", "mainHandleOfficeId", "filterId", "handleOfficeIds", "max", "_createCommentVNode", "_createElementVNode", "_hoisted_2", "_component_el_button", "type", "onClick", "submitForm", "formRef", "_createTextVNode", "resetForm"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestedClassification\\SelectHandlingUnit.vue"], "sourcesContent": ["<template>\r\n  <div class=\"SelectHandlingUnit\">\r\n    <el-form ref=\"formRef\" :model=\"form\" :rules=\"rules\" inline label-position=\"top\" class=\"globalForm\">\r\n      <el-form-item label=\"办理方式\" prop=\"transactType\">\r\n        <el-select v-model=\"form.transactType\" placeholder=\"请选择办理方式\" @change=\"transactTypeChange\" clearable>\r\n          <el-option label=\"主办\" value=\"main_assist\" />\r\n          <el-option label=\"分办\" value=\"publish\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <template v-if=\"form.transactType === 'main_assist'\">\r\n        <el-form-item label=\"主办单位\" prop=\"mainHandleOfficeId\" class=\"globalFormTitle\">\r\n          <suggest-simple-select-unit v-model=\"form.mainHandleOfficeId\" :filterId=\"form.handleOfficeIds\"\r\n            :max=\"1\"></suggest-simple-select-unit>\r\n        </el-form-item>\r\n      </template>\r\n      <!-- <template v-if=\"form.transactType === 'main_assist'\">\r\n        <el-form-item label=\"协办单位\"\r\n                      class=\"globalFormTitle\">\r\n          <suggest-simple-select-unit v-model=\"form.handleOfficeIds\"\r\n                                      :filterId=\"form.mainHandleOfficeId\"></suggest-simple-select-unit>\r\n        </el-form-item>\r\n      </template> -->\r\n      <template v-if=\"form.transactType === 'publish'\">\r\n        <el-form-item label=\"分办单位\" prop=\"handleOfficeIds\" class=\"globalFormTitle\">\r\n          <suggest-simple-select-unit v-model=\"form.handleOfficeIds\"></suggest-simple-select-unit>\r\n        </el-form-item>\r\n      </template>\r\n      <div class=\"globalFormButton\">\r\n        <el-button type=\"primary\" @click=\"submitForm(formRef)\">提交</el-button>\r\n        <el-button @click=\"resetForm\">取消</el-button>\r\n      </div>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'SelectHandlingUnit' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { reactive, ref, onMounted } from 'vue'\r\nimport { ElMessage } from 'element-plus'\r\nconst props = defineProps({ ids: { type: Array, default: () => ([]) } })\r\nconst emit = defineEmits(['callback'])\r\nconst formRef = ref()\r\nconst form = reactive({\r\n  transactType: '', // 请选择办理方式 默认主办/协办\r\n  mainHandleOfficeId: [],\r\n  handleOfficeIds: []\r\n})\r\nconst rules = reactive({\r\n  transactType: [{ required: true, message: '请选择办理方式', trigger: ['blur', 'change'] }],\r\n  mainHandleOfficeId: [{ type: 'array', required: false, message: '请选择主办单位', trigger: ['blur', 'change'] }],\r\n  // handleOfficeIds: [{ type: 'array', required: false, message: '请选择协办单位', trigger: ['blur', 'change'] }]\r\n})\r\nconst transactTypeChange = () => {\r\n  if (form.transactType === 'main_assist') {\r\n    rules.mainHandleOfficeId = [{ type: 'array', required: true, message: '请选择主办单位', trigger: ['blur', 'change'] }]\r\n    rules.handleOfficeIds = [{ type: 'array', required: false, message: '请选择分办单位', trigger: ['blur', 'change'] }]\r\n  } else if (form.transactType === 'publish') {\r\n    rules.mainHandleOfficeId = [{ type: 'array', required: false, message: '请选择主办单位', trigger: ['blur', 'change'] }]\r\n    rules.handleOfficeIds = [{ type: 'array', required: true, message: '请选择分办单位', trigger: ['blur', 'change'] }]\r\n  } else {\r\n    rules.mainHandleOfficeId = [{ type: 'array', required: false, message: '请选择主办单位', trigger: ['blur', 'change'] }]\r\n    rules.handleOfficeIds = [{ type: 'array', required: false, message: '请选择分办单位', trigger: ['blur', 'change'] }]\r\n  }\r\n}\r\nconst submitForm = async (formEl) => {\r\n  if (!formEl) return\r\n  await formEl.validate((valid, fields) => {\r\n    if (valid) { globalJson() } else { ElMessage({ type: 'warning', message: '请根据提示信息完善字段内容！' }) }\r\n  })\r\n}\r\nconst globalJson = async () => {\r\n  var params = {\r\n    suggestionIds: props.ids,\r\n    handleOfficeType: form.transactType || null, // 办理方式\r\n    mainHandleOfficeId: form.mainHandleOfficeId.join('') || null, // 主办单位\r\n    handleOfficeIds: form.handleOfficeIds || null, // 协办或分办单位\r\n  }\r\n  const { code } = await api.reqProposalBatchComplete('add', params)\r\n  if (code === 200) {\r\n    ElMessage({ type: 'success', message: '成功' })\r\n    emit('callback', 'submit')\r\n  }\r\n}\r\nconst resetForm = () => {\r\n  form.mainHandleOfficeId = []\r\n  form.handleOfficeIds = []\r\n  emit('callback')\r\n}\r\n\r\nonMounted(() => { })\r\n\r\n</script>\r\n<style lang=\"scss\">\r\n.suggest-simple-select-unit {\r\n  border: 1px solid #ebebeb;\r\n  border-radius: 4px;\r\n}\r\n\r\n.SelectHandlingUnit {\r\n  width: 680px;\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAoB;;EA0BtBA,KAAK,EAAC;AAAkB;;;;;;;;uBA1BjCC,mBAAA,CA+BM,OA/BNC,UA+BM,GA9BJC,YAAA,CA6BUC,kBAAA;IA7BDC,GAAG,EAAC,SAAS;IAAEC,KAAK,EAAEC,MAAA,CAAAC,IAAI;IAAGC,KAAK,EAAEF,MAAA,CAAAE,KAAK;IAAEC,MAAM,EAAN,EAAM;IAAC,gBAAc,EAAC,KAAK;IAACV,KAAK,EAAC;;IAF1FW,OAAA,EAAAC,QAAA,CAGM;MAAA,OAKe,CALfT,YAAA,CAKeU,uBAAA;QALDC,KAAK,EAAC,MAAM;QAACC,IAAI,EAAC;;QAHtCJ,OAAA,EAAAC,QAAA,CAIQ;UAAA,OAGY,CAHZT,YAAA,CAGYa,oBAAA;YAPpBC,UAAA,EAI4BV,MAAA,CAAAC,IAAI,CAACU,YAAY;YAJ7C,uBAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OAI4Bb,MAAA,CAAAC,IAAI,CAACU,YAAY,GAAAE,MAAA;YAAA;YAAEC,WAAW,EAAC,SAAS;YAAEC,QAAM,EAAEf,MAAA,CAAAgB,kBAAkB;YAAEC,SAAS,EAAT;;YAJlGb,OAAA,EAAAC,QAAA,CAKU;cAAA,OAA4C,CAA5CT,YAAA,CAA4CsB,oBAAA;gBAAjCX,KAAK,EAAC,IAAI;gBAACY,KAAK,EAAC;kBAC5BvB,YAAA,CAAwCsB,oBAAA;gBAA7BX,KAAK,EAAC,IAAI;gBAACY,KAAK,EAAC;;;YANtCC,CAAA;;;QAAAA,CAAA;UASsBpB,MAAA,CAAAC,IAAI,CAACU,YAAY,sB,cAC/BU,YAAA,CAGef,uBAAA;QAbvBgB,GAAA;QAUsBf,KAAK,EAAC,MAAM;QAACC,IAAI,EAAC,oBAAoB;QAACf,KAAK,EAAC;;QAVnEW,OAAA,EAAAC,QAAA,CAWU;UAAA,OACwC,CADxCT,YAAA,CACwC2B,qCAAA;YAZlDb,UAAA,EAW+CV,MAAA,CAAAC,IAAI,CAACuB,kBAAkB;YAXtE,uBAAAZ,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OAW+Cb,MAAA,CAAAC,IAAI,CAACuB,kBAAkB,GAAAX,MAAA;YAAA;YAAGY,QAAQ,EAAEzB,MAAA,CAAAC,IAAI,CAACyB,eAAe;YAC1FC,GAAG,EAAE;;;QAZlBP,CAAA;YAAAQ,mBAAA,gBAeMA,mBAAA,gYAMe,EACC5B,MAAA,CAAAC,IAAI,CAACU,YAAY,kB,cAC/BU,YAAA,CAEef,uBAAA;QAzBvBgB,GAAA;QAuBsBf,KAAK,EAAC,MAAM;QAACC,IAAI,EAAC,iBAAiB;QAACf,KAAK,EAAC;;QAvBhEW,OAAA,EAAAC,QAAA,CAwBU;UAAA,OAAwF,CAAxFT,YAAA,CAAwF2B,qCAAA;YAxBlGb,UAAA,EAwB+CV,MAAA,CAAAC,IAAI,CAACyB,eAAe;YAxBnE,uBAAAd,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OAwB+Cb,MAAA,CAAAC,IAAI,CAACyB,eAAe,GAAAb,MAAA;YAAA;;;QAxBnEO,CAAA;YAAAQ,mBAAA,gBA2BMC,mBAAA,CAGM,OAHNC,UAGM,GAFJlC,YAAA,CAAqEmC,oBAAA;QAA1DC,IAAI,EAAC,SAAS;QAAEC,OAAK,EAAArB,MAAA,QAAAA,MAAA,gBAAAC,MAAA;UAAA,OAAEb,MAAA,CAAAkC,UAAU,CAAClC,MAAA,CAAAmC,OAAO;QAAA;;QA5B5D/B,OAAA,EAAAC,QAAA,CA4B+D;UAAA,OAAEO,MAAA,QAAAA,MAAA,OA5BjEwB,gBAAA,CA4B+D,IAAE,E;;QA5BjEhB,CAAA;UA6BQxB,YAAA,CAA4CmC,oBAAA;QAAhCE,OAAK,EAAEjC,MAAA,CAAAqC;MAAS;QA7BpCjC,OAAA,EAAAC,QAAA,CA6BsC;UAAA,OAAEO,MAAA,QAAAA,MAAA,OA7BxCwB,gBAAA,CA6BsC,IAAE,E;;QA7BxChB,CAAA;;;IAAAA,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}