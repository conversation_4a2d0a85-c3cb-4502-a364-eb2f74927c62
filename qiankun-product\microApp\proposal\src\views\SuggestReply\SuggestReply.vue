<template>
  <div class="SuggestReply">
    <xyl-label v-model="labelId" @labelClick="handleLabel">
      <xyl-label-item v-for="item in labelList" :key="item.itemCode" :value="item.itemCode">{{ item.itemName }}（{{
        item.count }}）</xyl-label-item>
    </xyl-label>
    <xyl-search-button @queryClick="handleQuery" @resetClick="handleReset" @handleButton="handleButton"
      :buttonList="buttonList" :data="tableHead" ref="queryRef">
      <template #search>
        <el-input v-model="keyword" placeholder="请输入关键词" @keyup.enter="handleQuery" clearable />
      </template>
    </xyl-search-button>
    <div class="globalTable">
      <el-table ref="tableRef" row-key="id" :data="tableData" @select="handleTableSelect"
        @select-all="handleTableSelect" @sort-change="handleSortChange" :header-cell-class-name="handleHeaderClass">
        <el-table-column type="selection" reserve-selection width="60" fixed />
        <xyl-global-table :tableHead="tableHead" @tableClick="handleTableClick"
          :noTooltip="['mainHandleOffices', 'assistHandleOffices', 'publishHandleOffices']">
          <template #mainHandleOffices="scope">
            {{scope.row.mainHandleOffices?.map(v => v.flowHandleOfficeName).join('、')}}
          </template>
          <template #assistHandleOffices="scope">
            {{scope.row.assistHandleOffices?.map(v => v.flowHandleOfficeName).join('、')}}
          </template>
          <template #publishHandleOffices="scope">
            {{scope.row.publishHandleOffices?.map(v => v.flowHandleOfficeName).join('、')}}
          </template>
        </xyl-global-table>
        <xyl-global-table-button :editCustomTableHead="handleEditorCustom"></xyl-global-table-button>
      </el-table>
    </div>
    <div class="globalPagination">
      <el-pagination v-model:currentPage="pageNo" v-model:page-size="pageSize" :page-sizes="pageSizes"
        layout="total, sizes, prev, pager, next, jumper" @size-change="handleQuery" @current-change="handleQuery"
        :total="totals" background />
    </div>
    <xyl-popup-window v-model="exportShow" name="导出Excel">
      <xyl-export-excel name="已答复提案" :exportId="exportId" :params="exportParams" module="proposalExportExcel"
        tableId="id_prop_proposal_hasAnswerSuggestion" @excelCallback="callback"></xyl-export-excel>
    </xyl-popup-window>
  </div>
</template>
<script>
export default { name: 'SuggestReply' }
</script>
<script setup>
import api from '@/api'
import { ref, onActivated } from 'vue'
import { GlobalTable } from 'common/js/GlobalTable.js'
import { qiankunMicro } from 'common/config/MicroGlobal'
import { suggestExportWord, suggestExportAnswer } from '@/assets/js/suggestExportWord'
import { ElMessage, ElMessageBox } from 'element-plus'
const buttonList = ref([
  { id: 'exportWord', name: '导出Word', type: 'primary', has: '' },
  { id: 'export', name: '导出Excel', type: 'primary', has: '' },
  { id: 'exportAnswer', name: '导出答复件', type: 'primary', has: '' }
])
const labelId = ref('')
const labelList = ref([])
const {
  keyword,
  queryRef,
  tableRef,
  totals,
  pageNo,
  pageSize,
  pageSizes,
  tableHead,
  tableData,
  exportId,
  exportParams,
  exportShow,
  handleQuery,
  tableDataArray,
  handleSortChange,
  handleHeaderClass,
  handleTableSelect,
  tableRefReset,
  handleGetParams,
  handleEditorCustom,
  handleExportExcel,
  tableQuery
} = GlobalTable({ tableId: 'id_prop_proposal_hasAnswerSuggestion', tableApi: 'suggestionList' })

onActivated(() => { suggestionCountSelector() })

const suggestionCountSelector = async () => {
  const { data } = await api.suggestionCountSelector({ countItemType: 'answer' })
  labelId.value = data[0].itemCode
  labelList.value = data
  handleLabel()
}
const handleLabel = () => {
  tableQuery.value = { countItemCode: labelId.value || null }
  if (labelId.value === 'has_satisfaction') {
    buttonList.value = [{ id: 'exportWord', name: '导出Word', type: 'primary', has: '' }, { id: 'export', name: '导出Excel', type: 'primary', has: '' }, { id: 'exportAnswer', name: '导出答复件', type: 'primary', has: '' }, { id: 'conclude', name: '批量办结', type: 'primary', has: '' }]
  } else if (labelId.value === 'no_satisfaction') {
    buttonList.value = [{ id: 'exportWord', name: '导出Word', type: 'primary', has: '' }, { id: 'export', name: '导出Excel', type: 'primary', has: '' }, { id: 'exportAnswer', name: '导出答复件', type: 'primary', has: '' }, { id: 'rush', name: '催办测评', type: 'primary', has: '' },{id:'conclude',name:'批量办结',type:'primary',has:''}]
  } else {
    buttonList.value = [{ id: 'exportWord', name: '导出Word', type: 'primary', has: '' }, { id: 'export', name: '导出Excel', type: 'primary', has: '' }, { id: 'exportAnswer', name: '导出答复件', type: 'primary', has: '' }]
  }
  handleQuery()
}
const handleReset = () => {
  keyword.value = ''
  handleQuery()
}
const handleButton = (isType) => {
  switch (isType) {
    case 'exportWord':
      suggestExportWord(handleGetParams())
      break
    case 'export':
      handleExportExcel()
      break
    case 'exportAnswer':
      suggestExportAnswer(handleGetParams())
      break
    case 'rush':
      handleRush()
      break
    case 'conclude':
      handleNext()
      break
    default:
      break
  }
}
const handleTableClick = (key, row) => {
  switch (key) {
    case 'details':
      handleDetails(row)
      break
    default:
      break
  }
}
const handleDetails = (item) => {
  qiankunMicro.setGlobalState({ openRoute: { name: '提案详情', path: '/proposal/SuggestDetail', query: { id: item.id, type: 'reply' } } })
}
const callback = () => {
  tableRefReset()
  handleQuery()
  exportShow.value = false
}
const handleRush = () => {
  if (tableDataArray.value.length) {
    ElMessageBox.confirm('此操作将会提醒选中的提案的提案委员尽快对此提案就行满意度测评, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => { suggestionPress() }).catch(() => { ElMessage({ type: 'info', message: '已取消催办' }) })
  } else {
    ElMessage({ type: 'warning', message: '请至少选择一条数据' })
  }
}
const suggestionPress = async () => {
  const { code } = await api.suggestionPress({ ids: tableDataArray.value.map(v => v.id), pressMessageCode: 'satisfactionHopeHandle' })
  if (code === 200) {
    ElMessage({ type: 'success', message: '催办成功' })
    tableRefReset()
    handleQuery()
  }
}
const handleNext = () => {
  if (tableDataArray.value.length) {
    ElMessageBox.confirm('此操作将批量办结选中的提案, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => { suggestionBatchComplete() }).catch(() => { ElMessage({ type: 'info', message: '已取消办结' }) })
  } else {
    ElMessage({ type: 'warning', message: '请至少选择一条数据' })
  }
}
const suggestionBatchComplete = async () => {
  const { code } = await api.suggestionBatchComplete({ suggestionIds: tableDataArray.value.map(v => v.id), nextNodeId: 'handleOver' })
  if (code === 200) {
    ElMessage({ type: 'success', message: '办结成功' })
    tableRefReset()
    handleQuery()
  }
}
</script>
<style lang="scss">
.SuggestReply {
  width: 100%;
  height: 100%;
  padding: 0 20px;

  .globalTable {
    width: 100%;
    height: calc(100% - ((var(--zy-height) * 2) + (var(--zy-distance-four) * 4) + 42px));
  }
}
</style>
