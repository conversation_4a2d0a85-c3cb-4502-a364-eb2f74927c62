<template>
  <el-scrollbar class="managementHomepage">
    <!-- 顶部统计卡片 -->
    <div class="stat-cards">
      <div class="stat-card">
        <div class="stat-title">待处理</div>
        <div class="stat-value">{{ stats.dclCount }}</div>
      </div>
      <div class="stat-card">
        <div class="stat-title">拟采用</div>
        <div class="stat-value">{{ stats.nclCount }}</div>
      </div>
      <div class="stat-card">
        <div class="stat-title">已采用</div>
        <div class="stat-value">{{ stats.ycyCount }}</div>
      </div>
      <div class="stat-card">
        <div class="stat-title">已留存</div>
        <div class="stat-value warning">{{ stats.ylcCount }}</div>
      </div>
    </div>

    <!-- 图表区域 -->
    <div class="chart-container">
      <div class="trend-chart">
        <h3>报送趋势</h3>
        <div ref="trendChartRef" style="height: 300px;"></div>
      </div>
      <div class="category-chart">
        <h3>类别分布</h3>
        <div ref="categoryChartRef" style="height: 300px;"></div>
      </div>
    </div>

    <!-- 社情民意处理信息表格 -->
    <div class="feedback-table">
      <h3>社情民意处理信息</h3>
      <el-table :data="tableData" style="width: 100%">
        <el-table-column prop="title" label="信息标题" min-width="200">
          <template #default="scope">
            <el-link type="primary" @click="handleJoin(scope.row)">{{ scope.row.title }}</el-link>
          </template>
        </el-table-column>
        <el-table-column label="来源部门" min-width="140">
          <template #default="scope">
            {{ scope.row.reflecterType?.label }}
          </template>
        </el-table-column>
        <el-table-column label="提交时间" min-width="140">
          <template #default="scope">
            {{ format(scope.row.reportDate) }}
          </template>
        </el-table-column>
        <el-table-column prop="adoptInfo" label="当前状态" min-width="120">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.adoptInfo)">{{ scope.row.adoptInfo }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="180">
          <template #default="scope">
            <el-button @click="handleEdit(scope.row)" type="primary" size="small">编辑</el-button>
            <el-button @click="handleJoin(scope.row)" type="primary" size="small">处理</el-button>

            <!-- <el-button @click="editor(scope.row)" type="primary" size="mini">编辑</el-button>
            <el-button @click="editor(scope.row)" type="primary" size="mini">处理</el-button> -->
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination background layout="total, sizes, prev, pager, next, jumper" :total="total" :current-page="pageNo"
          :page-sizes="pageSizes" @current-change="handlePageChange" />
      </div>
    </div>
  </el-scrollbar>
</template>

<script setup>
import api from '@/api'
import { qiankunMicro } from 'common/config/MicroGlobal'
import { format } from 'common/js/time.js'
import { ref, onActivated } from 'vue'
import * as echarts from 'echarts'

// 统计数据
const stats = ref({
  pendingCount: 0,
  processedCount: 0,
  processingCount: 0,
  timeoutCount: 0
})

// 图表引用
const trendChartRef = ref(null)
const categoryChartRef = ref(null)

// 表格数据
const tableData = ref([
  // {
  //   title: '关于改善社区医疗服务的建议',
  //   department: '卫生局',
  //   submitTime: '2023-12-07 10:30',
  //   status: '待处理',
  //   deadline: '2023-12-08 10:30'
  // },
  // {
  //   title: '城市交通拥堵问题的解决方案',
  //   department: '交通局',
  //   submitTime: '2023-12-07 09:15',
  //   status: '处理中',
  //   deadline: '2023-12-08 09:15'
  // },
  // {
  //   title: '小区垃圾分类实施情况反馈',
  //   department: '环保局',
  //   submitTime: '2023-12-07 08:45',
  //   status: '已完成',
  //   deadline: '2023-12-08 08:45'
  // }
])
const pageNo = ref(1)
const pageSize = ref(10)
const pageSizes = ref([10, 20, 30, 40, 50])
const total = ref(0)
// const currentPage = ref(1)

// 状态标签类型
const getStatusType = (status) => {
  const types = {
    '待处理': 'warning',
    '处理中': 'primary',
    '已完成': 'success'
  }
  return types[status] || 'info'
}

const handlePageChange = (page) => {
  pageNo.value = page
  getPendingProcessingList()
}

// 初始化图表
onActivated(() => {
  getAuditorInformation()
  initTrendChart()
  initCategoryChart()
  getPendingProcessingList()
  // 监听窗口大小变化，重绘图表
  window.addEventListener('resize', () => {
    const trendChart = echarts.getInstanceByDom(trendChartRef.value)
    const categoryChart = echarts.getInstanceByDom(categoryChartRef.value)
    trendChart?.resize()
    categoryChart?.resize()
  })
})
// 获取统计基本信息
const getAuditorInformation = async () => {
  const res = await api.getAuditorInformation()
  stats.value.dclCount = res.data.dclCount
  stats.value.nclCount = res.data.nclCount
  stats.value.ycyCount = res.data.ycyCount
  stats.value.ylcCount = res.data.ylcCount
}
// 获取上报趋势图表
const initTrendChart = async () => {
  const res = await api.getAllReportTrend()
  const chart = echarts.init(trendChartRef.value)
  const option = {
    tooltip: {
      show: true,
      trigger: 'axis',
      axisPointer: {
        // 坐标轴指示器，坐标轴触发有效
        type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
      },
    },
    grid: {
      top: 30,
      right: 20,
      bottom: 30,
      left: 40
    },
    xAxis: {
      type: 'category',
      data: res.data.map(v => v.name),
      axisLine: { lineStyle: { color: '#E0E0E0' } }
    },
    yAxis: {
      type: 'value',
      splitLine: { lineStyle: { color: '#E0E0E0', type: 'dashed' } }
    },
    legend: {
      data: ['总量', '已处理', '待处理'],
      top: 0
    },
    series: [
      {
        name: '总量',
        type: 'line',
        data: res.data.map(v => v.count),
        smooth: true,
        itemStyle: { color: '#1890FF' }
      },
      {
        name: '已处理',
        type: 'line',
        data: res.data.map(v => v.yclcount),
        smooth: true,
        itemStyle: { color: '#52C41A' }
      },
      {
        name: '待处理',
        type: 'line',
        data: res.data.map(v => v.dclcount),
        smooth: true,
        itemStyle: { color: '#FAAD14' }
      }
    ]
  }
  chart.setOption(option)
}
// 获取类别分布图表
const initCategoryChart = async () => {
  const res = await api.getTypeStatistics()
  const colors = ['#2B5CE0', '#36CBCB', '#FFB800', '#FF6B6B', '#8E44AD', '#52C41A', '#FA8C16', '#722ED1', '#13C2C2', '#F5222D']
  const transformedData = res.data.map((item, index) => {
    const [name, value] = Object.entries(item)[0];
    return {
      name,
      value: Number(value), // 注意：ECharts 的 value 需要是数字，不是字符串
      itemStyle: { color: colors[index % colors.length] }
    };
  });
  const chart = echarts.init(categoryChartRef.value)
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'horizontal',
      bottom: 0,
      left: 'center'
    },
    series: [
      {
        name: '类别分布',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['50%', '45%'],
        avoidLabelOverlap: false,
        label: {
          show: true,
          position: 'outside',
          formatter: '{b}: {c}',
          fontSize: 12,
          color: '#333'
        },
        labelLine: {
          show: true,
          length: 15,
          length2: 10,
          smooth: false,
          lineStyle: {
            color: '#999',
            width: 1
          }
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 14,
            fontWeight: 'bold'
          }
        },
        data: transformedData
      }
    ]
  }
  chart.setOption(option)
}
// 获取待处理社情民意
const getPendingProcessingList = async () => {
  const res = await api.socialInfoList({
    pageNo: pageNo.value,
    pageSize: pageSize.value,
    tableId: 'id_social_info',
    startTime: '1735660800000',
    endTime: '1767196799999',
    isAnd: 1,
    orderBys: [{ columnId: "id_social_info_create_date", isDesc: "1" }],
    wheres: [{ columnId: "id_social_info_current_node_id", queryType: "EQ", value: "notHandle" }],
    content: ''
  })
  tableData.value = res.data
  total.value = res.total
}
// 编辑
const handleEdit = (row) => {
  qiankunMicro.setGlobalState({ openRoute: { name: '编辑信息', path: '/publicOpinion/PublicOpinionNew', query: { id: row.id } } })
}
// 处理
const handleJoin = (row) => {
  qiankunMicro.setGlobalState({ openRoute: { name: '处理信息', path: '/publicOpinion/PublicOpinionDetail', query: { id: row.id, isComplete: 1, type: 2 } } })
}
</script>

<style lang="scss" scoped>
.managementHomepage {
  padding: 20px;
  background-color: #F9FAFB;

  .stat-cards {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
    margin-bottom: 20px;

    .stat-card {
      background: #fff;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

      .stat-title {
        color: #666;
        font-size: 14px;
        margin-bottom: 8px;
      }

      .stat-value {
        color: #1890FF;
        font-size: 24px;
        font-weight: bold;

        &.warning {
          color: #FF4D4F;
        }
      }
    }
  }

  .chart-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;

    .trend-chart,
    .category-chart {
      background: #fff;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

      h3 {
        margin: 0 0 20px 0;
        font-size: 16px;
        color: #333;
      }
    }
  }

  .feedback-table {
    background: #fff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

    h3 {
      margin: 0 0 20px 0;
      font-size: 16px;
      color: #333;
    }

    .pagination {
      margin-top: 20px;
      display: flex;
      justify-content: flex-end;
    }
  }
}
</style>