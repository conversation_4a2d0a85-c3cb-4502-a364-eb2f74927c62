{"ast": null, "code": "function _regeneratorRuntime() { \"use strict\"; /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */ _regeneratorRuntime = function _regeneratorRuntime() { return e; }; var t, e = {}, r = Object.prototype, n = r.hasOwnProperty, o = Object.defineProperty || function (t, e, r) { t[e] = r.value; }, i = \"function\" == typeof Symbol ? Symbol : {}, a = i.iterator || \"@@iterator\", c = i.asyncIterator || \"@@asyncIterator\", u = i.toStringTag || \"@@toStringTag\"; function define(t, e, r) { return Object.defineProperty(t, e, { value: r, enumerable: !0, configurable: !0, writable: !0 }), t[e]; } try { define({}, \"\"); } catch (t) { define = function define(t, e, r) { return t[e] = r; }; } function wrap(t, e, r, n) { var i = e && e.prototype instanceof Generator ? e : Generator, a = Object.create(i.prototype), c = new Context(n || []); return o(a, \"_invoke\", { value: makeInvokeMethod(t, r, c) }), a; } function tryCatch(t, e, r) { try { return { type: \"normal\", arg: t.call(e, r) }; } catch (t) { return { type: \"throw\", arg: t }; } } e.wrap = wrap; var h = \"suspendedStart\", l = \"suspendedYield\", f = \"executing\", s = \"completed\", y = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} var p = {}; define(p, a, function () { return this; }); var d = Object.getPrototypeOf, v = d && d(d(values([]))); v && v !== r && n.call(v, a) && (p = v); var g = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(p); function defineIteratorMethods(t) { [\"next\", \"throw\", \"return\"].forEach(function (e) { define(t, e, function (t) { return this._invoke(e, t); }); }); } function AsyncIterator(t, e) { function invoke(r, o, i, a) { var c = tryCatch(t[r], t, o); if (\"throw\" !== c.type) { var u = c.arg, h = u.value; return h && \"object\" == typeof h && n.call(h, \"__await\") ? e.resolve(h.__await).then(function (t) { invoke(\"next\", t, i, a); }, function (t) { invoke(\"throw\", t, i, a); }) : e.resolve(h).then(function (t) { u.value = t, i(u); }, function (t) { return invoke(\"throw\", t, i, a); }); } a(c.arg); } var r; o(this, \"_invoke\", { value: function value(t, n) { function callInvokeWithMethodAndArg() { return new e(function (e, r) { invoke(t, n, e, r); }); } return r = r ? r.then(callInvokeWithMethodAndArg, callInvokeWithMethodAndArg) : callInvokeWithMethodAndArg(); } }); } function makeInvokeMethod(e, r, n) { var o = h; return function (i, a) { if (o === f) throw Error(\"Generator is already running\"); if (o === s) { if (\"throw\" === i) throw a; return { value: t, done: !0 }; } for (n.method = i, n.arg = a;;) { var c = n.delegate; if (c) { var u = maybeInvokeDelegate(c, n); if (u) { if (u === y) continue; return u; } } if (\"next\" === n.method) n.sent = n._sent = n.arg;else if (\"throw\" === n.method) { if (o === h) throw o = s, n.arg; n.dispatchException(n.arg); } else \"return\" === n.method && n.abrupt(\"return\", n.arg); o = f; var p = tryCatch(e, r, n); if (\"normal\" === p.type) { if (o = n.done ? s : l, p.arg === y) continue; return { value: p.arg, done: n.done }; } \"throw\" === p.type && (o = s, n.method = \"throw\", n.arg = p.arg); } }; } function maybeInvokeDelegate(e, r) { var n = r.method, o = e.iterator[n]; if (o === t) return r.delegate = null, \"throw\" === n && e.iterator.return && (r.method = \"return\", r.arg = t, maybeInvokeDelegate(e, r), \"throw\" === r.method) || \"return\" !== n && (r.method = \"throw\", r.arg = new TypeError(\"The iterator does not provide a '\" + n + \"' method\")), y; var i = tryCatch(o, e.iterator, r.arg); if (\"throw\" === i.type) return r.method = \"throw\", r.arg = i.arg, r.delegate = null, y; var a = i.arg; return a ? a.done ? (r[e.resultName] = a.value, r.next = e.nextLoc, \"return\" !== r.method && (r.method = \"next\", r.arg = t), r.delegate = null, y) : a : (r.method = \"throw\", r.arg = new TypeError(\"iterator result is not an object\"), r.delegate = null, y); } function pushTryEntry(t) { var e = { tryLoc: t[0] }; 1 in t && (e.catchLoc = t[1]), 2 in t && (e.finallyLoc = t[2], e.afterLoc = t[3]), this.tryEntries.push(e); } function resetTryEntry(t) { var e = t.completion || {}; e.type = \"normal\", delete e.arg, t.completion = e; } function Context(t) { this.tryEntries = [{ tryLoc: \"root\" }], t.forEach(pushTryEntry, this), this.reset(!0); } function values(e) { if (e || \"\" === e) { var r = e[a]; if (r) return r.call(e); if (\"function\" == typeof e.next) return e; if (!isNaN(e.length)) { var o = -1, i = function next() { for (; ++o < e.length;) if (n.call(e, o)) return next.value = e[o], next.done = !1, next; return next.value = t, next.done = !0, next; }; return i.next = i; } } throw new TypeError(typeof e + \" is not iterable\"); } return GeneratorFunction.prototype = GeneratorFunctionPrototype, o(g, \"constructor\", { value: GeneratorFunctionPrototype, configurable: !0 }), o(GeneratorFunctionPrototype, \"constructor\", { value: GeneratorFunction, configurable: !0 }), GeneratorFunction.displayName = define(GeneratorFunctionPrototype, u, \"GeneratorFunction\"), e.isGeneratorFunction = function (t) { var e = \"function\" == typeof t && t.constructor; return !!e && (e === GeneratorFunction || \"GeneratorFunction\" === (e.displayName || e.name)); }, e.mark = function (t) { return Object.setPrototypeOf ? Object.setPrototypeOf(t, GeneratorFunctionPrototype) : (t.__proto__ = GeneratorFunctionPrototype, define(t, u, \"GeneratorFunction\")), t.prototype = Object.create(g), t; }, e.awrap = function (t) { return { __await: t }; }, defineIteratorMethods(AsyncIterator.prototype), define(AsyncIterator.prototype, c, function () { return this; }), e.AsyncIterator = AsyncIterator, e.async = function (t, r, n, o, i) { void 0 === i && (i = Promise); var a = new AsyncIterator(wrap(t, r, n, o), i); return e.isGeneratorFunction(r) ? a : a.next().then(function (t) { return t.done ? t.value : a.next(); }); }, defineIteratorMethods(g), define(g, u, \"Generator\"), define(g, a, function () { return this; }), define(g, \"toString\", function () { return \"[object Generator]\"; }), e.keys = function (t) { var e = Object(t), r = []; for (var n in e) r.push(n); return r.reverse(), function next() { for (; r.length;) { var t = r.pop(); if (t in e) return next.value = t, next.done = !1, next; } return next.done = !0, next; }; }, e.values = values, Context.prototype = { constructor: Context, reset: function reset(e) { if (this.prev = 0, this.next = 0, this.sent = this._sent = t, this.done = !1, this.delegate = null, this.method = \"next\", this.arg = t, this.tryEntries.forEach(resetTryEntry), !e) for (var r in this) \"t\" === r.charAt(0) && n.call(this, r) && !isNaN(+r.slice(1)) && (this[r] = t); }, stop: function stop() { this.done = !0; var t = this.tryEntries[0].completion; if (\"throw\" === t.type) throw t.arg; return this.rval; }, dispatchException: function dispatchException(e) { if (this.done) throw e; var r = this; function handle(n, o) { return a.type = \"throw\", a.arg = e, r.next = n, o && (r.method = \"next\", r.arg = t), !!o; } for (var o = this.tryEntries.length - 1; o >= 0; --o) { var i = this.tryEntries[o], a = i.completion; if (\"root\" === i.tryLoc) return handle(\"end\"); if (i.tryLoc <= this.prev) { var c = n.call(i, \"catchLoc\"), u = n.call(i, \"finallyLoc\"); if (c && u) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } else if (c) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); } else { if (!u) throw Error(\"try statement without catch or finally\"); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } } } }, abrupt: function abrupt(t, e) { for (var r = this.tryEntries.length - 1; r >= 0; --r) { var o = this.tryEntries[r]; if (o.tryLoc <= this.prev && n.call(o, \"finallyLoc\") && this.prev < o.finallyLoc) { var i = o; break; } } i && (\"break\" === t || \"continue\" === t) && i.tryLoc <= e && e <= i.finallyLoc && (i = null); var a = i ? i.completion : {}; return a.type = t, a.arg = e, i ? (this.method = \"next\", this.next = i.finallyLoc, y) : this.complete(a); }, complete: function complete(t, e) { if (\"throw\" === t.type) throw t.arg; return \"break\" === t.type || \"continue\" === t.type ? this.next = t.arg : \"return\" === t.type ? (this.rval = this.arg = t.arg, this.method = \"return\", this.next = \"end\") : \"normal\" === t.type && e && (this.next = e), y; }, finish: function finish(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.finallyLoc === t) return this.complete(r.completion, r.afterLoc), resetTryEntry(r), y; } }, catch: function _catch(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.tryLoc === t) { var n = r.completion; if (\"throw\" === n.type) { var o = n.arg; resetTryEntry(r); } return o; } } throw Error(\"illegal catch attempt\"); }, delegateYield: function delegateYield(e, r, n) { return this.delegate = { iterator: values(e), resultName: r, nextLoc: n }, \"next\" === this.method && (this.arg = t), y; } }, e; }\nfunction asyncGeneratorStep(n, t, e, r, o, a, c) { try { var i = n[a](c), u = i.value; } catch (n) { return void e(n); } i.done ? t(u) : Promise.resolve(u).then(r, o); }\nfunction _asyncToGenerator(n) { return function () { var t = this, e = arguments; return new Promise(function (r, o) { var a = n.apply(t, e); function _next(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"next\", n); } function _throw(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"throw\", n); } _next(void 0); }); }; }\nfunction _toConsumableArray(r) { return _arrayWithoutHoles(r) || _iterableToArray(r) || _unsupportedIterableToArray(r) || _nonIterableSpread(); }\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(r, a) { if (r) { if (\"string\" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }\nfunction _iterableToArray(r) { if (\"undefined\" != typeof Symbol && null != r[Symbol.iterator] || null != r[\"@@iterator\"]) return Array.from(r); }\nfunction _arrayWithoutHoles(r) { if (Array.isArray(r)) return _arrayLikeToArray(r); }\nfunction _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }\nimport api from '@/api';\nimport { reactive, ref, onActivated, watch } from 'vue';\nimport { whetherUseIntelligentize } from 'common/js/system_var.js';\nimport { ElMessage } from 'element-plus';\nimport SuggestRecommendType from '@/components/SuggestRecommendType/SuggestRecommendType.vue';\nimport SuggestRecommendUnit from '@/components/SuggestRecommendUnit/SuggestRecommendUnit.vue';\nvar __default__ = {\n  name: 'SuggestAssignDetail'\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  props: {\n    id: {\n      type: String,\n      default: ''\n    },\n    name: {\n      type: String,\n      default: ''\n    },\n    details: {\n      type: Object,\n      default: function _default() {\n        return {};\n      }\n    },\n    transactObj: {\n      type: Object,\n      default: function _default() {\n        return {};\n      }\n    },\n    isPreAssign: {\n      type: Boolean,\n      default: false\n    }\n  },\n  emits: ['callback'],\n  setup(__props, _ref) {\n    var __expose = _ref.expose,\n      __emit = _ref.emit;\n    __expose();\n    var props = __props;\n    var emit = __emit;\n    var isReviewResult = ref('');\n    var formRef = ref();\n    var form = reactive({\n      reviewResult: '',\n      // 交办方式\n      SuggestBigType: '',\n      // 提案大类\n      SuggestSmallType: '',\n      // 提案小类\n      reviewOpinion: '',\n      // 交办意见\n      transactType: '',\n      // 请选择办理方式\n      mainHandleOfficeId: [],\n      handleOfficeIds: [],\n      answerStopDate: '',\n      adjustStopDate: '',\n      confirmStopDate: ''\n    });\n    var rules = reactive({\n      reviewResult: [{\n        required: true,\n        message: '请选择交办方式',\n        trigger: ['blur', 'change']\n      }],\n      SuggestBigType: [{\n        required: true,\n        message: '请选择提案大类',\n        trigger: ['blur', 'change']\n      }],\n      transactType: [{\n        required: false,\n        message: '请选择办理方式',\n        trigger: ['blur', 'change']\n      }],\n      mainHandleOfficeId: [{\n        type: 'array',\n        required: false,\n        message: '请选择主办单位',\n        trigger: ['blur', 'change']\n      }],\n      handleOfficeIds: [{\n        type: 'array',\n        required: false,\n        message: '请选择分办单位',\n        trigger: ['blur', 'change']\n      }],\n      answerStopDate: [{\n        required: false,\n        message: '请选择答复截止时间',\n        trigger: ['blur', 'change']\n      }],\n      adjustStopDate: [{\n        required: false,\n        message: '请选择调整截止时间',\n        trigger: ['blur', 'change']\n      }],\n      confirmStopDate: [{\n        required: false,\n        message: '请选择签收截止时间',\n        trigger: ['blur', 'change']\n      }]\n    });\n    var disabledDate = function disabledDate(time) {\n      return time.getTime() < Date.now() + 3600 * 1000 * 24 * 3;\n    };\n    var adjustDisabledDate = function adjustDisabledDate(time) {\n      return time.getTime() < Date.now() + 3600 * 1000 * 24;\n    };\n    var unitParams = ref({});\n    var reviewResult = ref([]);\n    var SuggestBigType = ref([]);\n    var SuggestSmallType = ref([]);\n    // const transactType = ref([])\n\n    var elTypeShow = ref(false);\n    var visibleTypeShow = ref(false);\n    var elUnitShow = ref(false);\n    var visibleUnitShow = ref(false);\n    onActivated(function () {\n      globalReadConfig();\n      suggestionThemeSelect();\n      if (props.id) {\n        suggestionNextNodes();\n      } else {\n        var currentDate = new Date();\n        currentDate.setDate(currentDate.getDate() + 14); // 直接加 14 天\n        // 赋值 Date 对象\n        form.confirmStopDate = currentDate;\n        console.log('currentDate===>', currentDate);\n      }\n    });\n    var typeCallback = function typeCallback(isElIsShow, isVisibleIsShow) {\n      elTypeShow.value = isElIsShow;\n      visibleTypeShow.value = isVisibleIsShow;\n    };\n    var typeSelect = function typeSelect(item, id) {\n      if (id) {\n        form.SuggestBigType = id;\n        SuggestBigTypeChange();\n        form.SuggestSmallType = item._id;\n      } else {\n        form.SuggestBigType = item._id;\n        SuggestBigTypeChange();\n      }\n    };\n    var unitCallback = function unitCallback(isElIsShow, isVisibleIsShow) {\n      elUnitShow.value = isElIsShow;\n      visibleUnitShow.value = isVisibleIsShow;\n    };\n    var unitSelect = function unitSelect(item) {\n      if (form.transactType === 'main_assist') {\n        if (!form.mainHandleOfficeId.length) {\n          if (!form.handleOfficeIds.includes(item.id)) {\n            form.mainHandleOfficeId = [item.id];\n            ElMessage({\n              type: 'success',\n              message: `已为您将【${item.name}】添加到主办单位`\n            });\n          }\n        } else {\n          if (!form.mainHandleOfficeId.includes(item.id) && !form.handleOfficeIds.includes(item.id)) {\n            form.handleOfficeIds = [].concat(_toConsumableArray(form.handleOfficeIds), [item.id]);\n            ElMessage({\n              type: 'success',\n              message: `当前已选择主办单位，已为您将【${item.name}】添加到协办单位`\n            });\n          }\n        }\n      } else if (form.transactType === 'publish') {\n        if (!form.handleOfficeIds.includes(item.id)) {\n          form.handleOfficeIds = [].concat(_toConsumableArray(form.handleOfficeIds), [item.id]);\n          ElMessage({\n            type: 'success',\n            message: `已为您将${item.name}添加到分办单位`\n          });\n        }\n      }\n    };\n    var globalReadConfig = /*#__PURE__*/function () {\n      var _ref2 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee() {\n        var _yield$api$globalRead, data;\n        return _regeneratorRuntime().wrap(function _callee$(_context) {\n          while (1) switch (_context.prev = _context.next) {\n            case 0:\n              _context.next = 2;\n              return api.globalReadConfig({\n                codes: ['SuggestSignTime', 'SuggestReplyTime', 'SuggestAdjustTime', 'SuggestReplyDate']\n              });\n            case 2:\n              _yield$api$globalRead = _context.sent;\n              data = _yield$api$globalRead.data;\n              if (data.SuggestReplyTime) {\n                form.answerStopDate = Date.now() + 3600 * 1000 * 24 * Number(data.SuggestReplyTime);\n              }\n              if (data.SuggestAdjustTime) {\n                form.adjustStopDate = Date.now() + 3600 * 1000 * 24 * Number(data.SuggestAdjustTime);\n              }\n              if (data.SuggestSignTime) {\n                form.confirmStopDate = Date.now() + 3600 * 1000 * 24 * Number(data.SuggestSignTime);\n              }\n              if (data.SuggestReplyDate && new Date().getTime() < new Date(data.SuggestReplyDate).getTime()) {\n                form.answerStopDate = new Date(data.SuggestReplyDate).getTime();\n              }\n            case 8:\n            case \"end\":\n              return _context.stop();\n          }\n        }, _callee);\n      }));\n      return function globalReadConfig() {\n        return _ref2.apply(this, arguments);\n      };\n    }();\n    var suggestionNextNodes = /*#__PURE__*/function () {\n      var _ref3 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee2() {\n        var res, data;\n        return _regeneratorRuntime().wrap(function _callee2$(_context2) {\n          while (1) switch (_context2.prev = _context2.next) {\n            case 0:\n              _context2.next = 2;\n              return api.suggestionNextNodes({\n                suggestionId: props.id\n              });\n            case 2:\n              res = _context2.sent;\n              data = res.data;\n              form.reviewResult = data[0].nodeId;\n              reviewResult.value = data;\n              reviewResultChange();\n            case 7:\n            case \"end\":\n              return _context2.stop();\n          }\n        }, _callee2);\n      }));\n      return function suggestionNextNodes() {\n        return _ref3.apply(this, arguments);\n      };\n    }();\n    var reviewResultChange = function reviewResultChange() {\n      isReviewResult.value = '';\n      rules.SuggestBigType = [{\n        required: true,\n        message: '请选择提案大类',\n        trigger: ['blur', 'change']\n      }];\n      rules.transactType = [{\n        required: false,\n        message: '请选择办理方式',\n        trigger: ['blur', 'change']\n      }];\n      rules.answerStopDate = [{\n        required: false,\n        message: '请选择答复截止时间',\n        trigger: ['blur', 'change']\n      }];\n      rules.adjustStopDate = [{\n        required: false,\n        message: '请选择调整截止时间',\n        trigger: ['blur', 'change']\n      }];\n      for (var index = 0; index < reviewResult.value.length; index++) {\n        var item = reviewResult.value[index];\n        if (item.nodeId === form.reviewResult) {\n          isReviewResult.value = item.formType;\n          if (item.formType === 'success') {\n            rules.transactType = [{\n              required: true,\n              message: '请选择办理方式',\n              trigger: ['blur', 'change']\n            }];\n            rules.answerStopDate = [{\n              required: true,\n              message: '请选择答复截止时间',\n              trigger: ['blur', 'change']\n            }];\n            rules.adjustStopDate = [{\n              required: true,\n              message: '请选择调整截止时间',\n              trigger: ['blur', 'change']\n            }];\n          }\n          if (item.formType === 'preAssign') {\n            rules.transactType = [{\n              required: true,\n              message: '请选择办理方式',\n              trigger: ['blur', 'change']\n            }];\n            rules.confirmStopDate = [{\n              required: true,\n              message: '请选择签收截止时间',\n              trigger: ['blur', 'change']\n            }];\n          }\n          if (item.formType === 'sendback') {\n            rules.SuggestBigType = [{\n              required: false,\n              message: '请选择提案大类',\n              trigger: ['blur', 'change']\n            }];\n          }\n        }\n      }\n    };\n    var suggestionThemeSelect = /*#__PURE__*/function () {\n      var _ref4 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee3() {\n        var res, data;\n        return _regeneratorRuntime().wrap(function _callee3$(_context3) {\n          while (1) switch (_context3.prev = _context3.next) {\n            case 0:\n              _context3.next = 2;\n              return api.suggestionThemeSelect({\n                query: {\n                  isUsing: 1\n                }\n              });\n            case 2:\n              res = _context3.sent;\n              data = res.data;\n              SuggestBigType.value = data;\n              SuggestBigTypeChange();\n            case 6:\n            case \"end\":\n              return _context3.stop();\n          }\n        }, _callee3);\n      }));\n      return function suggestionThemeSelect() {\n        return _ref4.apply(this, arguments);\n      };\n    }();\n    var SuggestBigTypeChange = function SuggestBigTypeChange() {\n      if (form.SuggestBigType) {\n        for (var index = 0; index < SuggestBigType.value.length; index++) {\n          var item = SuggestBigType.value[index];\n          if (item.id === form.SuggestBigType) {\n            if (!item.children.map(function (v) {\n              return v.id;\n            }).includes(form.SuggestSmallType)) {\n              form.SuggestSmallType = '';\n            }\n            SuggestSmallType.value = item.children;\n          }\n        }\n      } else {\n        form.SuggestSmallType = '';\n        SuggestSmallType.value = [];\n      }\n    };\n    var transactTypeChange = function transactTypeChange() {\n      if (form.transactType === 'main_assist') {\n        rules.mainHandleOfficeId = [{\n          type: 'array',\n          required: true,\n          message: '请选择主办单位',\n          trigger: ['blur', 'change']\n        }];\n        rules.handleOfficeIds = [{\n          type: 'array',\n          required: false,\n          message: '请选择分办单位',\n          trigger: ['blur', 'change']\n        }];\n      } else if (form.transactType === 'publish') {\n        rules.mainHandleOfficeId = [{\n          type: 'array',\n          required: false,\n          message: '请选择主办单位',\n          trigger: ['blur', 'change']\n        }];\n        rules.handleOfficeIds = [{\n          type: 'array',\n          required: true,\n          message: '请选择分办单位',\n          trigger: ['blur', 'change']\n        }];\n      } else {\n        rules.mainHandleOfficeId = [{\n          type: 'array',\n          required: false,\n          message: '请选择主办单位',\n          trigger: ['blur', 'change']\n        }];\n        rules.handleOfficeIds = [{\n          type: 'array',\n          required: false,\n          message: '请选择分办单位',\n          trigger: ['blur', 'change']\n        }];\n      }\n    };\n    var submitForm = /*#__PURE__*/function () {\n      var _ref5 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee4(formEl) {\n        return _regeneratorRuntime().wrap(function _callee4$(_context4) {\n          while (1) switch (_context4.prev = _context4.next) {\n            case 0:\n              if (formEl) {\n                _context4.next = 2;\n                break;\n              }\n              return _context4.abrupt(\"return\");\n            case 2:\n              _context4.next = 4;\n              return formEl.validate(function (valid, fields) {\n                if (valid) {\n                  suggestionComplete();\n                } else {\n                  ElMessage({\n                    type: 'warning',\n                    message: '请根据提示信息完善字段内容！'\n                  });\n                }\n              });\n            case 4:\n            case \"end\":\n              return _context4.stop();\n          }\n        }, _callee4);\n      }));\n      return function submitForm(_x) {\n        return _ref5.apply(this, arguments);\n      };\n    }();\n    var suggestionComplete = /*#__PURE__*/function () {\n      var _ref6 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee5() {\n        var _yield$api$suggestion, code;\n        return _regeneratorRuntime().wrap(function _callee5$(_context5) {\n          while (1) switch (_context5.prev = _context5.next) {\n            case 0:\n              _context5.next = 2;\n              return api.suggestionComplete({\n                suggestionId: props.id,\n                nextNodeId: form.reviewResult,\n                // 交办方式\n                bigThemeId: form.SuggestBigType,\n                // 提案大类\n                smallThemeId: form.SuggestSmallType,\n                // 提案小类\n                variable: {\n                  handleContent: form.reviewOpinion // 交办意见\n                },\n                handleOfficeType: isReviewResult.value === 'success' || isReviewResult.value === 'preAssign' ? form.transactType : null,\n                // 办理方式\n                mainHandleOfficeId: isReviewResult.value === 'success' || isReviewResult.value === 'preAssign' ? form.mainHandleOfficeId.join('') : null,\n                // 主办单位\n                handleOfficeIds: isReviewResult.value === 'success' || isReviewResult.value === 'preAssign' ? form.handleOfficeIds : null,\n                // 协办或分办单位\n                answerStopDate: isReviewResult.value === 'success' ? form.answerStopDate : null,\n                adjustStopDate: isReviewResult.value === 'success' ? form.adjustStopDate : null,\n                confirmStopDate: isReviewResult.value === 'preAssign' ? form.confirmStopDate : null\n              });\n            case 2:\n              _yield$api$suggestion = _context5.sent;\n              code = _yield$api$suggestion.code;\n              if (code === 200) {\n                ElMessage({\n                  type: 'success',\n                  message: '交办成功'\n                });\n                emit('callback');\n              }\n            case 5:\n            case \"end\":\n              return _context5.stop();\n          }\n        }, _callee5);\n      }));\n      return function suggestionComplete() {\n        return _ref6.apply(this, arguments);\n      };\n    }();\n    var resetForm = function resetForm() {\n      emit('callback');\n    };\n    watch(function () {\n      return props.details;\n    }, function () {\n      var _props$details;\n      var selectUnit = ((_props$details = props.details) === null || _props$details === void 0 || (_props$details = _props$details.hopeHandleOfficeIds) === null || _props$details === void 0 ? void 0 : _props$details.map(function (v) {\n        return v.officeId;\n      })) || [];\n      unitParams.value = {\n        selectUnit: JSON.stringify(selectUnit),\n        content: props.details.content\n      };\n      if (form.SuggestBigType === '') {\n        if (props.details.bigThemeId) {\n          form.SuggestBigType = props.details.bigThemeId; // 提案大类\n          form.SuggestSmallType = props.details.smallThemeId; // 提案小类\n          SuggestBigTypeChange();\n        }\n      }\n    }, {\n      immediate: true\n    });\n    watch(function () {\n      return props.transactObj;\n    }, function () {\n      if (form.transactType === '') {\n        if (props.transactObj.transactType) {\n          form.transactType = props.transactObj.transactType;\n          form.mainHandleOfficeId = props.transactObj.mainHandleOfficeId;\n          form.handleOfficeIds = props.transactObj.handleOfficeIds;\n          transactTypeChange();\n        }\n      }\n    }, {\n      immediate: true\n    });\n    var __returned__ = {\n      props,\n      emit,\n      isReviewResult,\n      formRef,\n      form,\n      rules,\n      disabledDate,\n      adjustDisabledDate,\n      unitParams,\n      reviewResult,\n      SuggestBigType,\n      SuggestSmallType,\n      elTypeShow,\n      visibleTypeShow,\n      elUnitShow,\n      visibleUnitShow,\n      typeCallback,\n      typeSelect,\n      unitCallback,\n      unitSelect,\n      globalReadConfig,\n      suggestionNextNodes,\n      reviewResultChange,\n      suggestionThemeSelect,\n      SuggestBigTypeChange,\n      transactTypeChange,\n      submitForm,\n      suggestionComplete,\n      resetForm,\n      get api() {\n        return api;\n      },\n      reactive,\n      ref,\n      onActivated,\n      watch,\n      get whetherUseIntelligentize() {\n        return whetherUseIntelligentize;\n      },\n      get ElMessage() {\n        return ElMessage;\n      },\n      SuggestRecommendType,\n      SuggestRecommendUnit\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["_regeneratorRuntime", "e", "t", "r", "Object", "prototype", "n", "hasOwnProperty", "o", "defineProperty", "value", "i", "Symbol", "a", "iterator", "c", "asyncIterator", "u", "toStringTag", "define", "enumerable", "configurable", "writable", "wrap", "Generator", "create", "Context", "makeInvokeMethod", "tryCatch", "type", "arg", "call", "h", "l", "f", "s", "y", "GeneratorFunction", "GeneratorFunctionPrototype", "p", "d", "getPrototypeOf", "v", "values", "g", "defineIteratorMethods", "for<PERSON>ach", "_invoke", "AsyncIterator", "invoke", "resolve", "__await", "then", "callInvokeWithMethodAndArg", "Error", "done", "method", "delegate", "maybeInvokeDelegate", "sent", "_sent", "dispatchException", "abrupt", "return", "TypeError", "resultName", "next", "nextLoc", "pushTryEntry", "tryLoc", "catchLoc", "finallyLoc", "afterLoc", "tryEntries", "push", "resetTryEntry", "completion", "reset", "isNaN", "length", "displayName", "isGeneratorFunction", "constructor", "name", "mark", "setPrototypeOf", "__proto__", "awrap", "async", "Promise", "keys", "reverse", "pop", "prev", "char<PERSON>t", "slice", "stop", "rval", "handle", "complete", "finish", "catch", "_catch", "<PERSON><PERSON><PERSON>", "asyncGeneratorStep", "_asyncToGenerator", "arguments", "apply", "_next", "_throw", "_toConsumableArray", "_arrayWithoutHoles", "_iterableToArray", "_unsupportedIterableToArray", "_nonIterableSpread", "_arrayLikeToArray", "toString", "Array", "from", "test", "isArray", "api", "reactive", "ref", "onActivated", "watch", "whetherUseIntelligentize", "ElMessage", "SuggestRecommendType", "SuggestRecommendUnit", "__default__", "props", "__props", "emit", "__emit", "isReviewResult", "formRef", "form", "reviewResult", "SuggestBigType", "SuggestSmallType", "reviewOpinion", "transactType", "mainHandleOfficeId", "handleOfficeIds", "answerStopDate", "adjustStopDate", "confirmStopDate", "rules", "required", "message", "trigger", "disabledDate", "time", "getTime", "Date", "now", "adjustDisabledDate", "unitParams", "elTypeShow", "visibleTypeShow", "elUnitShow", "visibleUnitShow", "globalReadConfig", "suggestionThemeSelect", "id", "suggestionNextNodes", "currentDate", "setDate", "getDate", "console", "log", "typeCallback", "isElIsShow", "isVisibleIsShow", "typeSelect", "item", "SuggestBigTypeChange", "_id", "unitCallback", "unitSelect", "includes", "concat", "_ref2", "_callee", "_yield$api$globalRead", "data", "_callee$", "_context", "codes", "SuggestReplyTime", "Number", "SuggestAdjustTime", "SuggestSignTime", "SuggestReplyDate", "_ref3", "_callee2", "res", "_callee2$", "_context2", "suggestionId", "nodeId", "reviewResultChange", "index", "formType", "_ref4", "_callee3", "_callee3$", "_context3", "query", "isUsing", "children", "map", "transactTypeChange", "submitForm", "_ref5", "_callee4", "formEl", "_callee4$", "_context4", "validate", "valid", "fields", "suggestionComplete", "_x", "_ref6", "_callee5", "_yield$api$suggestion", "code", "_callee5$", "_context5", "nextNodeId", "bigThemeId", "smallThemeId", "variable", "handleContent", "handleOfficeType", "join", "resetForm", "details", "_props$details", "selectUnit", "hopeHandleOfficeIds", "officeId", "JSON", "stringify", "content", "immediate", "transactObj"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/microApp/proposal/src/views/SuggestAssign/component/SuggestAssignDetail.vue"], "sourcesContent": ["<template>\r\n  <div class=\"SuggestAssignDetail\">\r\n    <div class=\"SuggestAssignDetailNameBody\">\r\n      <div class=\"SuggestAssignDetailName\">\r\n        <div>{{ props.name }}</div>\r\n      </div>\r\n    </div>\r\n    <el-form ref=\"formRef\" :model=\"form\" :rules=\"rules\" inline label-position=\"top\" class=\"globalForm\">\r\n      <el-form-item label=\"交办方式\" prop=\"reviewResult\" class=\"globalFormTitle\">\r\n        <el-radio-group v-model=\"form.reviewResult\" @change=\"reviewResultChange\">\r\n          <el-radio v-for=\"item in reviewResult\" :key=\"item.nodeId\" :label=\"item.nodeId\">{{ item.nodeName }}</el-radio>\r\n        </el-radio-group>\r\n        <template v-if=\"whetherUseIntelligentize\">\r\n          <intelligent-assistant v-model:elIsShow=\"elTypeShow\" v-model=\"visibleTypeShow\">\r\n            <SuggestRecommendType :id=\"props.id\" :content=\"props.details.content\" @callback=\"typeCallback\"\r\n              @select=\"typeSelect\"></SuggestRecommendType>\r\n          </intelligent-assistant>\r\n        </template>\r\n      </el-form-item>\r\n      <el-form-item label=\"提案大类\" v-show=\"['success', 'other', 'preAssign'].includes(isReviewResult)\"\r\n        prop=\"SuggestBigType\">\r\n        <el-select v-model=\"form.SuggestBigType\" placeholder=\"请选择提案大类\" @change=\"SuggestBigTypeChange\" clearable>\r\n          <el-option v-for=\"item in SuggestBigType\" :key=\"item.id\" :label=\"item.name\" :value=\"item.id\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"提案小类\" v-show=\"['success', 'other', 'preAssign'].includes(isReviewResult)\">\r\n        <el-select v-model=\"form.SuggestSmallType\" placeholder=\"请选择提案小类\" clearable>\r\n          <el-option v-for=\"item in SuggestSmallType\" :key=\"item.id\" :label=\"item.name\" :value=\"item.id\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item :label=\"`${['success', 'other', 'preAssign'].includes(isReviewResult) ? '交办' : '退回'}意见`\"\r\n        class=\"globalFormTitle\">\r\n        <el-input v-model=\"form.reviewOpinion\"\r\n          :placeholder=\"`请输入${['success', 'other', 'preAssign'].includes(isReviewResult) ? '交办' : '退回'}意见`\"\r\n          type=\"textarea\" :rows=\"5\" clearable />\r\n      </el-form-item>\r\n      <template v-if=\"isReviewResult === 'success'\">\r\n        <el-form-item label=\"办理方式\" prop=\"transactType\">\r\n          <el-select v-model=\"form.transactType\" placeholder=\"请选择办理方式\" @change=\"transactTypeChange\" clearable\r\n            :disabled=\"props.isPreAssign\">\r\n            <el-option label=\"主办\" value=\"main_assist\" />\r\n            <el-option label=\"分办\" value=\"publish\" />\r\n          </el-select>\r\n        </el-form-item>\r\n        <template v-if=\"form.transactType === 'main_assist'\">\r\n          <el-form-item label=\"主办单位\" prop=\"mainHandleOfficeId\" class=\"globalFormTitle\">\r\n            <suggest-simple-select-unit v-model=\"form.mainHandleOfficeId\" :filterId=\"form.handleOfficeIds\" :max=\"1\"\r\n              :disabled=\"props.isPreAssign\"></suggest-simple-select-unit>\r\n            <template v-if=\"whetherUseIntelligentize\">\r\n              <intelligent-assistant v-model:elIsShow=\"elUnitShow\" v-model=\"visibleUnitShow\">\r\n                <SuggestRecommendUnit :params=\"unitParams\" @callback=\"unitCallback\" @select=\"unitSelect\">\r\n                </SuggestRecommendUnit>\r\n              </intelligent-assistant>\r\n            </template>\r\n          </el-form-item>\r\n        </template>\r\n        <!-- <template v-if=\"form.transactType === 'main_assist'\">\r\n          <el-form-item label=\"协办单位\" class=\"globalFormTitle\">\r\n            <suggest-simple-select-unit v-model=\"form.handleOfficeIds\" :filterId=\"form.mainHandleOfficeId\"\r\n              :disabled=\"props.isPreAssign\"></suggest-simple-select-unit>\r\n          </el-form-item>\r\n        </template> -->\r\n        <template v-if=\"form.transactType === 'publish'\">\r\n          <el-form-item label=\"分办单位\" prop=\"handleOfficeIds\" class=\"globalFormTitle\">\r\n            <suggest-simple-select-unit v-model=\"form.handleOfficeIds\"\r\n              :disabled=\"props.isPreAssign\"></suggest-simple-select-unit>\r\n            <template v-if=\"whetherUseIntelligentize\">\r\n              <intelligent-assistant v-model:elIsShow=\"elUnitShow\" v-model=\"visibleUnitShow\">\r\n                <SuggestRecommendUnit :params=\"unitParams\" @callback=\"unitCallback\" @select=\"unitSelect\">\r\n                </SuggestRecommendUnit>\r\n              </intelligent-assistant>\r\n            </template>\r\n          </el-form-item>\r\n        </template>\r\n        <div class=\"zy-el-form-item-br\"></div>\r\n        <el-form-item label=\"答复截止时间\" prop=\"answerStopDate\">\r\n          <xyl-date-picker v-model=\"form.answerStopDate\" type=\"datetime\" value-format=\"x\" placeholder=\"请选择答复截止时间\"\r\n            :disabled-date=\"disabledDate\"></xyl-date-picker>\r\n        </el-form-item>\r\n        <el-form-item label=\"调整截止时间\" prop=\"adjustStopDate\">\r\n          <xyl-date-picker v-model=\"form.adjustStopDate\" type=\"datetime\" value-format=\"x\" placeholder=\"请选择调整截止时间\"\r\n            :disabled-date=\"adjustDisabledDate\"></xyl-date-picker>\r\n        </el-form-item>\r\n      </template>\r\n      <template v-if=\"isReviewResult === 'preAssign'\">\r\n        <el-form-item label=\"办理方式\" prop=\"transactType\">\r\n          <el-select v-model=\"form.transactType\" placeholder=\"请选择办理方式\" @change=\"transactTypeChange\" clearable>\r\n            <el-option label=\"主办\" value=\"main_assist\" />\r\n            <el-option label=\"分办\" value=\"publish\" />\r\n          </el-select>\r\n        </el-form-item>\r\n        <template v-if=\"form.transactType === 'main_assist'\">\r\n          <el-form-item label=\"主办单位\" prop=\"mainHandleOfficeId\" class=\"globalFormTitle\">\r\n            <suggest-simple-select-unit v-model=\"form.mainHandleOfficeId\" :filterId=\"form.handleOfficeIds\"\r\n              :max=\"1\"></suggest-simple-select-unit>\r\n            <template v-if=\"whetherUseIntelligentize\">\r\n              <intelligent-assistant v-model:elIsShow=\"elUnitShow\" v-model=\"visibleUnitShow\">\r\n                <SuggestRecommendUnit :params=\"unitParams\" @callback=\"unitCallback\" @select=\"unitSelect\">\r\n                </SuggestRecommendUnit>\r\n              </intelligent-assistant>\r\n            </template>\r\n          </el-form-item>\r\n        </template>\r\n        <!-- <template v-if=\"form.transactType === 'main_assist'\">\r\n          <el-form-item label=\"协办单位\" class=\"globalFormTitle\">\r\n            <suggest-simple-select-unit\r\n              v-model=\"form.handleOfficeIds\"\r\n              :filterId=\"form.mainHandleOfficeId\"></suggest-simple-select-unit>\r\n          </el-form-item>\r\n        </template> -->\r\n        <template v-if=\"form.transactType === 'publish'\">\r\n          <el-form-item label=\"分办单位\" prop=\"handleOfficeIds\" class=\"globalFormTitle\">\r\n            <suggest-simple-select-unit v-model=\"form.handleOfficeIds\"></suggest-simple-select-unit>\r\n            <template v-if=\"whetherUseIntelligentize\">\r\n              <intelligent-assistant v-model:elIsShow=\"elUnitShow\" v-model=\"visibleUnitShow\">\r\n                <SuggestRecommendUnit :params=\"unitParams\" @callback=\"unitCallback\" @select=\"unitSelect\">\r\n                </SuggestRecommendUnit>\r\n              </intelligent-assistant>\r\n            </template>\r\n          </el-form-item>\r\n        </template>\r\n        <div class=\"zy-el-form-item-br\"></div>\r\n        <el-form-item label=\"签收截止时间\" prop=\"confirmStopDate\">\r\n          <xyl-date-picker v-model=\"form.confirmStopDate\" type=\"datetime\" value-format=\"x\" placeholder=\"请选择签收截止时间\"\r\n            :disabled-date=\"disabledDate\"></xyl-date-picker>\r\n        </el-form-item>\r\n      </template>\r\n      <div class=\"globalFormButton\">\r\n        <el-button type=\"primary\" @click=\"submitForm(formRef)\">提交</el-button>\r\n        <el-button @click=\"resetForm\">取消</el-button>\r\n      </div>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'SuggestAssignDetail' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { reactive, ref, onActivated, watch } from 'vue'\r\nimport { whetherUseIntelligentize } from 'common/js/system_var.js'\r\nimport { ElMessage } from 'element-plus'\r\nimport SuggestRecommendType from '@/components/SuggestRecommendType/SuggestRecommendType.vue'\r\nimport SuggestRecommendUnit from '@/components/SuggestRecommendUnit/SuggestRecommendUnit.vue'\r\nconst props = defineProps({\r\n  id: { type: String, default: '' },\r\n  name: { type: String, default: '' },\r\n  details: { type: Object, default: () => ({}) },\r\n  transactObj: { type: Object, default: () => ({}) },\r\n  isPreAssign: { type: Boolean, default: false }\r\n})\r\nconst emit = defineEmits(['callback'])\r\nconst isReviewResult = ref('')\r\nconst formRef = ref()\r\nconst form = reactive({\r\n  reviewResult: '', // 交办方式\r\n  SuggestBigType: '', // 提案大类\r\n  SuggestSmallType: '', // 提案小类\r\n  reviewOpinion: '', // 交办意见\r\n  transactType: '', // 请选择办理方式\r\n  mainHandleOfficeId: [],\r\n  handleOfficeIds: [],\r\n  answerStopDate: '',\r\n  adjustStopDate: '',\r\n  confirmStopDate: ''\r\n})\r\nconst rules = reactive({\r\n  reviewResult: [{ required: true, message: '请选择交办方式', trigger: ['blur', 'change'] }],\r\n  SuggestBigType: [{ required: true, message: '请选择提案大类', trigger: ['blur', 'change'] }],\r\n  transactType: [{ required: false, message: '请选择办理方式', trigger: ['blur', 'change'] }],\r\n  mainHandleOfficeId: [{ type: 'array', required: false, message: '请选择主办单位', trigger: ['blur', 'change'] }],\r\n  handleOfficeIds: [{ type: 'array', required: false, message: '请选择分办单位', trigger: ['blur', 'change'] }],\r\n  answerStopDate: [{ required: false, message: '请选择答复截止时间', trigger: ['blur', 'change'] }],\r\n  adjustStopDate: [{ required: false, message: '请选择调整截止时间', trigger: ['blur', 'change'] }],\r\n  confirmStopDate: [{ required: false, message: '请选择签收截止时间', trigger: ['blur', 'change'] }]\r\n})\r\nconst disabledDate = (time) => time.getTime() < Date.now() + 3600 * 1000 * 24 * 3\r\nconst adjustDisabledDate = (time) => time.getTime() < Date.now() + 3600 * 1000 * 24\r\nconst unitParams = ref({})\r\nconst reviewResult = ref([])\r\nconst SuggestBigType = ref([])\r\nconst SuggestSmallType = ref([])\r\n// const transactType = ref([])\r\n\r\nconst elTypeShow = ref(false)\r\nconst visibleTypeShow = ref(false)\r\nconst elUnitShow = ref(false)\r\nconst visibleUnitShow = ref(false)\r\n\r\nonActivated(() => {\r\n  globalReadConfig()\r\n  suggestionThemeSelect()\r\n  if (props.id) {\r\n    suggestionNextNodes()\r\n  } else {\r\n    const currentDate = new Date()\r\n    currentDate.setDate(currentDate.getDate() + 14) // 直接加 14 天\r\n    // 赋值 Date 对象\r\n    form.confirmStopDate = currentDate\r\n    console.log('currentDate===>', currentDate)\r\n  }\r\n})\r\n\r\nconst typeCallback = (isElIsShow, isVisibleIsShow) => {\r\n  elTypeShow.value = isElIsShow\r\n  visibleTypeShow.value = isVisibleIsShow\r\n}\r\nconst typeSelect = (item, id) => {\r\n  if (id) {\r\n    form.SuggestBigType = id\r\n    SuggestBigTypeChange()\r\n    form.SuggestSmallType = item._id\r\n  } else {\r\n    form.SuggestBigType = item._id\r\n    SuggestBigTypeChange()\r\n  }\r\n}\r\nconst unitCallback = (isElIsShow, isVisibleIsShow) => {\r\n  elUnitShow.value = isElIsShow\r\n  visibleUnitShow.value = isVisibleIsShow\r\n}\r\nconst unitSelect = (item) => {\r\n  if (form.transactType === 'main_assist') {\r\n    if (!form.mainHandleOfficeId.length) {\r\n      if (!form.handleOfficeIds.includes(item.id)) {\r\n        form.mainHandleOfficeId = [item.id]\r\n        ElMessage({ type: 'success', message: `已为您将【${item.name}】添加到主办单位` })\r\n      }\r\n    } else {\r\n      if (!form.mainHandleOfficeId.includes(item.id) && !form.handleOfficeIds.includes(item.id)) {\r\n        form.handleOfficeIds = [...form.handleOfficeIds, item.id]\r\n        ElMessage({ type: 'success', message: `当前已选择主办单位，已为您将【${item.name}】添加到协办单位` })\r\n      }\r\n    }\r\n  } else if (form.transactType === 'publish') {\r\n    if (!form.handleOfficeIds.includes(item.id)) {\r\n      form.handleOfficeIds = [...form.handleOfficeIds, item.id]\r\n      ElMessage({ type: 'success', message: `已为您将${item.name}添加到分办单位` })\r\n    }\r\n  }\r\n}\r\n\r\nconst globalReadConfig = async () => {\r\n  const { data } = await api.globalReadConfig({\r\n    codes: ['SuggestSignTime', 'SuggestReplyTime', 'SuggestAdjustTime', 'SuggestReplyDate']\r\n  })\r\n  if (data.SuggestReplyTime) {\r\n    form.answerStopDate = Date.now() + 3600 * 1000 * 24 * Number(data.SuggestReplyTime)\r\n  }\r\n  if (data.SuggestAdjustTime) {\r\n    form.adjustStopDate = Date.now() + 3600 * 1000 * 24 * Number(data.SuggestAdjustTime)\r\n  }\r\n  if (data.SuggestSignTime) {\r\n    form.confirmStopDate = Date.now() + 3600 * 1000 * 24 * Number(data.SuggestSignTime)\r\n  }\r\n  if (data.SuggestReplyDate && new Date().getTime() < new Date(data.SuggestReplyDate).getTime()) {\r\n    form.answerStopDate = new Date(data.SuggestReplyDate).getTime()\r\n  }\r\n}\r\nconst suggestionNextNodes = async () => {\r\n  const res = await api.suggestionNextNodes({ suggestionId: props.id })\r\n  var { data } = res\r\n  form.reviewResult = data[0].nodeId\r\n  reviewResult.value = data\r\n  reviewResultChange()\r\n}\r\nconst reviewResultChange = () => {\r\n  isReviewResult.value = ''\r\n  rules.SuggestBigType = [{ required: true, message: '请选择提案大类', trigger: ['blur', 'change'] }]\r\n  rules.transactType = [{ required: false, message: '请选择办理方式', trigger: ['blur', 'change'] }]\r\n  rules.answerStopDate = [{ required: false, message: '请选择答复截止时间', trigger: ['blur', 'change'] }]\r\n  rules.adjustStopDate = [{ required: false, message: '请选择调整截止时间', trigger: ['blur', 'change'] }]\r\n  for (let index = 0; index < reviewResult.value.length; index++) {\r\n    const item = reviewResult.value[index]\r\n    if (item.nodeId === form.reviewResult) {\r\n      isReviewResult.value = item.formType\r\n      if (item.formType === 'success') {\r\n        rules.transactType = [{ required: true, message: '请选择办理方式', trigger: ['blur', 'change'] }]\r\n        rules.answerStopDate = [{ required: true, message: '请选择答复截止时间', trigger: ['blur', 'change'] }]\r\n        rules.adjustStopDate = [{ required: true, message: '请选择调整截止时间', trigger: ['blur', 'change'] }]\r\n      }\r\n      if (item.formType === 'preAssign') {\r\n        rules.transactType = [{ required: true, message: '请选择办理方式', trigger: ['blur', 'change'] }]\r\n        rules.confirmStopDate = [{ required: true, message: '请选择签收截止时间', trigger: ['blur', 'change'] }]\r\n      }\r\n      if (item.formType === 'sendback') {\r\n        rules.SuggestBigType = [{ required: false, message: '请选择提案大类', trigger: ['blur', 'change'] }]\r\n      }\r\n    }\r\n  }\r\n}\r\nconst suggestionThemeSelect = async () => {\r\n  const res = await api.suggestionThemeSelect({ query: { isUsing: 1 } })\r\n  var { data } = res\r\n  SuggestBigType.value = data\r\n  SuggestBigTypeChange()\r\n}\r\nconst SuggestBigTypeChange = () => {\r\n  if (form.SuggestBigType) {\r\n    for (let index = 0; index < SuggestBigType.value.length; index++) {\r\n      const item = SuggestBigType.value[index]\r\n      if (item.id === form.SuggestBigType) {\r\n        if (!item.children.map((v) => v.id).includes(form.SuggestSmallType)) {\r\n          form.SuggestSmallType = ''\r\n        }\r\n        SuggestSmallType.value = item.children\r\n      }\r\n    }\r\n  } else {\r\n    form.SuggestSmallType = ''\r\n    SuggestSmallType.value = []\r\n  }\r\n}\r\nconst transactTypeChange = () => {\r\n  if (form.transactType === 'main_assist') {\r\n    rules.mainHandleOfficeId = [\r\n      { type: 'array', required: true, message: '请选择主办单位', trigger: ['blur', 'change'] }\r\n    ]\r\n    rules.handleOfficeIds = [{ type: 'array', required: false, message: '请选择分办单位', trigger: ['blur', 'change'] }]\r\n  } else if (form.transactType === 'publish') {\r\n    rules.mainHandleOfficeId = [\r\n      { type: 'array', required: false, message: '请选择主办单位', trigger: ['blur', 'change'] }\r\n    ]\r\n    rules.handleOfficeIds = [{ type: 'array', required: true, message: '请选择分办单位', trigger: ['blur', 'change'] }]\r\n  } else {\r\n    rules.mainHandleOfficeId = [\r\n      { type: 'array', required: false, message: '请选择主办单位', trigger: ['blur', 'change'] }\r\n    ]\r\n    rules.handleOfficeIds = [{ type: 'array', required: false, message: '请选择分办单位', trigger: ['blur', 'change'] }]\r\n  }\r\n}\r\nconst submitForm = async (formEl) => {\r\n  if (!formEl) return\r\n  await formEl.validate((valid, fields) => {\r\n    if (valid) {\r\n      suggestionComplete()\r\n    } else {\r\n      ElMessage({ type: 'warning', message: '请根据提示信息完善字段内容！' })\r\n    }\r\n  })\r\n}\r\n\r\nconst suggestionComplete = async () => {\r\n  const { code } = await api.suggestionComplete({\r\n    suggestionId: props.id,\r\n    nextNodeId: form.reviewResult, // 交办方式\r\n    bigThemeId: form.SuggestBigType, // 提案大类\r\n    smallThemeId: form.SuggestSmallType, // 提案小类\r\n    variable: {\r\n      handleContent: form.reviewOpinion // 交办意见\r\n    },\r\n    handleOfficeType:\r\n      isReviewResult.value === 'success' || isReviewResult.value === 'preAssign' ? form.transactType : null, // 办理方式\r\n    mainHandleOfficeId:\r\n      isReviewResult.value === 'success' || isReviewResult.value === 'preAssign'\r\n        ? form.mainHandleOfficeId.join('')\r\n        : null, // 主办单位\r\n    handleOfficeIds:\r\n      isReviewResult.value === 'success' || isReviewResult.value === 'preAssign' ? form.handleOfficeIds : null, // 协办或分办单位\r\n    answerStopDate: isReviewResult.value === 'success' ? form.answerStopDate : null,\r\n    adjustStopDate: isReviewResult.value === 'success' ? form.adjustStopDate : null,\r\n    confirmStopDate: isReviewResult.value === 'preAssign' ? form.confirmStopDate : null\r\n  })\r\n  if (code === 200) {\r\n    ElMessage({ type: 'success', message: '交办成功' })\r\n    emit('callback')\r\n  }\r\n}\r\nconst resetForm = () => {\r\n  emit('callback')\r\n}\r\nwatch(\r\n  () => props.details,\r\n  () => {\r\n    const selectUnit = props.details?.hopeHandleOfficeIds?.map((v) => v.officeId) || []\r\n    unitParams.value = { selectUnit: JSON.stringify(selectUnit), content: props.details.content }\r\n    if (form.SuggestBigType === '') {\r\n      if (props.details.bigThemeId) {\r\n        form.SuggestBigType = props.details.bigThemeId // 提案大类\r\n        form.SuggestSmallType = props.details.smallThemeId // 提案小类\r\n        SuggestBigTypeChange()\r\n      }\r\n    }\r\n  },\r\n  { immediate: true }\r\n)\r\nwatch(\r\n  () => props.transactObj,\r\n  () => {\r\n    if (form.transactType === '') {\r\n      if (props.transactObj.transactType) {\r\n        form.transactType = props.transactObj.transactType\r\n        form.mainHandleOfficeId = props.transactObj.mainHandleOfficeId\r\n        form.handleOfficeIds = props.transactObj.handleOfficeIds\r\n        transactTypeChange()\r\n      }\r\n    }\r\n  },\r\n  { immediate: true }\r\n)\r\n</script>\r\n<style lang=\"scss\">\r\n.SuggestAssignDetail {\r\n  width: 100%;\r\n  height: 100%;\r\n\r\n  .SuggestAssignDetailNameBody {\r\n    padding: 0 var(--zy-distance-one);\r\n    padding-top: var(--zy-distance-one);\r\n\r\n    .SuggestAssignDetailName {\r\n      width: 100%;\r\n      color: var(--zy-el-color-primary);\r\n      font-size: var(--zy-name-font-size);\r\n      line-height: var(--zy-line-height);\r\n      font-weight: bold;\r\n      position: relative;\r\n      text-align: center;\r\n\r\n      div {\r\n        display: inline-block;\r\n        background-color: #fff;\r\n        position: relative;\r\n        z-index: 2;\r\n        padding: 0 20px;\r\n      }\r\n\r\n      &::after {\r\n        content: '';\r\n        position: absolute;\r\n        top: 50%;\r\n        left: 0;\r\n        transform: translateY(-50%);\r\n        width: 100%;\r\n        height: 1px;\r\n        background-color: var(--zy-el-color-primary);\r\n      }\r\n    }\r\n  }\r\n\r\n  .suggest-simple-select-unit {\r\n    box-shadow: 0 0 0 1px var(--zy-el-input-border-color, var(--zy-el-border-color)) inset;\r\n    border-radius: var(--zy-el-input-border-radius, var(--zy-el-border-radius-base));\r\n  }\r\n}\r\n</style>\r\n"], "mappings": "+CA2IA,qJAAAA,mBAAA,YAAAA,oBAAA,WAAAC,CAAA,SAAAC,CAAA,EAAAD,CAAA,OAAAE,CAAA,GAAAC,MAAA,CAAAC,SAAA,EAAAC,CAAA,GAAAH,CAAA,CAAAI,cAAA,EAAAC,CAAA,GAAAJ,MAAA,CAAAK,cAAA,cAAAP,CAAA,EAAAD,CAAA,EAAAE,CAAA,IAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,CAAAO,KAAA,KAAAC,CAAA,wBAAAC,MAAA,GAAAA,MAAA,OAAAC,CAAA,GAAAF,CAAA,CAAAG,QAAA,kBAAAC,CAAA,GAAAJ,CAAA,CAAAK,aAAA,uBAAAC,CAAA,GAAAN,CAAA,CAAAO,WAAA,8BAAAC,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAC,MAAA,CAAAK,cAAA,CAAAP,CAAA,EAAAD,CAAA,IAAAS,KAAA,EAAAP,CAAA,EAAAiB,UAAA,MAAAC,YAAA,MAAAC,QAAA,SAAApB,CAAA,CAAAD,CAAA,WAAAkB,MAAA,mBAAAjB,CAAA,IAAAiB,MAAA,YAAAA,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,gBAAAoB,KAAArB,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAK,CAAA,GAAAV,CAAA,IAAAA,CAAA,CAAAI,SAAA,YAAAmB,SAAA,GAAAvB,CAAA,GAAAuB,SAAA,EAAAX,CAAA,GAAAT,MAAA,CAAAqB,MAAA,CAAAd,CAAA,CAAAN,SAAA,GAAAU,CAAA,OAAAW,OAAA,CAAApB,CAAA,gBAAAE,CAAA,CAAAK,CAAA,eAAAH,KAAA,EAAAiB,gBAAA,CAAAzB,CAAA,EAAAC,CAAA,EAAAY,CAAA,MAAAF,CAAA,aAAAe,SAAA1B,CAAA,EAAAD,CAAA,EAAAE,CAAA,mBAAA0B,IAAA,YAAAC,GAAA,EAAA5B,CAAA,CAAA6B,IAAA,CAAA9B,CAAA,EAAAE,CAAA,cAAAD,CAAA,aAAA2B,IAAA,WAAAC,GAAA,EAAA5B,CAAA,QAAAD,CAAA,CAAAsB,IAAA,GAAAA,IAAA,MAAAS,CAAA,qBAAAC,CAAA,qBAAAC,CAAA,gBAAAC,CAAA,gBAAAC,CAAA,gBAAAZ,UAAA,cAAAa,kBAAA,cAAAC,2BAAA,SAAAC,CAAA,OAAApB,MAAA,CAAAoB,CAAA,EAAA1B,CAAA,qCAAA2B,CAAA,GAAApC,MAAA,CAAAqC,cAAA,EAAAC,CAAA,GAAAF,CAAA,IAAAA,CAAA,CAAAA,CAAA,CAAAG,MAAA,QAAAD,CAAA,IAAAA,CAAA,KAAAvC,CAAA,IAAAG,CAAA,CAAAyB,IAAA,CAAAW,CAAA,EAAA7B,CAAA,MAAA0B,CAAA,GAAAG,CAAA,OAAAE,CAAA,GAAAN,0BAAA,CAAAjC,SAAA,GAAAmB,SAAA,CAAAnB,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAc,CAAA,YAAAM,sBAAA3C,CAAA,gCAAA4C,OAAA,WAAA7C,CAAA,IAAAkB,MAAA,CAAAjB,CAAA,EAAAD,CAAA,YAAAC,CAAA,gBAAA6C,OAAA,CAAA9C,CAAA,EAAAC,CAAA,sBAAA8C,cAAA9C,CAAA,EAAAD,CAAA,aAAAgD,OAAA9C,CAAA,EAAAK,CAAA,EAAAG,CAAA,EAAAE,CAAA,QAAAE,CAAA,GAAAa,QAAA,CAAA1B,CAAA,CAAAC,CAAA,GAAAD,CAAA,EAAAM,CAAA,mBAAAO,CAAA,CAAAc,IAAA,QAAAZ,CAAA,GAAAF,CAAA,CAAAe,GAAA,EAAAE,CAAA,GAAAf,CAAA,CAAAP,KAAA,SAAAsB,CAAA,uBAAAA,CAAA,IAAA1B,CAAA,CAAAyB,IAAA,CAAAC,CAAA,eAAA/B,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,CAAAmB,OAAA,EAAAC,IAAA,WAAAlD,CAAA,IAAA+C,MAAA,SAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,gBAAAX,CAAA,IAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,QAAAZ,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,EAAAoB,IAAA,WAAAlD,CAAA,IAAAe,CAAA,CAAAP,KAAA,GAAAR,CAAA,EAAAS,CAAA,CAAAM,CAAA,gBAAAf,CAAA,WAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,SAAAA,CAAA,CAAAE,CAAA,CAAAe,GAAA,SAAA3B,CAAA,EAAAK,CAAA,oBAAAE,KAAA,WAAAA,MAAAR,CAAA,EAAAI,CAAA,aAAA+C,2BAAA,eAAApD,CAAA,WAAAA,CAAA,EAAAE,CAAA,IAAA8C,MAAA,CAAA/C,CAAA,EAAAI,CAAA,EAAAL,CAAA,EAAAE,CAAA,gBAAAA,CAAA,GAAAA,CAAA,GAAAA,CAAA,CAAAiD,IAAA,CAAAC,0BAAA,EAAAA,0BAAA,IAAAA,0BAAA,qBAAA1B,iBAAA1B,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAE,CAAA,GAAAwB,CAAA,mBAAArB,CAAA,EAAAE,CAAA,QAAAL,CAAA,KAAA0B,CAAA,QAAAoB,KAAA,sCAAA9C,CAAA,KAAA2B,CAAA,oBAAAxB,CAAA,QAAAE,CAAA,WAAAH,KAAA,EAAAR,CAAA,EAAAqD,IAAA,eAAAjD,CAAA,CAAAkD,MAAA,GAAA7C,CAAA,EAAAL,CAAA,CAAAwB,GAAA,GAAAjB,CAAA,UAAAE,CAAA,GAAAT,CAAA,CAAAmD,QAAA,MAAA1C,CAAA,QAAAE,CAAA,GAAAyC,mBAAA,CAAA3C,CAAA,EAAAT,CAAA,OAAAW,CAAA,QAAAA,CAAA,KAAAmB,CAAA,mBAAAnB,CAAA,qBAAAX,CAAA,CAAAkD,MAAA,EAAAlD,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAsD,KAAA,GAAAtD,CAAA,CAAAwB,GAAA,sBAAAxB,CAAA,CAAAkD,MAAA,QAAAhD,CAAA,KAAAwB,CAAA,QAAAxB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAwB,GAAA,EAAAxB,CAAA,CAAAuD,iBAAA,CAAAvD,CAAA,CAAAwB,GAAA,uBAAAxB,CAAA,CAAAkD,MAAA,IAAAlD,CAAA,CAAAwD,MAAA,WAAAxD,CAAA,CAAAwB,GAAA,GAAAtB,CAAA,GAAA0B,CAAA,MAAAK,CAAA,GAAAX,QAAA,CAAA3B,CAAA,EAAAE,CAAA,EAAAG,CAAA,oBAAAiC,CAAA,CAAAV,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAiD,IAAA,GAAApB,CAAA,GAAAF,CAAA,EAAAM,CAAA,CAAAT,GAAA,KAAAM,CAAA,qBAAA1B,KAAA,EAAA6B,CAAA,CAAAT,GAAA,EAAAyB,IAAA,EAAAjD,CAAA,CAAAiD,IAAA,kBAAAhB,CAAA,CAAAV,IAAA,KAAArB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAkD,MAAA,YAAAlD,CAAA,CAAAwB,GAAA,GAAAS,CAAA,CAAAT,GAAA,mBAAA4B,oBAAAzD,CAAA,EAAAE,CAAA,QAAAG,CAAA,GAAAH,CAAA,CAAAqD,MAAA,EAAAhD,CAAA,GAAAP,CAAA,CAAAa,QAAA,CAAAR,CAAA,OAAAE,CAAA,KAAAN,CAAA,SAAAC,CAAA,CAAAsD,QAAA,qBAAAnD,CAAA,IAAAL,CAAA,CAAAa,QAAA,CAAAiD,MAAA,KAAA5D,CAAA,CAAAqD,MAAA,aAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,EAAAwD,mBAAA,CAAAzD,CAAA,EAAAE,CAAA,eAAAA,CAAA,CAAAqD,MAAA,kBAAAlD,CAAA,KAAAH,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,uCAAA1D,CAAA,iBAAA8B,CAAA,MAAAzB,CAAA,GAAAiB,QAAA,CAAApB,CAAA,EAAAP,CAAA,CAAAa,QAAA,EAAAX,CAAA,CAAA2B,GAAA,mBAAAnB,CAAA,CAAAkB,IAAA,SAAA1B,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,GAAAnB,CAAA,CAAAmB,GAAA,EAAA3B,CAAA,CAAAsD,QAAA,SAAArB,CAAA,MAAAvB,CAAA,GAAAF,CAAA,CAAAmB,GAAA,SAAAjB,CAAA,GAAAA,CAAA,CAAA0C,IAAA,IAAApD,CAAA,CAAAF,CAAA,CAAAgE,UAAA,IAAApD,CAAA,CAAAH,KAAA,EAAAP,CAAA,CAAA+D,IAAA,GAAAjE,CAAA,CAAAkE,OAAA,eAAAhE,CAAA,CAAAqD,MAAA,KAAArD,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,GAAAC,CAAA,CAAAsD,QAAA,SAAArB,CAAA,IAAAvB,CAAA,IAAAV,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,sCAAA7D,CAAA,CAAAsD,QAAA,SAAArB,CAAA,cAAAgC,aAAAlE,CAAA,QAAAD,CAAA,KAAAoE,MAAA,EAAAnE,CAAA,YAAAA,CAAA,KAAAD,CAAA,CAAAqE,QAAA,GAAApE,CAAA,WAAAA,CAAA,KAAAD,CAAA,CAAAsE,UAAA,GAAArE,CAAA,KAAAD,CAAA,CAAAuE,QAAA,GAAAtE,CAAA,WAAAuE,UAAA,CAAAC,IAAA,CAAAzE,CAAA,cAAA0E,cAAAzE,CAAA,QAAAD,CAAA,GAAAC,CAAA,CAAA0E,UAAA,QAAA3E,CAAA,CAAA4B,IAAA,oBAAA5B,CAAA,CAAA6B,GAAA,EAAA5B,CAAA,CAAA0E,UAAA,GAAA3E,CAAA,aAAAyB,QAAAxB,CAAA,SAAAuE,UAAA,MAAAJ,MAAA,aAAAnE,CAAA,CAAA4C,OAAA,CAAAsB,YAAA,cAAAS,KAAA,iBAAAlC,OAAA1C,CAAA,QAAAA,CAAA,WAAAA,CAAA,QAAAE,CAAA,GAAAF,CAAA,CAAAY,CAAA,OAAAV,CAAA,SAAAA,CAAA,CAAA4B,IAAA,CAAA9B,CAAA,4BAAAA,CAAA,CAAAiE,IAAA,SAAAjE,CAAA,OAAA6E,KAAA,CAAA7E,CAAA,CAAA8E,MAAA,SAAAvE,CAAA,OAAAG,CAAA,YAAAuD,KAAA,aAAA1D,CAAA,GAAAP,CAAA,CAAA8E,MAAA,OAAAzE,CAAA,CAAAyB,IAAA,CAAA9B,CAAA,EAAAO,CAAA,UAAA0D,IAAA,CAAAxD,KAAA,GAAAT,CAAA,CAAAO,CAAA,GAAA0D,IAAA,CAAAX,IAAA,OAAAW,IAAA,SAAAA,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,YAAAvD,CAAA,CAAAuD,IAAA,GAAAvD,CAAA,gBAAAqD,SAAA,QAAA/D,CAAA,iCAAAoC,iBAAA,CAAAhC,SAAA,GAAAiC,0BAAA,EAAA9B,CAAA,CAAAoC,CAAA,mBAAAlC,KAAA,EAAA4B,0BAAA,EAAAjB,YAAA,SAAAb,CAAA,CAAA8B,0BAAA,mBAAA5B,KAAA,EAAA2B,iBAAA,EAAAhB,YAAA,SAAAgB,iBAAA,CAAA2C,WAAA,GAAA7D,MAAA,CAAAmB,0BAAA,EAAArB,CAAA,wBAAAhB,CAAA,CAAAgF,mBAAA,aAAA/E,CAAA,QAAAD,CAAA,wBAAAC,CAAA,IAAAA,CAAA,CAAAgF,WAAA,WAAAjF,CAAA,KAAAA,CAAA,KAAAoC,iBAAA,6BAAApC,CAAA,CAAA+E,WAAA,IAAA/E,CAAA,CAAAkF,IAAA,OAAAlF,CAAA,CAAAmF,IAAA,aAAAlF,CAAA,WAAAE,MAAA,CAAAiF,cAAA,GAAAjF,MAAA,CAAAiF,cAAA,CAAAnF,CAAA,EAAAoC,0BAAA,KAAApC,CAAA,CAAAoF,SAAA,GAAAhD,0BAAA,EAAAnB,MAAA,CAAAjB,CAAA,EAAAe,CAAA,yBAAAf,CAAA,CAAAG,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAmB,CAAA,GAAA1C,CAAA,KAAAD,CAAA,CAAAsF,KAAA,aAAArF,CAAA,aAAAiD,OAAA,EAAAjD,CAAA,OAAA2C,qBAAA,CAAAG,aAAA,CAAA3C,SAAA,GAAAc,MAAA,CAAA6B,aAAA,CAAA3C,SAAA,EAAAU,CAAA,iCAAAd,CAAA,CAAA+C,aAAA,GAAAA,aAAA,EAAA/C,CAAA,CAAAuF,KAAA,aAAAtF,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,EAAAG,CAAA,eAAAA,CAAA,KAAAA,CAAA,GAAA8E,OAAA,OAAA5E,CAAA,OAAAmC,aAAA,CAAAzB,IAAA,CAAArB,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,GAAAG,CAAA,UAAAV,CAAA,CAAAgF,mBAAA,CAAA9E,CAAA,IAAAU,CAAA,GAAAA,CAAA,CAAAqD,IAAA,GAAAd,IAAA,WAAAlD,CAAA,WAAAA,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAQ,KAAA,GAAAG,CAAA,CAAAqD,IAAA,WAAArB,qBAAA,CAAAD,CAAA,GAAAzB,MAAA,CAAAyB,CAAA,EAAA3B,CAAA,gBAAAE,MAAA,CAAAyB,CAAA,EAAA/B,CAAA,iCAAAM,MAAA,CAAAyB,CAAA,6DAAA3C,CAAA,CAAAyF,IAAA,aAAAxF,CAAA,QAAAD,CAAA,GAAAG,MAAA,CAAAF,CAAA,GAAAC,CAAA,gBAAAG,CAAA,IAAAL,CAAA,EAAAE,CAAA,CAAAuE,IAAA,CAAApE,CAAA,UAAAH,CAAA,CAAAwF,OAAA,aAAAzB,KAAA,WAAA/D,CAAA,CAAA4E,MAAA,SAAA7E,CAAA,GAAAC,CAAA,CAAAyF,GAAA,QAAA1F,CAAA,IAAAD,CAAA,SAAAiE,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,WAAAA,IAAA,CAAAX,IAAA,OAAAW,IAAA,QAAAjE,CAAA,CAAA0C,MAAA,GAAAA,MAAA,EAAAjB,OAAA,CAAArB,SAAA,KAAA6E,WAAA,EAAAxD,OAAA,EAAAmD,KAAA,WAAAA,MAAA5E,CAAA,aAAA4F,IAAA,WAAA3B,IAAA,WAAAP,IAAA,QAAAC,KAAA,GAAA1D,CAAA,OAAAqD,IAAA,YAAAE,QAAA,cAAAD,MAAA,gBAAA1B,GAAA,GAAA5B,CAAA,OAAAuE,UAAA,CAAA3B,OAAA,CAAA6B,aAAA,IAAA1E,CAAA,WAAAE,CAAA,kBAAAA,CAAA,CAAA2F,MAAA,OAAAxF,CAAA,CAAAyB,IAAA,OAAA5B,CAAA,MAAA2E,KAAA,EAAA3E,CAAA,CAAA4F,KAAA,cAAA5F,CAAA,IAAAD,CAAA,MAAA8F,IAAA,WAAAA,KAAA,SAAAzC,IAAA,WAAArD,CAAA,QAAAuE,UAAA,IAAAG,UAAA,kBAAA1E,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,cAAAmE,IAAA,KAAApC,iBAAA,WAAAA,kBAAA5D,CAAA,aAAAsD,IAAA,QAAAtD,CAAA,MAAAE,CAAA,kBAAA+F,OAAA5F,CAAA,EAAAE,CAAA,WAAAK,CAAA,CAAAgB,IAAA,YAAAhB,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAE,CAAA,CAAA+D,IAAA,GAAA5D,CAAA,EAAAE,CAAA,KAAAL,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,KAAAM,CAAA,aAAAA,CAAA,QAAAiE,UAAA,CAAAM,MAAA,MAAAvE,CAAA,SAAAA,CAAA,QAAAG,CAAA,QAAA8D,UAAA,CAAAjE,CAAA,GAAAK,CAAA,GAAAF,CAAA,CAAAiE,UAAA,iBAAAjE,CAAA,CAAA0D,MAAA,SAAA6B,MAAA,aAAAvF,CAAA,CAAA0D,MAAA,SAAAwB,IAAA,QAAA9E,CAAA,GAAAT,CAAA,CAAAyB,IAAA,CAAApB,CAAA,eAAAM,CAAA,GAAAX,CAAA,CAAAyB,IAAA,CAAApB,CAAA,qBAAAI,CAAA,IAAAE,CAAA,aAAA4E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,gBAAAuB,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,cAAAxD,CAAA,aAAA8E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,qBAAArD,CAAA,QAAAqC,KAAA,qDAAAuC,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,YAAAT,MAAA,WAAAA,OAAA5D,CAAA,EAAAD,CAAA,aAAAE,CAAA,QAAAsE,UAAA,CAAAM,MAAA,MAAA5E,CAAA,SAAAA,CAAA,QAAAK,CAAA,QAAAiE,UAAA,CAAAtE,CAAA,OAAAK,CAAA,CAAA6D,MAAA,SAAAwB,IAAA,IAAAvF,CAAA,CAAAyB,IAAA,CAAAvB,CAAA,wBAAAqF,IAAA,GAAArF,CAAA,CAAA+D,UAAA,QAAA5D,CAAA,GAAAH,CAAA,aAAAG,CAAA,iBAAAT,CAAA,mBAAAA,CAAA,KAAAS,CAAA,CAAA0D,MAAA,IAAApE,CAAA,IAAAA,CAAA,IAAAU,CAAA,CAAA4D,UAAA,KAAA5D,CAAA,cAAAE,CAAA,GAAAF,CAAA,GAAAA,CAAA,CAAAiE,UAAA,cAAA/D,CAAA,CAAAgB,IAAA,GAAA3B,CAAA,EAAAW,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAU,CAAA,SAAA6C,MAAA,gBAAAU,IAAA,GAAAvD,CAAA,CAAA4D,UAAA,EAAAnC,CAAA,SAAA+D,QAAA,CAAAtF,CAAA,MAAAsF,QAAA,WAAAA,SAAAjG,CAAA,EAAAD,CAAA,oBAAAC,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,qBAAA5B,CAAA,CAAA2B,IAAA,mBAAA3B,CAAA,CAAA2B,IAAA,QAAAqC,IAAA,GAAAhE,CAAA,CAAA4B,GAAA,gBAAA5B,CAAA,CAAA2B,IAAA,SAAAoE,IAAA,QAAAnE,GAAA,GAAA5B,CAAA,CAAA4B,GAAA,OAAA0B,MAAA,kBAAAU,IAAA,yBAAAhE,CAAA,CAAA2B,IAAA,IAAA5B,CAAA,UAAAiE,IAAA,GAAAjE,CAAA,GAAAmC,CAAA,KAAAgE,MAAA,WAAAA,OAAAlG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAoE,UAAA,KAAArE,CAAA,cAAAiG,QAAA,CAAAhG,CAAA,CAAAyE,UAAA,EAAAzE,CAAA,CAAAqE,QAAA,GAAAG,aAAA,CAAAxE,CAAA,GAAAiC,CAAA,OAAAiE,KAAA,WAAAC,OAAApG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAkE,MAAA,KAAAnE,CAAA,QAAAI,CAAA,GAAAH,CAAA,CAAAyE,UAAA,kBAAAtE,CAAA,CAAAuB,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAwB,GAAA,EAAA6C,aAAA,CAAAxE,CAAA,YAAAK,CAAA,YAAA8C,KAAA,8BAAAiD,aAAA,WAAAA,cAAAtG,CAAA,EAAAE,CAAA,EAAAG,CAAA,gBAAAmD,QAAA,KAAA3C,QAAA,EAAA6B,MAAA,CAAA1C,CAAA,GAAAgE,UAAA,EAAA9D,CAAA,EAAAgE,OAAA,EAAA7D,CAAA,oBAAAkD,MAAA,UAAA1B,GAAA,GAAA5B,CAAA,GAAAkC,CAAA,OAAAnC,CAAA;AAAA,SAAAuG,mBAAAlG,CAAA,EAAAJ,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAK,CAAA,EAAAK,CAAA,EAAAE,CAAA,cAAAJ,CAAA,GAAAL,CAAA,CAAAO,CAAA,EAAAE,CAAA,GAAAE,CAAA,GAAAN,CAAA,CAAAD,KAAA,WAAAJ,CAAA,gBAAAL,CAAA,CAAAK,CAAA,KAAAK,CAAA,CAAA4C,IAAA,GAAArD,CAAA,CAAAe,CAAA,IAAAwE,OAAA,CAAAvC,OAAA,CAAAjC,CAAA,EAAAmC,IAAA,CAAAjD,CAAA,EAAAK,CAAA;AAAA,SAAAiG,kBAAAnG,CAAA,6BAAAJ,CAAA,SAAAD,CAAA,GAAAyG,SAAA,aAAAjB,OAAA,WAAAtF,CAAA,EAAAK,CAAA,QAAAK,CAAA,GAAAP,CAAA,CAAAqG,KAAA,CAAAzG,CAAA,EAAAD,CAAA,YAAA2G,MAAAtG,CAAA,IAAAkG,kBAAA,CAAA3F,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAoG,KAAA,EAAAC,MAAA,UAAAvG,CAAA,cAAAuG,OAAAvG,CAAA,IAAAkG,kBAAA,CAAA3F,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAoG,KAAA,EAAAC,MAAA,WAAAvG,CAAA,KAAAsG,KAAA;AAAA,SAAAE,mBAAA3G,CAAA,WAAA4G,kBAAA,CAAA5G,CAAA,KAAA6G,gBAAA,CAAA7G,CAAA,KAAA8G,2BAAA,CAAA9G,CAAA,KAAA+G,kBAAA;AAAA,SAAAA,mBAAA,cAAAlD,SAAA;AAAA,SAAAiD,4BAAA9G,CAAA,EAAAU,CAAA,QAAAV,CAAA,2BAAAA,CAAA,SAAAgH,iBAAA,CAAAhH,CAAA,EAAAU,CAAA,OAAAX,CAAA,MAAAkH,QAAA,CAAArF,IAAA,CAAA5B,CAAA,EAAA4F,KAAA,6BAAA7F,CAAA,IAAAC,CAAA,CAAA+E,WAAA,KAAAhF,CAAA,GAAAC,CAAA,CAAA+E,WAAA,CAAAC,IAAA,aAAAjF,CAAA,cAAAA,CAAA,GAAAmH,KAAA,CAAAC,IAAA,CAAAnH,CAAA,oBAAAD,CAAA,+CAAAqH,IAAA,CAAArH,CAAA,IAAAiH,iBAAA,CAAAhH,CAAA,EAAAU,CAAA;AAAA,SAAAmG,iBAAA7G,CAAA,8BAAAS,MAAA,YAAAT,CAAA,CAAAS,MAAA,CAAAE,QAAA,aAAAX,CAAA,uBAAAkH,KAAA,CAAAC,IAAA,CAAAnH,CAAA;AAAA,SAAA4G,mBAAA5G,CAAA,QAAAkH,KAAA,CAAAG,OAAA,CAAArH,CAAA,UAAAgH,iBAAA,CAAAhH,CAAA;AAAA,SAAAgH,kBAAAhH,CAAA,EAAAU,CAAA,aAAAA,CAAA,IAAAA,CAAA,GAAAV,CAAA,CAAA4E,MAAA,MAAAlE,CAAA,GAAAV,CAAA,CAAA4E,MAAA,YAAA9E,CAAA,MAAAK,CAAA,GAAA+G,KAAA,CAAAxG,CAAA,GAAAZ,CAAA,GAAAY,CAAA,EAAAZ,CAAA,IAAAK,CAAA,CAAAL,CAAA,IAAAE,CAAA,CAAAF,CAAA,UAAAK,CAAA;AADA,OAAOmH,GAAG,MAAM,OAAO;AACvB,SAASC,QAAQ,EAAEC,GAAG,EAAEC,WAAW,EAAEC,KAAK,QAAQ,KAAK;AACvD,SAASC,wBAAwB,QAAQ,yBAAyB;AAClE,SAASC,SAAS,QAAQ,cAAc;AACxC,OAAOC,oBAAoB,MAAM,4DAA4D;AAC7F,OAAOC,oBAAoB,MAAM,4DAA4D;AAR7F,IAAAC,WAAA,GAAe;EAAE/C,IAAI,EAAE;AAAsB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAS9C,IAAMgD,KAAK,GAAGC,OAMZ;IACF,IAAMC,IAAI,GAAGC,MAAyB;IACtC,IAAMC,cAAc,GAAGZ,GAAG,CAAC,EAAE,CAAC;IAC9B,IAAMa,OAAO,GAAGb,GAAG,CAAC,CAAC;IACrB,IAAMc,IAAI,GAAGf,QAAQ,CAAC;MACpBgB,YAAY,EAAE,EAAE;MAAE;MAClBC,cAAc,EAAE,EAAE;MAAE;MACpBC,gBAAgB,EAAE,EAAE;MAAE;MACtBC,aAAa,EAAE,EAAE;MAAE;MACnBC,YAAY,EAAE,EAAE;MAAE;MAClBC,kBAAkB,EAAE,EAAE;MACtBC,eAAe,EAAE,EAAE;MACnBC,cAAc,EAAE,EAAE;MAClBC,cAAc,EAAE,EAAE;MAClBC,eAAe,EAAE;IACnB,CAAC,CAAC;IACF,IAAMC,KAAK,GAAG1B,QAAQ,CAAC;MACrBgB,YAAY,EAAE,CAAC;QAAEW,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,SAAS;QAAEC,OAAO,EAAE,CAAC,MAAM,EAAE,QAAQ;MAAE,CAAC,CAAC;MACnFZ,cAAc,EAAE,CAAC;QAAEU,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,SAAS;QAAEC,OAAO,EAAE,CAAC,MAAM,EAAE,QAAQ;MAAE,CAAC,CAAC;MACrFT,YAAY,EAAE,CAAC;QAAEO,QAAQ,EAAE,KAAK;QAAEC,OAAO,EAAE,SAAS;QAAEC,OAAO,EAAE,CAAC,MAAM,EAAE,QAAQ;MAAE,CAAC,CAAC;MACpFR,kBAAkB,EAAE,CAAC;QAAElH,IAAI,EAAE,OAAO;QAAEwH,QAAQ,EAAE,KAAK;QAAEC,OAAO,EAAE,SAAS;QAAEC,OAAO,EAAE,CAAC,MAAM,EAAE,QAAQ;MAAE,CAAC,CAAC;MACzGP,eAAe,EAAE,CAAC;QAAEnH,IAAI,EAAE,OAAO;QAAEwH,QAAQ,EAAE,KAAK;QAAEC,OAAO,EAAE,SAAS;QAAEC,OAAO,EAAE,CAAC,MAAM,EAAE,QAAQ;MAAE,CAAC,CAAC;MACtGN,cAAc,EAAE,CAAC;QAAEI,QAAQ,EAAE,KAAK;QAAEC,OAAO,EAAE,WAAW;QAAEC,OAAO,EAAE,CAAC,MAAM,EAAE,QAAQ;MAAE,CAAC,CAAC;MACxFL,cAAc,EAAE,CAAC;QAAEG,QAAQ,EAAE,KAAK;QAAEC,OAAO,EAAE,WAAW;QAAEC,OAAO,EAAE,CAAC,MAAM,EAAE,QAAQ;MAAE,CAAC,CAAC;MACxFJ,eAAe,EAAE,CAAC;QAAEE,QAAQ,EAAE,KAAK;QAAEC,OAAO,EAAE,WAAW;QAAEC,OAAO,EAAE,CAAC,MAAM,EAAE,QAAQ;MAAE,CAAC;IAC1F,CAAC,CAAC;IACF,IAAMC,YAAY,GAAG,SAAfA,YAAYA,CAAIC,IAAI;MAAA,OAAKA,IAAI,CAACC,OAAO,CAAC,CAAC,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG,CAAC;IAAA;IACjF,IAAMC,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAIJ,IAAI;MAAA,OAAKA,IAAI,CAACC,OAAO,CAAC,CAAC,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI,GAAG,IAAI,GAAG,EAAE;IAAA;IACnF,IAAME,UAAU,GAAGnC,GAAG,CAAC,CAAC,CAAC,CAAC;IAC1B,IAAMe,YAAY,GAAGf,GAAG,CAAC,EAAE,CAAC;IAC5B,IAAMgB,cAAc,GAAGhB,GAAG,CAAC,EAAE,CAAC;IAC9B,IAAMiB,gBAAgB,GAAGjB,GAAG,CAAC,EAAE,CAAC;IAChC;;IAEA,IAAMoC,UAAU,GAAGpC,GAAG,CAAC,KAAK,CAAC;IAC7B,IAAMqC,eAAe,GAAGrC,GAAG,CAAC,KAAK,CAAC;IAClC,IAAMsC,UAAU,GAAGtC,GAAG,CAAC,KAAK,CAAC;IAC7B,IAAMuC,eAAe,GAAGvC,GAAG,CAAC,KAAK,CAAC;IAElCC,WAAW,CAAC,YAAM;MAChBuC,gBAAgB,CAAC,CAAC;MAClBC,qBAAqB,CAAC,CAAC;MACvB,IAAIjC,KAAK,CAACkC,EAAE,EAAE;QACZC,mBAAmB,CAAC,CAAC;MACvB,CAAC,MAAM;QACL,IAAMC,WAAW,GAAG,IAAIZ,IAAI,CAAC,CAAC;QAC9BY,WAAW,CAACC,OAAO,CAACD,WAAW,CAACE,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC,EAAC;QAChD;QACAhC,IAAI,CAACU,eAAe,GAAGoB,WAAW;QAClCG,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEJ,WAAW,CAAC;MAC7C;IACF,CAAC,CAAC;IAEF,IAAMK,YAAY,GAAG,SAAfA,YAAYA,CAAIC,UAAU,EAAEC,eAAe,EAAK;MACpDf,UAAU,CAACrJ,KAAK,GAAGmK,UAAU;MAC7Bb,eAAe,CAACtJ,KAAK,GAAGoK,eAAe;IACzC,CAAC;IACD,IAAMC,UAAU,GAAG,SAAbA,UAAUA,CAAIC,IAAI,EAAEX,EAAE,EAAK;MAC/B,IAAIA,EAAE,EAAE;QACN5B,IAAI,CAACE,cAAc,GAAG0B,EAAE;QACxBY,oBAAoB,CAAC,CAAC;QACtBxC,IAAI,CAACG,gBAAgB,GAAGoC,IAAI,CAACE,GAAG;MAClC,CAAC,MAAM;QACLzC,IAAI,CAACE,cAAc,GAAGqC,IAAI,CAACE,GAAG;QAC9BD,oBAAoB,CAAC,CAAC;MACxB;IACF,CAAC;IACD,IAAME,YAAY,GAAG,SAAfA,YAAYA,CAAIN,UAAU,EAAEC,eAAe,EAAK;MACpDb,UAAU,CAACvJ,KAAK,GAAGmK,UAAU;MAC7BX,eAAe,CAACxJ,KAAK,GAAGoK,eAAe;IACzC,CAAC;IACD,IAAMM,UAAU,GAAG,SAAbA,UAAUA,CAAIJ,IAAI,EAAK;MAC3B,IAAIvC,IAAI,CAACK,YAAY,KAAK,aAAa,EAAE;QACvC,IAAI,CAACL,IAAI,CAACM,kBAAkB,CAAChE,MAAM,EAAE;UACnC,IAAI,CAAC0D,IAAI,CAACO,eAAe,CAACqC,QAAQ,CAACL,IAAI,CAACX,EAAE,CAAC,EAAE;YAC3C5B,IAAI,CAACM,kBAAkB,GAAG,CAACiC,IAAI,CAACX,EAAE,CAAC;YACnCtC,SAAS,CAAC;cAAElG,IAAI,EAAE,SAAS;cAAEyH,OAAO,EAAE,QAAQ0B,IAAI,CAAC7F,IAAI;YAAW,CAAC,CAAC;UACtE;QACF,CAAC,MAAM;UACL,IAAI,CAACsD,IAAI,CAACM,kBAAkB,CAACsC,QAAQ,CAACL,IAAI,CAACX,EAAE,CAAC,IAAI,CAAC5B,IAAI,CAACO,eAAe,CAACqC,QAAQ,CAACL,IAAI,CAACX,EAAE,CAAC,EAAE;YACzF5B,IAAI,CAACO,eAAe,MAAAsC,MAAA,CAAAxE,kBAAA,CAAO2B,IAAI,CAACO,eAAe,IAAEgC,IAAI,CAACX,EAAE,EAAC;YACzDtC,SAAS,CAAC;cAAElG,IAAI,EAAE,SAAS;cAAEyH,OAAO,EAAE,kBAAkB0B,IAAI,CAAC7F,IAAI;YAAW,CAAC,CAAC;UAChF;QACF;MACF,CAAC,MAAM,IAAIsD,IAAI,CAACK,YAAY,KAAK,SAAS,EAAE;QAC1C,IAAI,CAACL,IAAI,CAACO,eAAe,CAACqC,QAAQ,CAACL,IAAI,CAACX,EAAE,CAAC,EAAE;UAC3C5B,IAAI,CAACO,eAAe,MAAAsC,MAAA,CAAAxE,kBAAA,CAAO2B,IAAI,CAACO,eAAe,IAAEgC,IAAI,CAACX,EAAE,EAAC;UACzDtC,SAAS,CAAC;YAAElG,IAAI,EAAE,SAAS;YAAEyH,OAAO,EAAE,OAAO0B,IAAI,CAAC7F,IAAI;UAAU,CAAC,CAAC;QACpE;MACF;IACF,CAAC;IAED,IAAMgF,gBAAgB;MAAA,IAAAoB,KAAA,GAAA9E,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAoG,QAAA;QAAA,IAAAC,qBAAA,EAAAC,IAAA;QAAA,OAAA1L,mBAAA,GAAAuB,IAAA,UAAAoK,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAA/F,IAAA,GAAA+F,QAAA,CAAA1H,IAAA;YAAA;cAAA0H,QAAA,CAAA1H,IAAA;cAAA,OACAuD,GAAG,CAAC0C,gBAAgB,CAAC;gBAC1C0B,KAAK,EAAE,CAAC,iBAAiB,EAAE,kBAAkB,EAAE,mBAAmB,EAAE,kBAAkB;cACxF,CAAC,CAAC;YAAA;cAAAJ,qBAAA,GAAAG,QAAA,CAAAjI,IAAA;cAFM+H,IAAI,GAAAD,qBAAA,CAAJC,IAAI;cAGZ,IAAIA,IAAI,CAACI,gBAAgB,EAAE;gBACzBrD,IAAI,CAACQ,cAAc,GAAGU,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI,GAAG,IAAI,GAAG,EAAE,GAAGmC,MAAM,CAACL,IAAI,CAACI,gBAAgB,CAAC;cACrF;cACA,IAAIJ,IAAI,CAACM,iBAAiB,EAAE;gBAC1BvD,IAAI,CAACS,cAAc,GAAGS,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI,GAAG,IAAI,GAAG,EAAE,GAAGmC,MAAM,CAACL,IAAI,CAACM,iBAAiB,CAAC;cACtF;cACA,IAAIN,IAAI,CAACO,eAAe,EAAE;gBACxBxD,IAAI,CAACU,eAAe,GAAGQ,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI,GAAG,IAAI,GAAG,EAAE,GAAGmC,MAAM,CAACL,IAAI,CAACO,eAAe,CAAC;cACrF;cACA,IAAIP,IAAI,CAACQ,gBAAgB,IAAI,IAAIvC,IAAI,CAAC,CAAC,CAACD,OAAO,CAAC,CAAC,GAAG,IAAIC,IAAI,CAAC+B,IAAI,CAACQ,gBAAgB,CAAC,CAACxC,OAAO,CAAC,CAAC,EAAE;gBAC7FjB,IAAI,CAACQ,cAAc,GAAG,IAAIU,IAAI,CAAC+B,IAAI,CAACQ,gBAAgB,CAAC,CAACxC,OAAO,CAAC,CAAC;cACjE;YAAC;YAAA;cAAA,OAAAkC,QAAA,CAAA5F,IAAA;UAAA;QAAA,GAAAwF,OAAA;MAAA,CACF;MAAA,gBAhBKrB,gBAAgBA,CAAA;QAAA,OAAAoB,KAAA,CAAA5E,KAAA,OAAAD,SAAA;MAAA;IAAA,GAgBrB;IACD,IAAM4D,mBAAmB;MAAA,IAAA6B,KAAA,GAAA1F,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAgH,SAAA;QAAA,IAAAC,GAAA,EAAAX,IAAA;QAAA,OAAA1L,mBAAA,GAAAuB,IAAA,UAAA+K,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA1G,IAAA,GAAA0G,SAAA,CAAArI,IAAA;YAAA;cAAAqI,SAAA,CAAArI,IAAA;cAAA,OACRuD,GAAG,CAAC6C,mBAAmB,CAAC;gBAAEkC,YAAY,EAAErE,KAAK,CAACkC;cAAG,CAAC,CAAC;YAAA;cAA/DgC,GAAG,GAAAE,SAAA,CAAA5I,IAAA;cACH+H,IAAI,GAAKW,GAAG,CAAZX,IAAI;cACVjD,IAAI,CAACC,YAAY,GAAGgD,IAAI,CAAC,CAAC,CAAC,CAACe,MAAM;cAClC/D,YAAY,CAAChI,KAAK,GAAGgL,IAAI;cACzBgB,kBAAkB,CAAC,CAAC;YAAA;YAAA;cAAA,OAAAH,SAAA,CAAAvG,IAAA;UAAA;QAAA,GAAAoG,QAAA;MAAA,CACrB;MAAA,gBANK9B,mBAAmBA,CAAA;QAAA,OAAA6B,KAAA,CAAAxF,KAAA,OAAAD,SAAA;MAAA;IAAA,GAMxB;IACD,IAAMgG,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAA,EAAS;MAC/BnE,cAAc,CAAC7H,KAAK,GAAG,EAAE;MACzB0I,KAAK,CAACT,cAAc,GAAG,CAAC;QAAEU,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,SAAS;QAAEC,OAAO,EAAE,CAAC,MAAM,EAAE,QAAQ;MAAE,CAAC,CAAC;MAC5FH,KAAK,CAACN,YAAY,GAAG,CAAC;QAAEO,QAAQ,EAAE,KAAK;QAAEC,OAAO,EAAE,SAAS;QAAEC,OAAO,EAAE,CAAC,MAAM,EAAE,QAAQ;MAAE,CAAC,CAAC;MAC3FH,KAAK,CAACH,cAAc,GAAG,CAAC;QAAEI,QAAQ,EAAE,KAAK;QAAEC,OAAO,EAAE,WAAW;QAAEC,OAAO,EAAE,CAAC,MAAM,EAAE,QAAQ;MAAE,CAAC,CAAC;MAC/FH,KAAK,CAACF,cAAc,GAAG,CAAC;QAAEG,QAAQ,EAAE,KAAK;QAAEC,OAAO,EAAE,WAAW;QAAEC,OAAO,EAAE,CAAC,MAAM,EAAE,QAAQ;MAAE,CAAC,CAAC;MAC/F,KAAK,IAAIoD,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGjE,YAAY,CAAChI,KAAK,CAACqE,MAAM,EAAE4H,KAAK,EAAE,EAAE;QAC9D,IAAM3B,IAAI,GAAGtC,YAAY,CAAChI,KAAK,CAACiM,KAAK,CAAC;QACtC,IAAI3B,IAAI,CAACyB,MAAM,KAAKhE,IAAI,CAACC,YAAY,EAAE;UACrCH,cAAc,CAAC7H,KAAK,GAAGsK,IAAI,CAAC4B,QAAQ;UACpC,IAAI5B,IAAI,CAAC4B,QAAQ,KAAK,SAAS,EAAE;YAC/BxD,KAAK,CAACN,YAAY,GAAG,CAAC;cAAEO,QAAQ,EAAE,IAAI;cAAEC,OAAO,EAAE,SAAS;cAAEC,OAAO,EAAE,CAAC,MAAM,EAAE,QAAQ;YAAE,CAAC,CAAC;YAC1FH,KAAK,CAACH,cAAc,GAAG,CAAC;cAAEI,QAAQ,EAAE,IAAI;cAAEC,OAAO,EAAE,WAAW;cAAEC,OAAO,EAAE,CAAC,MAAM,EAAE,QAAQ;YAAE,CAAC,CAAC;YAC9FH,KAAK,CAACF,cAAc,GAAG,CAAC;cAAEG,QAAQ,EAAE,IAAI;cAAEC,OAAO,EAAE,WAAW;cAAEC,OAAO,EAAE,CAAC,MAAM,EAAE,QAAQ;YAAE,CAAC,CAAC;UAChG;UACA,IAAIyB,IAAI,CAAC4B,QAAQ,KAAK,WAAW,EAAE;YACjCxD,KAAK,CAACN,YAAY,GAAG,CAAC;cAAEO,QAAQ,EAAE,IAAI;cAAEC,OAAO,EAAE,SAAS;cAAEC,OAAO,EAAE,CAAC,MAAM,EAAE,QAAQ;YAAE,CAAC,CAAC;YAC1FH,KAAK,CAACD,eAAe,GAAG,CAAC;cAAEE,QAAQ,EAAE,IAAI;cAAEC,OAAO,EAAE,WAAW;cAAEC,OAAO,EAAE,CAAC,MAAM,EAAE,QAAQ;YAAE,CAAC,CAAC;UACjG;UACA,IAAIyB,IAAI,CAAC4B,QAAQ,KAAK,UAAU,EAAE;YAChCxD,KAAK,CAACT,cAAc,GAAG,CAAC;cAAEU,QAAQ,EAAE,KAAK;cAAEC,OAAO,EAAE,SAAS;cAAEC,OAAO,EAAE,CAAC,MAAM,EAAE,QAAQ;YAAE,CAAC,CAAC;UAC/F;QACF;MACF;IACF,CAAC;IACD,IAAMa,qBAAqB;MAAA,IAAAyC,KAAA,GAAApG,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAA0H,SAAA;QAAA,IAAAT,GAAA,EAAAX,IAAA;QAAA,OAAA1L,mBAAA,GAAAuB,IAAA,UAAAwL,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAnH,IAAA,GAAAmH,SAAA,CAAA9I,IAAA;YAAA;cAAA8I,SAAA,CAAA9I,IAAA;cAAA,OACVuD,GAAG,CAAC2C,qBAAqB,CAAC;gBAAE6C,KAAK,EAAE;kBAAEC,OAAO,EAAE;gBAAE;cAAE,CAAC,CAAC;YAAA;cAAhEb,GAAG,GAAAW,SAAA,CAAArJ,IAAA;cACH+H,IAAI,GAAKW,GAAG,CAAZX,IAAI;cACV/C,cAAc,CAACjI,KAAK,GAAGgL,IAAI;cAC3BT,oBAAoB,CAAC,CAAC;YAAA;YAAA;cAAA,OAAA+B,SAAA,CAAAhH,IAAA;UAAA;QAAA,GAAA8G,QAAA;MAAA,CACvB;MAAA,gBALK1C,qBAAqBA,CAAA;QAAA,OAAAyC,KAAA,CAAAlG,KAAA,OAAAD,SAAA;MAAA;IAAA,GAK1B;IACD,IAAMuE,oBAAoB,GAAG,SAAvBA,oBAAoBA,CAAA,EAAS;MACjC,IAAIxC,IAAI,CAACE,cAAc,EAAE;QACvB,KAAK,IAAIgE,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGhE,cAAc,CAACjI,KAAK,CAACqE,MAAM,EAAE4H,KAAK,EAAE,EAAE;UAChE,IAAM3B,IAAI,GAAGrC,cAAc,CAACjI,KAAK,CAACiM,KAAK,CAAC;UACxC,IAAI3B,IAAI,CAACX,EAAE,KAAK5B,IAAI,CAACE,cAAc,EAAE;YACnC,IAAI,CAACqC,IAAI,CAACmC,QAAQ,CAACC,GAAG,CAAC,UAAC1K,CAAC;cAAA,OAAKA,CAAC,CAAC2H,EAAE;YAAA,EAAC,CAACgB,QAAQ,CAAC5C,IAAI,CAACG,gBAAgB,CAAC,EAAE;cACnEH,IAAI,CAACG,gBAAgB,GAAG,EAAE;YAC5B;YACAA,gBAAgB,CAAClI,KAAK,GAAGsK,IAAI,CAACmC,QAAQ;UACxC;QACF;MACF,CAAC,MAAM;QACL1E,IAAI,CAACG,gBAAgB,GAAG,EAAE;QAC1BA,gBAAgB,CAAClI,KAAK,GAAG,EAAE;MAC7B;IACF,CAAC;IACD,IAAM2M,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAA,EAAS;MAC/B,IAAI5E,IAAI,CAACK,YAAY,KAAK,aAAa,EAAE;QACvCM,KAAK,CAACL,kBAAkB,GAAG,CACzB;UAAElH,IAAI,EAAE,OAAO;UAAEwH,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,SAAS;UAAEC,OAAO,EAAE,CAAC,MAAM,EAAE,QAAQ;QAAE,CAAC,CACnF;QACDH,KAAK,CAACJ,eAAe,GAAG,CAAC;UAAEnH,IAAI,EAAE,OAAO;UAAEwH,QAAQ,EAAE,KAAK;UAAEC,OAAO,EAAE,SAAS;UAAEC,OAAO,EAAE,CAAC,MAAM,EAAE,QAAQ;QAAE,CAAC,CAAC;MAC/G,CAAC,MAAM,IAAId,IAAI,CAACK,YAAY,KAAK,SAAS,EAAE;QAC1CM,KAAK,CAACL,kBAAkB,GAAG,CACzB;UAAElH,IAAI,EAAE,OAAO;UAAEwH,QAAQ,EAAE,KAAK;UAAEC,OAAO,EAAE,SAAS;UAAEC,OAAO,EAAE,CAAC,MAAM,EAAE,QAAQ;QAAE,CAAC,CACpF;QACDH,KAAK,CAACJ,eAAe,GAAG,CAAC;UAAEnH,IAAI,EAAE,OAAO;UAAEwH,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,SAAS;UAAEC,OAAO,EAAE,CAAC,MAAM,EAAE,QAAQ;QAAE,CAAC,CAAC;MAC9G,CAAC,MAAM;QACLH,KAAK,CAACL,kBAAkB,GAAG,CACzB;UAAElH,IAAI,EAAE,OAAO;UAAEwH,QAAQ,EAAE,KAAK;UAAEC,OAAO,EAAE,SAAS;UAAEC,OAAO,EAAE,CAAC,MAAM,EAAE,QAAQ;QAAE,CAAC,CACpF;QACDH,KAAK,CAACJ,eAAe,GAAG,CAAC;UAAEnH,IAAI,EAAE,OAAO;UAAEwH,QAAQ,EAAE,KAAK;UAAEC,OAAO,EAAE,SAAS;UAAEC,OAAO,EAAE,CAAC,MAAM,EAAE,QAAQ;QAAE,CAAC,CAAC;MAC/G;IACF,CAAC;IACD,IAAM+D,UAAU;MAAA,IAAAC,KAAA,GAAA9G,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAoI,SAAOC,MAAM;QAAA,OAAAzN,mBAAA,GAAAuB,IAAA,UAAAmM,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA9H,IAAA,GAAA8H,SAAA,CAAAzJ,IAAA;YAAA;cAAA,IACzBuJ,MAAM;gBAAAE,SAAA,CAAAzJ,IAAA;gBAAA;cAAA;cAAA,OAAAyJ,SAAA,CAAA7J,MAAA;YAAA;cAAA6J,SAAA,CAAAzJ,IAAA;cAAA,OACLuJ,MAAM,CAACG,QAAQ,CAAC,UAACC,KAAK,EAAEC,MAAM,EAAK;gBACvC,IAAID,KAAK,EAAE;kBACTE,kBAAkB,CAAC,CAAC;gBACtB,CAAC,MAAM;kBACLhG,SAAS,CAAC;oBAAElG,IAAI,EAAE,SAAS;oBAAEyH,OAAO,EAAE;kBAAiB,CAAC,CAAC;gBAC3D;cACF,CAAC,CAAC;YAAA;YAAA;cAAA,OAAAqE,SAAA,CAAA3H,IAAA;UAAA;QAAA,GAAAwH,QAAA;MAAA,CACH;MAAA,gBATKF,UAAUA,CAAAU,EAAA;QAAA,OAAAT,KAAA,CAAA5G,KAAA,OAAAD,SAAA;MAAA;IAAA,GASf;IAED,IAAMqH,kBAAkB;MAAA,IAAAE,KAAA,GAAAxH,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAA8I,SAAA;QAAA,IAAAC,qBAAA,EAAAC,IAAA;QAAA,OAAApO,mBAAA,GAAAuB,IAAA,UAAA8M,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAzI,IAAA,GAAAyI,SAAA,CAAApK,IAAA;YAAA;cAAAoK,SAAA,CAAApK,IAAA;cAAA,OACFuD,GAAG,CAACsG,kBAAkB,CAAC;gBAC5CvB,YAAY,EAAErE,KAAK,CAACkC,EAAE;gBACtBkE,UAAU,EAAE9F,IAAI,CAACC,YAAY;gBAAE;gBAC/B8F,UAAU,EAAE/F,IAAI,CAACE,cAAc;gBAAE;gBACjC8F,YAAY,EAAEhG,IAAI,CAACG,gBAAgB;gBAAE;gBACrC8F,QAAQ,EAAE;kBACRC,aAAa,EAAElG,IAAI,CAACI,aAAa,CAAC;gBACpC,CAAC;gBACD+F,gBAAgB,EACdrG,cAAc,CAAC7H,KAAK,KAAK,SAAS,IAAI6H,cAAc,CAAC7H,KAAK,KAAK,WAAW,GAAG+H,IAAI,CAACK,YAAY,GAAG,IAAI;gBAAE;gBACzGC,kBAAkB,EAChBR,cAAc,CAAC7H,KAAK,KAAK,SAAS,IAAI6H,cAAc,CAAC7H,KAAK,KAAK,WAAW,GACtE+H,IAAI,CAACM,kBAAkB,CAAC8F,IAAI,CAAC,EAAE,CAAC,GAChC,IAAI;gBAAE;gBACZ7F,eAAe,EACbT,cAAc,CAAC7H,KAAK,KAAK,SAAS,IAAI6H,cAAc,CAAC7H,KAAK,KAAK,WAAW,GAAG+H,IAAI,CAACO,eAAe,GAAG,IAAI;gBAAE;gBAC5GC,cAAc,EAAEV,cAAc,CAAC7H,KAAK,KAAK,SAAS,GAAG+H,IAAI,CAACQ,cAAc,GAAG,IAAI;gBAC/EC,cAAc,EAAEX,cAAc,CAAC7H,KAAK,KAAK,SAAS,GAAG+H,IAAI,CAACS,cAAc,GAAG,IAAI;gBAC/EC,eAAe,EAAEZ,cAAc,CAAC7H,KAAK,KAAK,WAAW,GAAG+H,IAAI,CAACU,eAAe,GAAG;cACjF,CAAC,CAAC;YAAA;cAAAgF,qBAAA,GAAAG,SAAA,CAAA3K,IAAA;cAnBMyK,IAAI,GAAAD,qBAAA,CAAJC,IAAI;cAoBZ,IAAIA,IAAI,KAAK,GAAG,EAAE;gBAChBrG,SAAS,CAAC;kBAAElG,IAAI,EAAE,SAAS;kBAAEyH,OAAO,EAAE;gBAAO,CAAC,CAAC;gBAC/CjB,IAAI,CAAC,UAAU,CAAC;cAClB;YAAC;YAAA;cAAA,OAAAiG,SAAA,CAAAtI,IAAA;UAAA;QAAA,GAAAkI,QAAA;MAAA,CACF;MAAA,gBAzBKH,kBAAkBA,CAAA;QAAA,OAAAE,KAAA,CAAAtH,KAAA,OAAAD,SAAA;MAAA;IAAA,GAyBvB;IACD,IAAMoI,SAAS,GAAG,SAAZA,SAASA,CAAA,EAAS;MACtBzG,IAAI,CAAC,UAAU,CAAC;IAClB,CAAC;IACDR,KAAK,CACH;MAAA,OAAMM,KAAK,CAAC4G,OAAO;IAAA,GACnB,YAAM;MAAA,IAAAC,cAAA;MACJ,IAAMC,UAAU,GAAG,EAAAD,cAAA,GAAA7G,KAAK,CAAC4G,OAAO,cAAAC,cAAA,gBAAAA,cAAA,GAAbA,cAAA,CAAeE,mBAAmB,cAAAF,cAAA,uBAAlCA,cAAA,CAAoC5B,GAAG,CAAC,UAAC1K,CAAC;QAAA,OAAKA,CAAC,CAACyM,QAAQ;MAAA,EAAC,KAAI,EAAE;MACnFrF,UAAU,CAACpJ,KAAK,GAAG;QAAEuO,UAAU,EAAEG,IAAI,CAACC,SAAS,CAACJ,UAAU,CAAC;QAAEK,OAAO,EAAEnH,KAAK,CAAC4G,OAAO,CAACO;MAAQ,CAAC;MAC7F,IAAI7G,IAAI,CAACE,cAAc,KAAK,EAAE,EAAE;QAC9B,IAAIR,KAAK,CAAC4G,OAAO,CAACP,UAAU,EAAE;UAC5B/F,IAAI,CAACE,cAAc,GAAGR,KAAK,CAAC4G,OAAO,CAACP,UAAU,EAAC;UAC/C/F,IAAI,CAACG,gBAAgB,GAAGT,KAAK,CAAC4G,OAAO,CAACN,YAAY,EAAC;UACnDxD,oBAAoB,CAAC,CAAC;QACxB;MACF;IACF,CAAC,EACD;MAAEsE,SAAS,EAAE;IAAK,CACpB,CAAC;IACD1H,KAAK,CACH;MAAA,OAAMM,KAAK,CAACqH,WAAW;IAAA,GACvB,YAAM;MACJ,IAAI/G,IAAI,CAACK,YAAY,KAAK,EAAE,EAAE;QAC5B,IAAIX,KAAK,CAACqH,WAAW,CAAC1G,YAAY,EAAE;UAClCL,IAAI,CAACK,YAAY,GAAGX,KAAK,CAACqH,WAAW,CAAC1G,YAAY;UAClDL,IAAI,CAACM,kBAAkB,GAAGZ,KAAK,CAACqH,WAAW,CAACzG,kBAAkB;UAC9DN,IAAI,CAACO,eAAe,GAAGb,KAAK,CAACqH,WAAW,CAACxG,eAAe;UACxDqE,kBAAkB,CAAC,CAAC;QACtB;MACF;IACF,CAAC,EACD;MAAEkC,SAAS,EAAE;IAAK,CACpB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}