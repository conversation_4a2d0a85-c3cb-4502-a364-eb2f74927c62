{"ast": null, "code": "function _regeneratorRuntime() { \"use strict\"; /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */ _regeneratorRuntime = function _regeneratorRuntime() { return e; }; var t, e = {}, r = Object.prototype, n = r.hasOwnProperty, o = Object.defineProperty || function (t, e, r) { t[e] = r.value; }, i = \"function\" == typeof Symbol ? Symbol : {}, a = i.iterator || \"@@iterator\", c = i.asyncIterator || \"@@asyncIterator\", u = i.toStringTag || \"@@toStringTag\"; function define(t, e, r) { return Object.defineProperty(t, e, { value: r, enumerable: !0, configurable: !0, writable: !0 }), t[e]; } try { define({}, \"\"); } catch (t) { define = function define(t, e, r) { return t[e] = r; }; } function wrap(t, e, r, n) { var i = e && e.prototype instanceof Generator ? e : Generator, a = Object.create(i.prototype), c = new Context(n || []); return o(a, \"_invoke\", { value: makeInvokeMethod(t, r, c) }), a; } function tryCatch(t, e, r) { try { return { type: \"normal\", arg: t.call(e, r) }; } catch (t) { return { type: \"throw\", arg: t }; } } e.wrap = wrap; var h = \"suspendedStart\", l = \"suspendedYield\", f = \"executing\", s = \"completed\", y = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} var p = {}; define(p, a, function () { return this; }); var d = Object.getPrototypeOf, v = d && d(d(values([]))); v && v !== r && n.call(v, a) && (p = v); var g = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(p); function defineIteratorMethods(t) { [\"next\", \"throw\", \"return\"].forEach(function (e) { define(t, e, function (t) { return this._invoke(e, t); }); }); } function AsyncIterator(t, e) { function invoke(r, o, i, a) { var c = tryCatch(t[r], t, o); if (\"throw\" !== c.type) { var u = c.arg, h = u.value; return h && \"object\" == typeof h && n.call(h, \"__await\") ? e.resolve(h.__await).then(function (t) { invoke(\"next\", t, i, a); }, function (t) { invoke(\"throw\", t, i, a); }) : e.resolve(h).then(function (t) { u.value = t, i(u); }, function (t) { return invoke(\"throw\", t, i, a); }); } a(c.arg); } var r; o(this, \"_invoke\", { value: function value(t, n) { function callInvokeWithMethodAndArg() { return new e(function (e, r) { invoke(t, n, e, r); }); } return r = r ? r.then(callInvokeWithMethodAndArg, callInvokeWithMethodAndArg) : callInvokeWithMethodAndArg(); } }); } function makeInvokeMethod(e, r, n) { var o = h; return function (i, a) { if (o === f) throw Error(\"Generator is already running\"); if (o === s) { if (\"throw\" === i) throw a; return { value: t, done: !0 }; } for (n.method = i, n.arg = a;;) { var c = n.delegate; if (c) { var u = maybeInvokeDelegate(c, n); if (u) { if (u === y) continue; return u; } } if (\"next\" === n.method) n.sent = n._sent = n.arg;else if (\"throw\" === n.method) { if (o === h) throw o = s, n.arg; n.dispatchException(n.arg); } else \"return\" === n.method && n.abrupt(\"return\", n.arg); o = f; var p = tryCatch(e, r, n); if (\"normal\" === p.type) { if (o = n.done ? s : l, p.arg === y) continue; return { value: p.arg, done: n.done }; } \"throw\" === p.type && (o = s, n.method = \"throw\", n.arg = p.arg); } }; } function maybeInvokeDelegate(e, r) { var n = r.method, o = e.iterator[n]; if (o === t) return r.delegate = null, \"throw\" === n && e.iterator.return && (r.method = \"return\", r.arg = t, maybeInvokeDelegate(e, r), \"throw\" === r.method) || \"return\" !== n && (r.method = \"throw\", r.arg = new TypeError(\"The iterator does not provide a '\" + n + \"' method\")), y; var i = tryCatch(o, e.iterator, r.arg); if (\"throw\" === i.type) return r.method = \"throw\", r.arg = i.arg, r.delegate = null, y; var a = i.arg; return a ? a.done ? (r[e.resultName] = a.value, r.next = e.nextLoc, \"return\" !== r.method && (r.method = \"next\", r.arg = t), r.delegate = null, y) : a : (r.method = \"throw\", r.arg = new TypeError(\"iterator result is not an object\"), r.delegate = null, y); } function pushTryEntry(t) { var e = { tryLoc: t[0] }; 1 in t && (e.catchLoc = t[1]), 2 in t && (e.finallyLoc = t[2], e.afterLoc = t[3]), this.tryEntries.push(e); } function resetTryEntry(t) { var e = t.completion || {}; e.type = \"normal\", delete e.arg, t.completion = e; } function Context(t) { this.tryEntries = [{ tryLoc: \"root\" }], t.forEach(pushTryEntry, this), this.reset(!0); } function values(e) { if (e || \"\" === e) { var r = e[a]; if (r) return r.call(e); if (\"function\" == typeof e.next) return e; if (!isNaN(e.length)) { var o = -1, i = function next() { for (; ++o < e.length;) if (n.call(e, o)) return next.value = e[o], next.done = !1, next; return next.value = t, next.done = !0, next; }; return i.next = i; } } throw new TypeError(typeof e + \" is not iterable\"); } return GeneratorFunction.prototype = GeneratorFunctionPrototype, o(g, \"constructor\", { value: GeneratorFunctionPrototype, configurable: !0 }), o(GeneratorFunctionPrototype, \"constructor\", { value: GeneratorFunction, configurable: !0 }), GeneratorFunction.displayName = define(GeneratorFunctionPrototype, u, \"GeneratorFunction\"), e.isGeneratorFunction = function (t) { var e = \"function\" == typeof t && t.constructor; return !!e && (e === GeneratorFunction || \"GeneratorFunction\" === (e.displayName || e.name)); }, e.mark = function (t) { return Object.setPrototypeOf ? Object.setPrototypeOf(t, GeneratorFunctionPrototype) : (t.__proto__ = GeneratorFunctionPrototype, define(t, u, \"GeneratorFunction\")), t.prototype = Object.create(g), t; }, e.awrap = function (t) { return { __await: t }; }, defineIteratorMethods(AsyncIterator.prototype), define(AsyncIterator.prototype, c, function () { return this; }), e.AsyncIterator = AsyncIterator, e.async = function (t, r, n, o, i) { void 0 === i && (i = Promise); var a = new AsyncIterator(wrap(t, r, n, o), i); return e.isGeneratorFunction(r) ? a : a.next().then(function (t) { return t.done ? t.value : a.next(); }); }, defineIteratorMethods(g), define(g, u, \"Generator\"), define(g, a, function () { return this; }), define(g, \"toString\", function () { return \"[object Generator]\"; }), e.keys = function (t) { var e = Object(t), r = []; for (var n in e) r.push(n); return r.reverse(), function next() { for (; r.length;) { var t = r.pop(); if (t in e) return next.value = t, next.done = !1, next; } return next.done = !0, next; }; }, e.values = values, Context.prototype = { constructor: Context, reset: function reset(e) { if (this.prev = 0, this.next = 0, this.sent = this._sent = t, this.done = !1, this.delegate = null, this.method = \"next\", this.arg = t, this.tryEntries.forEach(resetTryEntry), !e) for (var r in this) \"t\" === r.charAt(0) && n.call(this, r) && !isNaN(+r.slice(1)) && (this[r] = t); }, stop: function stop() { this.done = !0; var t = this.tryEntries[0].completion; if (\"throw\" === t.type) throw t.arg; return this.rval; }, dispatchException: function dispatchException(e) { if (this.done) throw e; var r = this; function handle(n, o) { return a.type = \"throw\", a.arg = e, r.next = n, o && (r.method = \"next\", r.arg = t), !!o; } for (var o = this.tryEntries.length - 1; o >= 0; --o) { var i = this.tryEntries[o], a = i.completion; if (\"root\" === i.tryLoc) return handle(\"end\"); if (i.tryLoc <= this.prev) { var c = n.call(i, \"catchLoc\"), u = n.call(i, \"finallyLoc\"); if (c && u) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } else if (c) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); } else { if (!u) throw Error(\"try statement without catch or finally\"); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } } } }, abrupt: function abrupt(t, e) { for (var r = this.tryEntries.length - 1; r >= 0; --r) { var o = this.tryEntries[r]; if (o.tryLoc <= this.prev && n.call(o, \"finallyLoc\") && this.prev < o.finallyLoc) { var i = o; break; } } i && (\"break\" === t || \"continue\" === t) && i.tryLoc <= e && e <= i.finallyLoc && (i = null); var a = i ? i.completion : {}; return a.type = t, a.arg = e, i ? (this.method = \"next\", this.next = i.finallyLoc, y) : this.complete(a); }, complete: function complete(t, e) { if (\"throw\" === t.type) throw t.arg; return \"break\" === t.type || \"continue\" === t.type ? this.next = t.arg : \"return\" === t.type ? (this.rval = this.arg = t.arg, this.method = \"return\", this.next = \"end\") : \"normal\" === t.type && e && (this.next = e), y; }, finish: function finish(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.finallyLoc === t) return this.complete(r.completion, r.afterLoc), resetTryEntry(r), y; } }, catch: function _catch(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.tryLoc === t) { var n = r.completion; if (\"throw\" === n.type) { var o = n.arg; resetTryEntry(r); } return o; } } throw Error(\"illegal catch attempt\"); }, delegateYield: function delegateYield(e, r, n) { return this.delegate = { iterator: values(e), resultName: r, nextLoc: n }, \"next\" === this.method && (this.arg = t), y; } }, e; }\nfunction asyncGeneratorStep(n, t, e, r, o, a, c) { try { var i = n[a](c), u = i.value; } catch (n) { return void e(n); } i.done ? t(u) : Promise.resolve(u).then(r, o); }\nfunction _asyncToGenerator(n) { return function () { var t = this, e = arguments; return new Promise(function (r, o) { var a = n.apply(t, e); function _next(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"next\", n); } function _throw(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"throw\", n); } _next(void 0); }); }; }\nimport api from '@/api';\nimport { ref, computed, onActivated } from 'vue';\nimport { format } from 'common/js/time.js';\nvar __default__ = {\n  name: 'SuggestBasicInfo'\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  props: {\n    id: {\n      type: String,\n      default: ''\n    },\n    details: {\n      type: Object,\n      default: function _default() {\n        return {};\n      }\n    }\n  },\n  setup(__props, _ref) {\n    var __expose = _ref.expose;\n    __expose();\n    var props = __props;\n    var details = computed(function () {\n      return props.details;\n    });\n    var suggestOpenTypeName = ref('');\n    var suggestSurveyTypeName = ref('');\n    var notHandleTimeTypeName = ref('');\n    var isHopeEnhanceTalkName = ref('');\n    var isMakeMineJobName = ref('');\n    var isNeedPaperAnswerName = ref('');\n    onActivated(function () {\n      dictionaryNameData();\n    });\n    var dictionaryNameData = /*#__PURE__*/function () {\n      var _ref2 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee() {\n        var _yield$api$dictionary, data;\n        return _regeneratorRuntime().wrap(function _callee$(_context) {\n          while (1) switch (_context.prev = _context.next) {\n            case 0:\n              _context.next = 2;\n              return api.dictionaryNameData({\n                dictCodes: ['suggest_open_type', 'suggest_survey_type', 'not_handle_time_type', 'is_hope_enhance_talk', 'is_make_mine_job', 'is_need_paper_answer']\n              });\n            case 2:\n              _yield$api$dictionary = _context.sent;\n              data = _yield$api$dictionary.data;\n              suggestOpenTypeName.value = data.suggest_open_type;\n              suggestSurveyTypeName.value = data.suggest_survey_type;\n              notHandleTimeTypeName.value = data.not_handle_time_type;\n              isHopeEnhanceTalkName.value = data.is_hope_enhance_talk;\n              isMakeMineJobName.value = data.is_make_mine_job;\n              isNeedPaperAnswerName.value = data.is_need_paper_answer;\n            case 10:\n            case \"end\":\n              return _context.stop();\n          }\n        }, _callee);\n      }));\n      return function dictionaryNameData() {\n        return _ref2.apply(this, arguments);\n      };\n    }();\n    var __returned__ = {\n      props,\n      details,\n      suggestOpenTypeName,\n      suggestSurveyTypeName,\n      notHandleTimeTypeName,\n      isHopeEnhanceTalkName,\n      isMakeMineJobName,\n      isNeedPaperAnswerName,\n      dictionaryNameData,\n      get api() {\n        return api;\n      },\n      ref,\n      computed,\n      onActivated,\n      get format() {\n        return format;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["_regeneratorRuntime", "e", "t", "r", "Object", "prototype", "n", "hasOwnProperty", "o", "defineProperty", "value", "i", "Symbol", "a", "iterator", "c", "asyncIterator", "u", "toStringTag", "define", "enumerable", "configurable", "writable", "wrap", "Generator", "create", "Context", "makeInvokeMethod", "tryCatch", "type", "arg", "call", "h", "l", "f", "s", "y", "GeneratorFunction", "GeneratorFunctionPrototype", "p", "d", "getPrototypeOf", "v", "values", "g", "defineIteratorMethods", "for<PERSON>ach", "_invoke", "AsyncIterator", "invoke", "resolve", "__await", "then", "callInvokeWithMethodAndArg", "Error", "done", "method", "delegate", "maybeInvokeDelegate", "sent", "_sent", "dispatchException", "abrupt", "return", "TypeError", "resultName", "next", "nextLoc", "pushTryEntry", "tryLoc", "catchLoc", "finallyLoc", "afterLoc", "tryEntries", "push", "resetTryEntry", "completion", "reset", "isNaN", "length", "displayName", "isGeneratorFunction", "constructor", "name", "mark", "setPrototypeOf", "__proto__", "awrap", "async", "Promise", "keys", "reverse", "pop", "prev", "char<PERSON>t", "slice", "stop", "rval", "handle", "complete", "finish", "catch", "_catch", "<PERSON><PERSON><PERSON>", "asyncGeneratorStep", "_asyncToGenerator", "arguments", "apply", "_next", "_throw", "api", "ref", "computed", "onActivated", "format", "__default__", "props", "__props", "details", "suggestOpenTypeName", "suggestSurveyTypeName", "notHandleTimeTypeName", "isHopeEnhanceTalkName", "isMakeMineJobName", "isNeedPaperAnswerName", "dictionaryNameData", "_ref2", "_callee", "_yield$api$dictionary", "data", "_callee$", "_context", "dictCodes", "suggest_open_type", "suggest_survey_type", "not_handle_time_type", "is_hope_enhance_talk", "is_make_mine_job", "is_need_paper_answer"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/microApp/proposal/src/views/SuggestDetail/component/SuggestBasicInfo.vue"], "sourcesContent": ["<template>\r\n  <div class=\"SuggestBasicInfo\">\r\n    <global-dynamic-title templateCode=\"proposal_title\" :params=\"{ businessId: props.id }\"></global-dynamic-title>\r\n    <div class=\"SuggestDetailInfo\">\r\n      <div class=\"SuggestDetailNumberTime\">\r\n        <div class=\"SuggestDetailNumber\">提案编号：{{ details.serialNumber }}</div>\r\n        <div class=\"SuggestDetailTime\">提案时间：{{ format(details.createDate) }}</div>\r\n      </div>\r\n      <div class=\"SuggestDetailType\">\r\n        <div class=\"SuggestDetailBigType\" v-if=\"details.bigThemeName\">{{ details.bigThemeName }}</div>\r\n        <div class=\"SuggestDetailSmallType\" v-if=\"details.smallThemeName\">{{ details.smallThemeName }}</div>\r\n      </div>\r\n    </div>\r\n    <div class=\"SuggestDetailTitle\">{{ details.title }}</div>\r\n    <template v-if=\"details.suggestSubmitWay === 'team'\">\r\n      <div class=\"SuggestDetailInfoItem\">提案者：{{ details.suggestUserName }}</div>\r\n    </template>\r\n    <template v-if=\"details.suggestSubmitWay === 'cppcc_member'\">\r\n      <div class=\"SuggestDetailInfo\">\r\n        <div class=\"SuggestDetailInfoItem\">提案者：{{ details.submitUserInfo?.userName }}</div>\r\n        <div class=\"SuggestDetailInfoItem\">委员证号：{{ details.submitUserInfo?.cardNumber }}</div>\r\n      </div>\r\n      <div class=\"SuggestDetailInfo\">\r\n        <div class=\"SuggestDetailInfoItem\">界别：{{ details.sectorType?.label }}</div>\r\n        <div class=\"SuggestDetailInfoItem\">联系电话：{{ details.submitUserInfo?.mobile }}</div>\r\n      </div>\r\n      <div class=\"SuggestDetailInfo\">\r\n        <div class=\"SuggestDetailInfoItem\">办公电话：{{ details.submitUserInfo?.officePhone }}</div>\r\n        <div class=\"SuggestDetailInfoItem\">邮政编码：{{ details.submitUserInfo?.postcode }}</div>\r\n      </div>\r\n      <div class=\"SuggestDetailInfoItem\">单位及职务：{{ details.submitUserInfo?.position }}</div>\r\n      <div class=\"SuggestDetailInfoItem\">通讯地址：{{ details.submitUserInfo?.callAddress }}</div>\r\n    </template>\r\n    <div class=\"SuggestDetailContent\" v-html=\"details.content\"></div>\r\n    <div class=\"SuggestDetailInfoName\">建议清单</div>\r\n    <!-- <div class=\"suggestionList\" v-for=\"(item, index) in details.proposalInventoryList\" :key=\"index\"> -->\r\n    <!-- <div class=\"suggestionListIndex\">建议{{ index + 1 }}：</div> -->\r\n    <!-- <div class=\"suggestionListName\">建议：<span>{{ item.content }}</span></div>\r\n      <div class=\"suggestionListDesc\">补充说明：<span>{{ item.replenish }}</span></div> -->\r\n    <!-- </div> -->\r\n    <!-- <div class=\"globalTable\"> -->\r\n    <el-table ref=\"tableRef\" row-key=\"id\" border :data=\"details.proposalInventoryList\" >\r\n      <el-table-column label=\"建议\" min-width=\"120\" prop=\"content\" />\r\n      <el-table-column label=\"答复\" width=\"120\" prop=\"\" />\r\n    </el-table>\r\n    <!-- </div> -->\r\n    <xyl-global-file :fileData=\"details.attachments\"></xyl-global-file>\r\n    <div class=\"SuggestDetailInfoName\">提案相关情况</div>\r\n    <div class=\"SuggestDetailInfoItem\" v-if=\"suggestOpenTypeName\">{{ suggestOpenTypeName }}：<span>{{\r\n      details.suggestOpenType?.label }}</span></div>\r\n    <div class=\"SuggestDetailInfoItem\" v-if=\"suggestSurveyTypeName\">{{ suggestSurveyTypeName }}：<span>{{\r\n      details.suggestSurveyType?.label }}</span>\r\n    </div>\r\n    <div class=\"SuggestDetailInfoItem\" v-if=\"isMakeMineJobName\">{{ isMakeMineJobName }}：<span>{{\r\n      details.isMakeMineJob?.label }}</span></div>\r\n    <div class=\"SuggestDetailInfoItem\" v-if=\"notHandleTimeTypeName\">{{ notHandleTimeTypeName }}：<span>{{\r\n      details.notHandleTimeType?.label }}</span>\r\n    </div>\r\n    <div class=\"SuggestDetailInfoItem\" v-if=\"isHopeEnhanceTalkName\">{{ isHopeEnhanceTalkName }}：<span>{{\r\n      details.isHopeEnhanceTalk?.label }}</span>\r\n    </div>\r\n    <div class=\"SuggestDetailInfoItem\" v-if=\"isNeedPaperAnswerName\">{{ isNeedPaperAnswerName }}：<span>{{\r\n      details.isNeedPaperAnswer?.label }}</span>\r\n    </div>\r\n    <div class=\"SuggestDetailInfoItem\">希望送交办单位：{{details.hopeHandleOfficeIds?.map(v => v.officeName).join('、')}}</div>\r\n    <div class=\"SuggestDetailInfoName\" v-if=\"details.joinUsers?.length\">提案联名人</div>\r\n    <div class=\"SuggestDetailTable\" v-if=\"details.joinUsers?.length\">\r\n      <div class=\"SuggestDetailTableHead\">\r\n        <div class=\"SuggestDetailTableItem row1\">姓名</div>\r\n        <div class=\"SuggestDetailTableItem row1\">委员证号</div>\r\n        <div class=\"SuggestDetailTableItem row1\">联系电话</div>\r\n        <div class=\"SuggestDetailTableItem row3\">通讯地址</div>\r\n        <div class=\"SuggestDetailTableItem row1\">是否同意</div>\r\n      </div>\r\n      <div class=\"SuggestDetailTableBody\" v-for=\"item in details.joinUsers\" :key=\"item.userId\">\r\n        <div class=\"SuggestDetailTableItem row1\">{{ item.userName }}</div>\r\n        <div class=\"SuggestDetailTableItem row1\">{{ item.cardNumber }}</div>\r\n        <div class=\"SuggestDetailTableItem row1\">{{ item.mobile }}</div>\r\n        <div class=\"SuggestDetailTableItem row3\">{{ item.callAddress }}</div>\r\n        <div class=\"SuggestDetailTableItem row1\" v-if=\"!item.agreeStatus\"></div>\r\n        <div class=\"SuggestDetailTableItem row1\" v-if=\"item.agreeStatus === 1\">已操作同意</div>\r\n        <div class=\"SuggestDetailTableItem row1\" v-if=\"item.agreeStatus === 2\">已操作不同意</div>\r\n      </div>\r\n    </div>\r\n    <div class=\"SuggestDetailInfoName\" v-if=\"details.contacters?.length\">提案联系人</div>\r\n    <div class=\"SuggestDetailTable\" v-if=\"details.contacters?.length\">\r\n      <div class=\"SuggestDetailTableHead\">\r\n        <div class=\"SuggestDetailTableItem row1\">联系人姓名</div>\r\n        <div class=\"SuggestDetailTableItem row1\">联系人电话</div>\r\n        <div class=\"SuggestDetailTableItem row4\">联系人通讯地址</div>\r\n      </div>\r\n      <div class=\"SuggestDetailTableBody\" v-for=\"item in details.contacters\" :key=\"item.id\">\r\n        <div class=\"SuggestDetailTableItem row1\">{{ item.contacterName }}</div>\r\n        <div class=\"SuggestDetailTableItem row1\">{{ item.contacterMobile }}</div>\r\n        <div class=\"SuggestDetailTableItem row4\">{{ item.contacterAddress }}</div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'SuggestBasicInfo' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, computed, onActivated } from 'vue'\r\nimport { format } from 'common/js/time.js'\r\nconst props = defineProps({ id: { type: String, default: '' }, details: { type: Object, default: () => ({}) } })\r\nconst details = computed(() => props.details)\r\nconst suggestOpenTypeName = ref('')\r\nconst suggestSurveyTypeName = ref('')\r\nconst notHandleTimeTypeName = ref('')\r\nconst isHopeEnhanceTalkName = ref('')\r\nconst isMakeMineJobName = ref('')\r\nconst isNeedPaperAnswerName = ref('')\r\nonActivated(() => { dictionaryNameData() })\r\n\r\nconst dictionaryNameData = async () => {\r\n  const { data } = await api.dictionaryNameData({\r\n    dictCodes: ['suggest_open_type', 'suggest_survey_type', 'not_handle_time_type', 'is_hope_enhance_talk', 'is_make_mine_job', 'is_need_paper_answer']\r\n  })\r\n  suggestOpenTypeName.value = data.suggest_open_type\r\n  suggestSurveyTypeName.value = data.suggest_survey_type\r\n  notHandleTimeTypeName.value = data.not_handle_time_type\r\n  isHopeEnhanceTalkName.value = data.is_hope_enhance_talk\r\n  isMakeMineJobName.value = data.is_make_mine_job\r\n  isNeedPaperAnswerName.value = data.is_need_paper_answer\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.SuggestBasicInfo {\r\n  width: 100%;\r\n  padding: var(--zy-distance-one) 0;\r\n\r\n  .SuggestDetailName {\r\n    font-size: var(--zy-title-font-size);\r\n    font-weight: bold;\r\n    border-bottom: 2px solid var(--zy-el-color-primary);\r\n    text-align: center;\r\n    padding: 20px 0;\r\n\r\n    span {\r\n      color: red;\r\n    }\r\n  }\r\n\r\n  .SuggestDetailTitle {\r\n    width: 100%;\r\n    padding: 10px 0;\r\n    font-weight: bold;\r\n    font-size: var(--zy-title-font-size);\r\n    line-height: var(--zy-line-height);\r\n  }\r\n\r\n  .SuggestDetailInfo {\r\n    width: 100%;\r\n    display: flex;\r\n    justify-content: space-between;\r\n\r\n    .SuggestDetailNumberTime {\r\n      display: flex;\r\n      padding-top: 20px;\r\n      padding-bottom: 10px;\r\n\r\n      .SuggestDetailNumber {\r\n        font-weight: bold;\r\n        font-size: var(--zy-text-font-size);\r\n        line-height: var(--zy-line-height);\r\n      }\r\n\r\n      .SuggestDetailTime {\r\n        color: var(--zy-el-text-color-regular);\r\n        font-size: var(--zy-text-font-size);\r\n        line-height: var(--zy-line-height);\r\n        margin-left: 40px;\r\n      }\r\n    }\r\n\r\n    .SuggestDetailType {\r\n      display: flex;\r\n      align-items: center;\r\n\r\n      .SuggestDetailBigType {\r\n        color: var(--zy-el-color-warning);\r\n        background-color: var(--zy-el-color-warning-light-9);\r\n        font-size: var(--zy-text-font-size);\r\n        line-height: var(--zy-line-height);\r\n        padding: 0 10px;\r\n      }\r\n\r\n      .SuggestDetailSmallType {\r\n        color: var(--zy-el-color-success);\r\n        background-color: var(--zy-el-color-success-light-9);\r\n        font-size: var(--zy-text-font-size);\r\n        line-height: var(--zy-line-height);\r\n        padding: 0 10px;\r\n        margin-left: 20px;\r\n      }\r\n    }\r\n\r\n    .SuggestDetailInfoItem {\r\n      width: 50%;\r\n      padding: 10px 0;\r\n    }\r\n  }\r\n\r\n  .SuggestDetailInfoName {\r\n    padding-top: 15px;\r\n    padding-bottom: 5px;\r\n    font-weight: bold;\r\n    font-size: var(--zy-text-font-size);\r\n    line-height: var(--zy-line-height);\r\n  }\r\n\r\n  .SuggestDetailInfoItem {\r\n    width: 100%;\r\n    padding: 5px 0;\r\n    font-size: var(--zy-text-font-size);\r\n    line-height: var(--zy-line-height);\r\n\r\n    span {\r\n      font-weight: bold;\r\n    }\r\n  }\r\n\r\n  .SuggestDetailContent {\r\n    padding: 20px 0;\r\n    overflow: hidden;\r\n    line-height: var(--zy-line-height);\r\n\r\n    img,\r\n    video {\r\n      max-width: 100%;\r\n      height: auto !important;\r\n    }\r\n\r\n    table {\r\n      max-width: 100%;\r\n      border-collapse: collapse;\r\n      border-spacing: 0;\r\n\r\n      tr {\r\n        page-break-inside: avoid;\r\n      }\r\n    }\r\n  }\r\n\r\n  .SuggestDetailTable {\r\n    width: 100%;\r\n    margin: 5px 0;\r\n    border-top: 1px solid var(--zy-el-border-color-lighter);\r\n    border-right: 1px solid var(--zy-el-border-color-lighter);\r\n\r\n    .SuggestDetailTableHead,\r\n    .SuggestDetailTableBody {\r\n      width: 100%;\r\n      display: flex;\r\n      border-bottom: 1px solid var(--zy-el-border-color-lighter);\r\n    }\r\n\r\n    .SuggestDetailTableHead {\r\n      background-color: var(--zy-el-color-info-light-9);\r\n    }\r\n\r\n    .SuggestDetailTableBody {\r\n      border-bottom: 1px solid var(--zy-el-border-color-lighter);\r\n    }\r\n\r\n    .row1 {\r\n      flex: 1;\r\n    }\r\n\r\n    .row2 {\r\n      flex: 2;\r\n    }\r\n\r\n    .row3 {\r\n      flex: 3;\r\n    }\r\n\r\n    .row4 {\r\n      flex: 4;\r\n    }\r\n\r\n    .SuggestDetailTableItem {\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      border-left: 1px solid var(--zy-el-border-color-lighter);\r\n      font-size: var(--zy-text-font-size);\r\n      line-height: var(--zy-line-height);\r\n      padding: 10px;\r\n    }\r\n  }\r\n\r\n  .suggestionList {\r\n    box-shadow: 0 2px 4px rgba(0, 0, 0, .12),\r\n      0 0 6px rgba(0, 0, 0, .04);\r\n    padding: 10px 20px;\r\n    margin-top: 10px;\r\n\r\n    .suggestionListIndex {}\r\n\r\n    .suggestionListName {\r\n      color: #000;\r\n      font-size: 14px;\r\n    }\r\n\r\n    .suggestionListDesc {\r\n      color: #000;\r\n      font-size: 14px;\r\n      margin-top: 5px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": "+CAwGA,qJAAAA,mBAAA,YAAAA,oBAAA,WAAAC,CAAA,SAAAC,CAAA,EAAAD,CAAA,OAAAE,CAAA,GAAAC,MAAA,CAAAC,SAAA,EAAAC,CAAA,GAAAH,CAAA,CAAAI,cAAA,EAAAC,CAAA,GAAAJ,MAAA,CAAAK,cAAA,cAAAP,CAAA,EAAAD,CAAA,EAAAE,CAAA,IAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,CAAAO,KAAA,KAAAC,CAAA,wBAAAC,MAAA,GAAAA,MAAA,OAAAC,CAAA,GAAAF,CAAA,CAAAG,QAAA,kBAAAC,CAAA,GAAAJ,CAAA,CAAAK,aAAA,uBAAAC,CAAA,GAAAN,CAAA,CAAAO,WAAA,8BAAAC,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAC,MAAA,CAAAK,cAAA,CAAAP,CAAA,EAAAD,CAAA,IAAAS,KAAA,EAAAP,CAAA,EAAAiB,UAAA,MAAAC,YAAA,MAAAC,QAAA,SAAApB,CAAA,CAAAD,CAAA,WAAAkB,MAAA,mBAAAjB,CAAA,IAAAiB,MAAA,YAAAA,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,gBAAAoB,KAAArB,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAK,CAAA,GAAAV,CAAA,IAAAA,CAAA,CAAAI,SAAA,YAAAmB,SAAA,GAAAvB,CAAA,GAAAuB,SAAA,EAAAX,CAAA,GAAAT,MAAA,CAAAqB,MAAA,CAAAd,CAAA,CAAAN,SAAA,GAAAU,CAAA,OAAAW,OAAA,CAAApB,CAAA,gBAAAE,CAAA,CAAAK,CAAA,eAAAH,KAAA,EAAAiB,gBAAA,CAAAzB,CAAA,EAAAC,CAAA,EAAAY,CAAA,MAAAF,CAAA,aAAAe,SAAA1B,CAAA,EAAAD,CAAA,EAAAE,CAAA,mBAAA0B,IAAA,YAAAC,GAAA,EAAA5B,CAAA,CAAA6B,IAAA,CAAA9B,CAAA,EAAAE,CAAA,cAAAD,CAAA,aAAA2B,IAAA,WAAAC,GAAA,EAAA5B,CAAA,QAAAD,CAAA,CAAAsB,IAAA,GAAAA,IAAA,MAAAS,CAAA,qBAAAC,CAAA,qBAAAC,CAAA,gBAAAC,CAAA,gBAAAC,CAAA,gBAAAZ,UAAA,cAAAa,kBAAA,cAAAC,2BAAA,SAAAC,CAAA,OAAApB,MAAA,CAAAoB,CAAA,EAAA1B,CAAA,qCAAA2B,CAAA,GAAApC,MAAA,CAAAqC,cAAA,EAAAC,CAAA,GAAAF,CAAA,IAAAA,CAAA,CAAAA,CAAA,CAAAG,MAAA,QAAAD,CAAA,IAAAA,CAAA,KAAAvC,CAAA,IAAAG,CAAA,CAAAyB,IAAA,CAAAW,CAAA,EAAA7B,CAAA,MAAA0B,CAAA,GAAAG,CAAA,OAAAE,CAAA,GAAAN,0BAAA,CAAAjC,SAAA,GAAAmB,SAAA,CAAAnB,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAc,CAAA,YAAAM,sBAAA3C,CAAA,gCAAA4C,OAAA,WAAA7C,CAAA,IAAAkB,MAAA,CAAAjB,CAAA,EAAAD,CAAA,YAAAC,CAAA,gBAAA6C,OAAA,CAAA9C,CAAA,EAAAC,CAAA,sBAAA8C,cAAA9C,CAAA,EAAAD,CAAA,aAAAgD,OAAA9C,CAAA,EAAAK,CAAA,EAAAG,CAAA,EAAAE,CAAA,QAAAE,CAAA,GAAAa,QAAA,CAAA1B,CAAA,CAAAC,CAAA,GAAAD,CAAA,EAAAM,CAAA,mBAAAO,CAAA,CAAAc,IAAA,QAAAZ,CAAA,GAAAF,CAAA,CAAAe,GAAA,EAAAE,CAAA,GAAAf,CAAA,CAAAP,KAAA,SAAAsB,CAAA,uBAAAA,CAAA,IAAA1B,CAAA,CAAAyB,IAAA,CAAAC,CAAA,eAAA/B,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,CAAAmB,OAAA,EAAAC,IAAA,WAAAlD,CAAA,IAAA+C,MAAA,SAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,gBAAAX,CAAA,IAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,QAAAZ,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,EAAAoB,IAAA,WAAAlD,CAAA,IAAAe,CAAA,CAAAP,KAAA,GAAAR,CAAA,EAAAS,CAAA,CAAAM,CAAA,gBAAAf,CAAA,WAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,SAAAA,CAAA,CAAAE,CAAA,CAAAe,GAAA,SAAA3B,CAAA,EAAAK,CAAA,oBAAAE,KAAA,WAAAA,MAAAR,CAAA,EAAAI,CAAA,aAAA+C,2BAAA,eAAApD,CAAA,WAAAA,CAAA,EAAAE,CAAA,IAAA8C,MAAA,CAAA/C,CAAA,EAAAI,CAAA,EAAAL,CAAA,EAAAE,CAAA,gBAAAA,CAAA,GAAAA,CAAA,GAAAA,CAAA,CAAAiD,IAAA,CAAAC,0BAAA,EAAAA,0BAAA,IAAAA,0BAAA,qBAAA1B,iBAAA1B,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAE,CAAA,GAAAwB,CAAA,mBAAArB,CAAA,EAAAE,CAAA,QAAAL,CAAA,KAAA0B,CAAA,QAAAoB,KAAA,sCAAA9C,CAAA,KAAA2B,CAAA,oBAAAxB,CAAA,QAAAE,CAAA,WAAAH,KAAA,EAAAR,CAAA,EAAAqD,IAAA,eAAAjD,CAAA,CAAAkD,MAAA,GAAA7C,CAAA,EAAAL,CAAA,CAAAwB,GAAA,GAAAjB,CAAA,UAAAE,CAAA,GAAAT,CAAA,CAAAmD,QAAA,MAAA1C,CAAA,QAAAE,CAAA,GAAAyC,mBAAA,CAAA3C,CAAA,EAAAT,CAAA,OAAAW,CAAA,QAAAA,CAAA,KAAAmB,CAAA,mBAAAnB,CAAA,qBAAAX,CAAA,CAAAkD,MAAA,EAAAlD,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAsD,KAAA,GAAAtD,CAAA,CAAAwB,GAAA,sBAAAxB,CAAA,CAAAkD,MAAA,QAAAhD,CAAA,KAAAwB,CAAA,QAAAxB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAwB,GAAA,EAAAxB,CAAA,CAAAuD,iBAAA,CAAAvD,CAAA,CAAAwB,GAAA,uBAAAxB,CAAA,CAAAkD,MAAA,IAAAlD,CAAA,CAAAwD,MAAA,WAAAxD,CAAA,CAAAwB,GAAA,GAAAtB,CAAA,GAAA0B,CAAA,MAAAK,CAAA,GAAAX,QAAA,CAAA3B,CAAA,EAAAE,CAAA,EAAAG,CAAA,oBAAAiC,CAAA,CAAAV,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAiD,IAAA,GAAApB,CAAA,GAAAF,CAAA,EAAAM,CAAA,CAAAT,GAAA,KAAAM,CAAA,qBAAA1B,KAAA,EAAA6B,CAAA,CAAAT,GAAA,EAAAyB,IAAA,EAAAjD,CAAA,CAAAiD,IAAA,kBAAAhB,CAAA,CAAAV,IAAA,KAAArB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAkD,MAAA,YAAAlD,CAAA,CAAAwB,GAAA,GAAAS,CAAA,CAAAT,GAAA,mBAAA4B,oBAAAzD,CAAA,EAAAE,CAAA,QAAAG,CAAA,GAAAH,CAAA,CAAAqD,MAAA,EAAAhD,CAAA,GAAAP,CAAA,CAAAa,QAAA,CAAAR,CAAA,OAAAE,CAAA,KAAAN,CAAA,SAAAC,CAAA,CAAAsD,QAAA,qBAAAnD,CAAA,IAAAL,CAAA,CAAAa,QAAA,CAAAiD,MAAA,KAAA5D,CAAA,CAAAqD,MAAA,aAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,EAAAwD,mBAAA,CAAAzD,CAAA,EAAAE,CAAA,eAAAA,CAAA,CAAAqD,MAAA,kBAAAlD,CAAA,KAAAH,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,uCAAA1D,CAAA,iBAAA8B,CAAA,MAAAzB,CAAA,GAAAiB,QAAA,CAAApB,CAAA,EAAAP,CAAA,CAAAa,QAAA,EAAAX,CAAA,CAAA2B,GAAA,mBAAAnB,CAAA,CAAAkB,IAAA,SAAA1B,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,GAAAnB,CAAA,CAAAmB,GAAA,EAAA3B,CAAA,CAAAsD,QAAA,SAAArB,CAAA,MAAAvB,CAAA,GAAAF,CAAA,CAAAmB,GAAA,SAAAjB,CAAA,GAAAA,CAAA,CAAA0C,IAAA,IAAApD,CAAA,CAAAF,CAAA,CAAAgE,UAAA,IAAApD,CAAA,CAAAH,KAAA,EAAAP,CAAA,CAAA+D,IAAA,GAAAjE,CAAA,CAAAkE,OAAA,eAAAhE,CAAA,CAAAqD,MAAA,KAAArD,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,GAAAC,CAAA,CAAAsD,QAAA,SAAArB,CAAA,IAAAvB,CAAA,IAAAV,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,sCAAA7D,CAAA,CAAAsD,QAAA,SAAArB,CAAA,cAAAgC,aAAAlE,CAAA,QAAAD,CAAA,KAAAoE,MAAA,EAAAnE,CAAA,YAAAA,CAAA,KAAAD,CAAA,CAAAqE,QAAA,GAAApE,CAAA,WAAAA,CAAA,KAAAD,CAAA,CAAAsE,UAAA,GAAArE,CAAA,KAAAD,CAAA,CAAAuE,QAAA,GAAAtE,CAAA,WAAAuE,UAAA,CAAAC,IAAA,CAAAzE,CAAA,cAAA0E,cAAAzE,CAAA,QAAAD,CAAA,GAAAC,CAAA,CAAA0E,UAAA,QAAA3E,CAAA,CAAA4B,IAAA,oBAAA5B,CAAA,CAAA6B,GAAA,EAAA5B,CAAA,CAAA0E,UAAA,GAAA3E,CAAA,aAAAyB,QAAAxB,CAAA,SAAAuE,UAAA,MAAAJ,MAAA,aAAAnE,CAAA,CAAA4C,OAAA,CAAAsB,YAAA,cAAAS,KAAA,iBAAAlC,OAAA1C,CAAA,QAAAA,CAAA,WAAAA,CAAA,QAAAE,CAAA,GAAAF,CAAA,CAAAY,CAAA,OAAAV,CAAA,SAAAA,CAAA,CAAA4B,IAAA,CAAA9B,CAAA,4BAAAA,CAAA,CAAAiE,IAAA,SAAAjE,CAAA,OAAA6E,KAAA,CAAA7E,CAAA,CAAA8E,MAAA,SAAAvE,CAAA,OAAAG,CAAA,YAAAuD,KAAA,aAAA1D,CAAA,GAAAP,CAAA,CAAA8E,MAAA,OAAAzE,CAAA,CAAAyB,IAAA,CAAA9B,CAAA,EAAAO,CAAA,UAAA0D,IAAA,CAAAxD,KAAA,GAAAT,CAAA,CAAAO,CAAA,GAAA0D,IAAA,CAAAX,IAAA,OAAAW,IAAA,SAAAA,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,YAAAvD,CAAA,CAAAuD,IAAA,GAAAvD,CAAA,gBAAAqD,SAAA,QAAA/D,CAAA,iCAAAoC,iBAAA,CAAAhC,SAAA,GAAAiC,0BAAA,EAAA9B,CAAA,CAAAoC,CAAA,mBAAAlC,KAAA,EAAA4B,0BAAA,EAAAjB,YAAA,SAAAb,CAAA,CAAA8B,0BAAA,mBAAA5B,KAAA,EAAA2B,iBAAA,EAAAhB,YAAA,SAAAgB,iBAAA,CAAA2C,WAAA,GAAA7D,MAAA,CAAAmB,0BAAA,EAAArB,CAAA,wBAAAhB,CAAA,CAAAgF,mBAAA,aAAA/E,CAAA,QAAAD,CAAA,wBAAAC,CAAA,IAAAA,CAAA,CAAAgF,WAAA,WAAAjF,CAAA,KAAAA,CAAA,KAAAoC,iBAAA,6BAAApC,CAAA,CAAA+E,WAAA,IAAA/E,CAAA,CAAAkF,IAAA,OAAAlF,CAAA,CAAAmF,IAAA,aAAAlF,CAAA,WAAAE,MAAA,CAAAiF,cAAA,GAAAjF,MAAA,CAAAiF,cAAA,CAAAnF,CAAA,EAAAoC,0BAAA,KAAApC,CAAA,CAAAoF,SAAA,GAAAhD,0BAAA,EAAAnB,MAAA,CAAAjB,CAAA,EAAAe,CAAA,yBAAAf,CAAA,CAAAG,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAmB,CAAA,GAAA1C,CAAA,KAAAD,CAAA,CAAAsF,KAAA,aAAArF,CAAA,aAAAiD,OAAA,EAAAjD,CAAA,OAAA2C,qBAAA,CAAAG,aAAA,CAAA3C,SAAA,GAAAc,MAAA,CAAA6B,aAAA,CAAA3C,SAAA,EAAAU,CAAA,iCAAAd,CAAA,CAAA+C,aAAA,GAAAA,aAAA,EAAA/C,CAAA,CAAAuF,KAAA,aAAAtF,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,EAAAG,CAAA,eAAAA,CAAA,KAAAA,CAAA,GAAA8E,OAAA,OAAA5E,CAAA,OAAAmC,aAAA,CAAAzB,IAAA,CAAArB,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,GAAAG,CAAA,UAAAV,CAAA,CAAAgF,mBAAA,CAAA9E,CAAA,IAAAU,CAAA,GAAAA,CAAA,CAAAqD,IAAA,GAAAd,IAAA,WAAAlD,CAAA,WAAAA,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAQ,KAAA,GAAAG,CAAA,CAAAqD,IAAA,WAAArB,qBAAA,CAAAD,CAAA,GAAAzB,MAAA,CAAAyB,CAAA,EAAA3B,CAAA,gBAAAE,MAAA,CAAAyB,CAAA,EAAA/B,CAAA,iCAAAM,MAAA,CAAAyB,CAAA,6DAAA3C,CAAA,CAAAyF,IAAA,aAAAxF,CAAA,QAAAD,CAAA,GAAAG,MAAA,CAAAF,CAAA,GAAAC,CAAA,gBAAAG,CAAA,IAAAL,CAAA,EAAAE,CAAA,CAAAuE,IAAA,CAAApE,CAAA,UAAAH,CAAA,CAAAwF,OAAA,aAAAzB,KAAA,WAAA/D,CAAA,CAAA4E,MAAA,SAAA7E,CAAA,GAAAC,CAAA,CAAAyF,GAAA,QAAA1F,CAAA,IAAAD,CAAA,SAAAiE,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,WAAAA,IAAA,CAAAX,IAAA,OAAAW,IAAA,QAAAjE,CAAA,CAAA0C,MAAA,GAAAA,MAAA,EAAAjB,OAAA,CAAArB,SAAA,KAAA6E,WAAA,EAAAxD,OAAA,EAAAmD,KAAA,WAAAA,MAAA5E,CAAA,aAAA4F,IAAA,WAAA3B,IAAA,WAAAP,IAAA,QAAAC,KAAA,GAAA1D,CAAA,OAAAqD,IAAA,YAAAE,QAAA,cAAAD,MAAA,gBAAA1B,GAAA,GAAA5B,CAAA,OAAAuE,UAAA,CAAA3B,OAAA,CAAA6B,aAAA,IAAA1E,CAAA,WAAAE,CAAA,kBAAAA,CAAA,CAAA2F,MAAA,OAAAxF,CAAA,CAAAyB,IAAA,OAAA5B,CAAA,MAAA2E,KAAA,EAAA3E,CAAA,CAAA4F,KAAA,cAAA5F,CAAA,IAAAD,CAAA,MAAA8F,IAAA,WAAAA,KAAA,SAAAzC,IAAA,WAAArD,CAAA,QAAAuE,UAAA,IAAAG,UAAA,kBAAA1E,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,cAAAmE,IAAA,KAAApC,iBAAA,WAAAA,kBAAA5D,CAAA,aAAAsD,IAAA,QAAAtD,CAAA,MAAAE,CAAA,kBAAA+F,OAAA5F,CAAA,EAAAE,CAAA,WAAAK,CAAA,CAAAgB,IAAA,YAAAhB,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAE,CAAA,CAAA+D,IAAA,GAAA5D,CAAA,EAAAE,CAAA,KAAAL,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,KAAAM,CAAA,aAAAA,CAAA,QAAAiE,UAAA,CAAAM,MAAA,MAAAvE,CAAA,SAAAA,CAAA,QAAAG,CAAA,QAAA8D,UAAA,CAAAjE,CAAA,GAAAK,CAAA,GAAAF,CAAA,CAAAiE,UAAA,iBAAAjE,CAAA,CAAA0D,MAAA,SAAA6B,MAAA,aAAAvF,CAAA,CAAA0D,MAAA,SAAAwB,IAAA,QAAA9E,CAAA,GAAAT,CAAA,CAAAyB,IAAA,CAAApB,CAAA,eAAAM,CAAA,GAAAX,CAAA,CAAAyB,IAAA,CAAApB,CAAA,qBAAAI,CAAA,IAAAE,CAAA,aAAA4E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,gBAAAuB,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,cAAAxD,CAAA,aAAA8E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,qBAAArD,CAAA,QAAAqC,KAAA,qDAAAuC,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,YAAAT,MAAA,WAAAA,OAAA5D,CAAA,EAAAD,CAAA,aAAAE,CAAA,QAAAsE,UAAA,CAAAM,MAAA,MAAA5E,CAAA,SAAAA,CAAA,QAAAK,CAAA,QAAAiE,UAAA,CAAAtE,CAAA,OAAAK,CAAA,CAAA6D,MAAA,SAAAwB,IAAA,IAAAvF,CAAA,CAAAyB,IAAA,CAAAvB,CAAA,wBAAAqF,IAAA,GAAArF,CAAA,CAAA+D,UAAA,QAAA5D,CAAA,GAAAH,CAAA,aAAAG,CAAA,iBAAAT,CAAA,mBAAAA,CAAA,KAAAS,CAAA,CAAA0D,MAAA,IAAApE,CAAA,IAAAA,CAAA,IAAAU,CAAA,CAAA4D,UAAA,KAAA5D,CAAA,cAAAE,CAAA,GAAAF,CAAA,GAAAA,CAAA,CAAAiE,UAAA,cAAA/D,CAAA,CAAAgB,IAAA,GAAA3B,CAAA,EAAAW,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAU,CAAA,SAAA6C,MAAA,gBAAAU,IAAA,GAAAvD,CAAA,CAAA4D,UAAA,EAAAnC,CAAA,SAAA+D,QAAA,CAAAtF,CAAA,MAAAsF,QAAA,WAAAA,SAAAjG,CAAA,EAAAD,CAAA,oBAAAC,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,qBAAA5B,CAAA,CAAA2B,IAAA,mBAAA3B,CAAA,CAAA2B,IAAA,QAAAqC,IAAA,GAAAhE,CAAA,CAAA4B,GAAA,gBAAA5B,CAAA,CAAA2B,IAAA,SAAAoE,IAAA,QAAAnE,GAAA,GAAA5B,CAAA,CAAA4B,GAAA,OAAA0B,MAAA,kBAAAU,IAAA,yBAAAhE,CAAA,CAAA2B,IAAA,IAAA5B,CAAA,UAAAiE,IAAA,GAAAjE,CAAA,GAAAmC,CAAA,KAAAgE,MAAA,WAAAA,OAAAlG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAoE,UAAA,KAAArE,CAAA,cAAAiG,QAAA,CAAAhG,CAAA,CAAAyE,UAAA,EAAAzE,CAAA,CAAAqE,QAAA,GAAAG,aAAA,CAAAxE,CAAA,GAAAiC,CAAA,OAAAiE,KAAA,WAAAC,OAAApG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAkE,MAAA,KAAAnE,CAAA,QAAAI,CAAA,GAAAH,CAAA,CAAAyE,UAAA,kBAAAtE,CAAA,CAAAuB,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAwB,GAAA,EAAA6C,aAAA,CAAAxE,CAAA,YAAAK,CAAA,YAAA8C,KAAA,8BAAAiD,aAAA,WAAAA,cAAAtG,CAAA,EAAAE,CAAA,EAAAG,CAAA,gBAAAmD,QAAA,KAAA3C,QAAA,EAAA6B,MAAA,CAAA1C,CAAA,GAAAgE,UAAA,EAAA9D,CAAA,EAAAgE,OAAA,EAAA7D,CAAA,oBAAAkD,MAAA,UAAA1B,GAAA,GAAA5B,CAAA,GAAAkC,CAAA,OAAAnC,CAAA;AAAA,SAAAuG,mBAAAlG,CAAA,EAAAJ,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAK,CAAA,EAAAK,CAAA,EAAAE,CAAA,cAAAJ,CAAA,GAAAL,CAAA,CAAAO,CAAA,EAAAE,CAAA,GAAAE,CAAA,GAAAN,CAAA,CAAAD,KAAA,WAAAJ,CAAA,gBAAAL,CAAA,CAAAK,CAAA,KAAAK,CAAA,CAAA4C,IAAA,GAAArD,CAAA,CAAAe,CAAA,IAAAwE,OAAA,CAAAvC,OAAA,CAAAjC,CAAA,EAAAmC,IAAA,CAAAjD,CAAA,EAAAK,CAAA;AAAA,SAAAiG,kBAAAnG,CAAA,6BAAAJ,CAAA,SAAAD,CAAA,GAAAyG,SAAA,aAAAjB,OAAA,WAAAtF,CAAA,EAAAK,CAAA,QAAAK,CAAA,GAAAP,CAAA,CAAAqG,KAAA,CAAAzG,CAAA,EAAAD,CAAA,YAAA2G,MAAAtG,CAAA,IAAAkG,kBAAA,CAAA3F,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAoG,KAAA,EAAAC,MAAA,UAAAvG,CAAA,cAAAuG,OAAAvG,CAAA,IAAAkG,kBAAA,CAAA3F,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAoG,KAAA,EAAAC,MAAA,WAAAvG,CAAA,KAAAsG,KAAA;AADA,OAAOE,GAAG,MAAM,OAAO;AACvB,SAASC,GAAG,EAAEC,QAAQ,EAAEC,WAAW,QAAQ,KAAK;AAChD,SAASC,MAAM,QAAQ,mBAAmB;AAL1C,IAAAC,WAAA,GAAe;EAAEhC,IAAI,EAAE;AAAmB,CAAC;;;;;;;;;;;;;;;;;IAM3C,IAAMiC,KAAK,GAAGC,OAAkG;IAChH,IAAMC,OAAO,GAAGN,QAAQ,CAAC;MAAA,OAAMI,KAAK,CAACE,OAAO;IAAA,EAAC;IAC7C,IAAMC,mBAAmB,GAAGR,GAAG,CAAC,EAAE,CAAC;IACnC,IAAMS,qBAAqB,GAAGT,GAAG,CAAC,EAAE,CAAC;IACrC,IAAMU,qBAAqB,GAAGV,GAAG,CAAC,EAAE,CAAC;IACrC,IAAMW,qBAAqB,GAAGX,GAAG,CAAC,EAAE,CAAC;IACrC,IAAMY,iBAAiB,GAAGZ,GAAG,CAAC,EAAE,CAAC;IACjC,IAAMa,qBAAqB,GAAGb,GAAG,CAAC,EAAE,CAAC;IACrCE,WAAW,CAAC,YAAM;MAAEY,kBAAkB,CAAC,CAAC;IAAC,CAAC,CAAC;IAE3C,IAAMA,kBAAkB;MAAA,IAAAC,KAAA,GAAArB,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAA2C,QAAA;QAAA,IAAAC,qBAAA,EAAAC,IAAA;QAAA,OAAAjI,mBAAA,GAAAuB,IAAA,UAAA2G,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAtC,IAAA,GAAAsC,QAAA,CAAAjE,IAAA;YAAA;cAAAiE,QAAA,CAAAjE,IAAA;cAAA,OACF4C,GAAG,CAACe,kBAAkB,CAAC;gBAC5CO,SAAS,EAAE,CAAC,mBAAmB,EAAE,qBAAqB,EAAE,sBAAsB,EAAE,sBAAsB,EAAE,kBAAkB,EAAE,sBAAsB;cACpJ,CAAC,CAAC;YAAA;cAAAJ,qBAAA,GAAAG,QAAA,CAAAxE,IAAA;cAFMsE,IAAI,GAAAD,qBAAA,CAAJC,IAAI;cAGZV,mBAAmB,CAAC7G,KAAK,GAAGuH,IAAI,CAACI,iBAAiB;cAClDb,qBAAqB,CAAC9G,KAAK,GAAGuH,IAAI,CAACK,mBAAmB;cACtDb,qBAAqB,CAAC/G,KAAK,GAAGuH,IAAI,CAACM,oBAAoB;cACvDb,qBAAqB,CAAChH,KAAK,GAAGuH,IAAI,CAACO,oBAAoB;cACvDb,iBAAiB,CAACjH,KAAK,GAAGuH,IAAI,CAACQ,gBAAgB;cAC/Cb,qBAAqB,CAAClH,KAAK,GAAGuH,IAAI,CAACS,oBAAoB;YAAA;YAAA;cAAA,OAAAP,QAAA,CAAAnC,IAAA;UAAA;QAAA,GAAA+B,OAAA;MAAA,CACxD;MAAA,gBAVKF,kBAAkBA,CAAA;QAAA,OAAAC,KAAA,CAAAnB,KAAA,OAAAD,SAAA;MAAA;IAAA,GAUvB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}