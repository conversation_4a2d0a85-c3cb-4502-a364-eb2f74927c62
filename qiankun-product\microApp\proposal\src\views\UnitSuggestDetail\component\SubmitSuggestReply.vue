<template>
  <div class="SubmitSuggestReply">
    <el-form ref="formRef" :model="form" :rules="rules" inline label-position="top" class="globalForm"
      @submit.enter.prevent>
      <el-form-item label="答复类型" prop="replyType">
        <el-select v-model="form.replyType" placeholder="请选择答复类型" clearable>
          <el-option v-for="item in replyType" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="是否公开" prop="isOpen">
        <el-radio-group v-model="form.isOpen" @change="isOpenChange">
          <el-radio :label="1">公开</el-radio>
          <el-radio :label="0">不公开</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="不公开理由" prop="noOpenReason" v-show="!form.isOpen" class="globalFormTitle">
        <el-input v-model="form.noOpenReason" placeholder="请输入不公开理由" type="textarea" :rows="5" clearable />
      </el-form-item>
      <el-form-item label="上传答复件" prop="fileData" class="globalFormTitle">
        <xyl-upload-file :fileData="fileData" @fileUpload="fileUpload" />
      </el-form-item>
      <div class="GlobalAiChatProposalReply" v-if="whetherAiChat && isAIFunction">
        <div class="GlobalAiChatProposalHead">
          <el-image :src="IntelligentAssistant" loading="lazy" fit="cover" draggable="false" />
          <div class="GlobalAiChatProposalName">答复件符合性评估</div>
        </div>
        <div class="GlobalAiChatProposalLoading" v-if="loading">
          <div class="answerLoading" v-html="loadingIcon"></div>
        </div>
        <div class="GlobalAiChatProposalPonder forbidSelect" @click="ponderShow = !ponderShow" v-if="elTime">
          <div v-html="ponderIcon"></div>
          已深度思考
          <span v-if="elTime !== '1'">（用时 {{ elTime }}）</span>
          <el-icon>
            <ArrowUpBold v-if="ponderShow" />
            <ArrowDownBold v-if="!ponderShow" />
          </el-icon>
        </div>
        <div class="GlobalAiChatProposalPonderContent" v-show="ponderShow">
          <GlobalMarkdown ref="elPonderRef" v-model="elPonderData.content" :content="elPonderData.contentOld" />
        </div>
        <div class="GlobalAiChatProposalContent">
          <GlobalMarkdown ref="elRef" v-model="elData.content" :content="elData.contentOld" />
        </div>
      </div>
      <el-form-item label="内容" prop="content" class="globalFormTitle">
        <TinyMceEditor v-model="form.content" />
      </el-form-item>
      <el-form-item label="意见建议清单答复" class="globalFormTitle" prop="suggestedReplies"
        v-if="form.suggestedReplies && form.suggestedReplies.length > 0">
        <div v-for="(item, index) in form.suggestedReplies" :key="index" class="SuggestedReplyList">
          <div style="width: 8%;">建议{{ index + 1 }}</div>
          <div style="width: 42%;">{{ item.content }}</div>
          <div style="width: 50%;margin-left: 20px;">
            <el-form-item label="答复类型" prop="SuggestedReplyType" class="globalFormTitle">
              <el-select v-model="item.SuggestedReplyType" placeholder="请选择答复类型" clearable>
                <el-option v-for="items in SuggestedReplyType" :key="items.id" :label="items.name" :value="items.id" />
              </el-select>
            </el-form-item>
            <el-form-item label="答复内容" prop="SuggestedReplyContent" style="margin-top: 12px;" class="globalFormTitle">
              <el-input v-model="item.SuggestedReplyContent" placeholder="请输入答复内容" type="textarea" :rows="3"
                clearable />
            </el-form-item>
          </div>
        </div>
      </el-form-item>
      <div class="globalFormButton">
        <el-button type="primary" @click="submitForm(formRef)">提交</el-button>
        <el-button @click="resetForm">取消</el-button>
      </div>
    </el-form>
  </div>
</template>
<script>
export default { name: 'SubmitSuggestReply' }
</script>
<script setup>
import api from '@/api'
import http_stream from 'common/http/stream.js'
import { reactive, ref, onMounted, nextTick } from 'vue'
import { IntelligentAssistant, whetherAiChat } from 'common/js/system_var.js'
import { AIFunctionMethod } from 'common/js/AIFunctionMethod.js'
import { ElMessage } from 'element-plus'
const props = defineProps({
  id: { type: String, default: '' },
  unitId: { type: String, default: '' },
  suggestId: { type: String, default: '' },
  detailsObjectType: { type: String, default: 'handlingPortionId' }
})
const loadingIcon =
  '<svg t="1716976607389" viewBox="0 0 1024 1024" version="1.1" p-id="2362" width="60%" height="60%"><path d="M827.211075 221.676536m-54.351151 0a54.351151 54.351151 0 1 0 108.702302 0 54.351151 54.351151 0 1 0-108.702302 0Z" fill="#2c2c2c" p-id="2363"></path><path d="M940.905298 515.399947m-67.086951 0a67.086952 67.086952 0 1 0 134.173903 0 67.086952 67.086952 0 1 0-134.173903 0Z" fill="#2c2c2c" p-id="2364"></path><path d="M829.755035 810.595334m-78.974766 0a78.974766 78.974766 0 1 0 157.949532 0 78.974766 78.974766 0 1 0-157.949532 0Z" fill="#2c2c2c" p-id="2365"></path><path d="M534.831643 928.64149m-91.48657 0a91.486571 91.486571 0 1 0 182.973141 0 91.486571 91.486571 0 1 0-182.973141 0Z" fill="#2c2c2c" p-id="2366"></path><path d="M243.780191 805.955407m-101.902408 0a101.902408 101.902408 0 1 0 203.804816 0 101.902408 101.902408 0 1 0-203.804816 0Z" fill="#2c2c2c" p-id="2367"></path><path d="M536.623615 107.870315m-107.854315 0a107.854315 107.854315 0 1 0 215.70863 0 107.854315 107.854315 0 1 0-215.70863 0Z" fill="#2c2c2c" p-id="2368"></path><path d="M243.780191 224.220497m-107.854315 0a107.854315 107.854315 0 1 0 215.70863 0 107.854315 107.854315 0 1 0-215.70863 0Z" fill="#2c2c2c" p-id="2369"></path><path d="M129.429978 512.008m-102.766395 0a102.766394 102.766394 0 1 0 205.532789 0 102.766394 102.766394 0 1 0-205.532789 0Z" fill="#2c2c2c" p-id="2370"></path></svg>'
const ponderIcon =
  '<svg t="1741658991857" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="11203" width="16" height="16"><path d="M886.592 369.152c-42.24 89.28-116.48 190.848-211.456 285.76-94.976 94.976-196.48 169.216-285.824 211.52-44.352 20.992-88.96 35.712-130.24 39.168-40.832 3.456-87.68-3.712-122.432-38.4-34.752-34.816-41.92-81.664-38.464-122.496 3.456-41.216 18.176-85.888 39.168-130.24 42.304-89.28 116.544-190.912 211.456-285.824 94.976-94.912 196.544-169.152 285.824-211.456 44.416-21.056 88.96-35.712 130.24-39.232 40.832-3.456 87.68 3.712 122.496 38.464 34.752 34.752 41.92 81.664 38.4 122.496-3.456 41.216-18.112 85.824-39.168 130.24zM629.888 609.664c182.272-182.272 277.312-382.848 212.224-448-65.152-65.088-265.728 29.952-448 212.224-182.336 182.336-277.376 382.912-212.224 448 65.088 65.152 265.664-29.888 448-212.224z" p-id="11204"></path><path d="M137.344 369.152c42.304 89.28 116.544 190.848 211.52 285.76 94.912 94.976 196.48 169.216 285.76 211.52 44.416 20.992 88.96 35.712 130.24 39.168 40.832 3.456 87.68-3.712 122.496-38.4 34.752-34.816 41.92-81.664 38.4-122.496-3.456-41.216-18.112-85.888-39.168-130.24-42.24-89.28-116.48-190.912-211.456-285.824-94.912-94.912-196.48-169.152-285.824-211.456-44.352-21.056-88.96-35.712-130.24-39.232-40.832-3.456-87.68 3.712-122.432 38.464-34.752 34.752-41.92 81.664-38.464 122.496 3.456 41.216 18.176 85.824 39.168 130.24z m256.768 240.512c-182.336-182.272-277.376-382.848-212.224-448 65.088-65.088 265.664 29.952 448 212.224 182.272 182.336 277.312 382.912 212.224 448-65.152 65.152-265.728-29.888-448-212.224z" p-id="11205"></path><path d="M576 512a64 64 0 1 1-128 0 64 64 0 0 1 128 0z" p-id="11206"></path></svg>'
const emit = defineEmits(['callback'])

const formRef = ref()
const form = reactive({
  id: '',
  replyType: '',
  isOpen: 1,
  noOpenReason: '',
  fileData: [],
  content: '',
  suggestedReplies: [ // 将 SuggestedReplyList 移到 form 中
    { id: '1', content: '', SuggestedReplyType: '', SuggestedReplyContent: '' },
    { id: '2', content: ' ', SuggestedReplyType: '', SuggestedReplyContent: '' }
  ]
})
const SuggestedReplyType = ref([
  { id: 'A', name: 'A类' },
  { id: 'B', name: 'B类' },
  { id: 'C', name: 'C类' }
])
const rules = reactive({
  replyType: [{ required: true, message: '请选择答复类型', trigger: ['blur', 'change'] }],
  isOpen: [{ required: true, message: '请选择是否公开', trigger: ['blur', 'change'] }],
  fileData: [{ required: true, message: '请上传答复件', trigger: ['blur', 'change'] }],
  noOpenReason: [{ required: false, message: '请输入不公开理由', trigger: ['blur', 'change'] }],
  content: [{ required: true, message: '请输入内容', trigger: ['blur', 'change'] }]
})
const details = ref({})
const fileData = ref([])
const replyType = ref([])

const ponderShow = ref(true)
const elTime = ref('')
const elRef = ref()
const elPonderRef = ref()
const elData = ref({ content: '', contentOld: '' })
const elPonderData = ref({ content: '', contentOld: '' })

let currentRequest = null
const loading = ref(false)
const isStreaming = ref(false)
let startTime = null
let endTime = null

const guid = () => {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
    var r = (Math.random() * 16) | 0,
      v = c == 'x' ? r : (r & 0x3) | 0x8
    return v.toString(16)
  })
}
const { isAIFunction } = AIFunctionMethod('ai-proposal-answer')
onMounted(() => {
  dictionaryData()
  proposalInventoryList()
  if (props.id) { handingPortionAnswerInfo() }
  suggestionInfo()
})

const dictionaryData = async () => {
  const res = await api.dictionaryData({ dictCodes: ['suggestion_answer_type'] })
  var { data } = res
  replyType.value = data.suggestion_answer_type
}

const proposalInventoryList = async () => {
  const res = await api.proposalInventoryList({ pageNo: '1', pageSize: '999', query: { suggestionId: props.suggestId } })
  form.suggestedReplies = res.data
}

const isOpenChange = () => {
  rules.noOpenReason = [{ required: false, message: '请输入不公开理由', trigger: ['blur', 'change'] }]
  if (!form.isOpen) {
    rules.noOpenReason = [{ required: true, message: '请输入不公开理由', trigger: ['blur', 'change'] }]
  }
}
const fileUpload = (file) => {
  form.fileData = file.map((v) => v.id)
  fileData.value = file
  formRef.value.validateField('fileData')
  nextTick(() => {
    elTime.value = ''
    elData.value = { content: '', contentOld: '' }
    elPonderData.value = { content: '', contentOld: '' }
    elRef.value?.clearContent()
    elPonderRef.value?.clearContent()
    handleStopMessage()
    if (fileData.value.length) {
      handleHttpStream()
    }
  })
}
const suggestionInfo = async () => {
  const { data } = await api.suggestionInfo({ detailId: props.suggestId })
  details.value = data
}
const handingPortionAnswerInfo = async () => {
  var params = {}
  params[props.detailsObjectType] = props.id
  const res = await api.handingPortionAnswerInfo(params)
  var { data } = res
  form.id = data.id
  form.replyType = data.suggestionAnswerType?.value
  form.isOpen = data.isOpen
  form.noOpenReason = data.noOpenReason
  form.content = data.content
  fileData.value = data.attachments || []
  form.fileData = data.attachments.map((v) => v.id)
}
const submitForm = async (formEl) => {
  if (!formEl) return
  await formEl.validate((valid, fields) => {
    if (valid) {
      globalJson()
    } else {
      ElMessage({ type: 'warning', message: '请根据提示信息完善字段内容！' })
    }
  })
}
const globalJson = async () => {
  const { code } = await api.globalJson(
    props.id ? '/cppcc/handingPortionAnswer/edit' : '/cppcc/handingPortionAnswer/add',
    {
      form: {
        id: form.id,
        handlingPortionId: props.unitId,
        suggestionId: props.suggestId,
        suggestionAnswerType: form.replyType,
        isOpen: form.isOpen,
        noOpenReason: form.isOpen ? '' : form.noOpenReason,
        content: form.content,
        attachmentIds: fileData.value.map((v) => v.id)
      }
    }
  )
  if (code === 200) {
    ElMessage({ type: 'success', message: props.id ? '编辑成功' : '新增成功' })
    emit('callback', true)
  }
}
const resetForm = () => {
  emit('callback')
}

const formatDuring = (mss) => {
  const days = parseInt(mss / (1000 * 60 * 60 * 24))
  const hours = parseInt((mss % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
  const minutes = parseInt((mss % (1000 * 60 * 60)) / (1000 * 60))
  const seconds = (mss % (1000 * 60)) / 1000
  var time = ''
  if (days > 0) time += `${days} 天 `
  if (hours > 0) time += `${hours} 小时 `
  if (minutes > 0) time += `${minutes} 分钟 `
  if (seconds > 0) time += `${seconds} 秒 `
  return time
}
const handleHttpStream = async () => {
  if (!whetherAiChat.value) return
  startTime = new Date()
  loading.value = true
  isStreaming.value = true
  try {
    const AiChatParam = {
      chatBusinessScene: 'ai-proposal-answer',
      chatId: guid(),
      question: '提案答复件智能检测',
      businessId: props.suggestId,
      attachmentIds: fileData.value.map((v) => v.id).join(',')
    }
    currentRequest = http_stream('/aigpt/chatStream', {
      body: JSON.stringify(AiChatParam),
      onMessage (event) {
        // if (event.data === '{\"status\":\"running\",\"name\":\"AI 对话\"}') loading.value = false
        loading.value = false
        if (event.data !== '[DONE]') {
          const data = JSON.parse(event.data)
          if (Array.isArray(data)) {
            console.log('[]', data)
          } else {
            // console.log('{}', data)
            const choice = data?.choices || [{}]
            const details = choice[0]?.delta || {}
            if (Object.prototype.hasOwnProperty.call(details, 'reasoning_content')) {
              elPonderRef.value?.enqueueRender(details.reasoning_content || '')
              if (elTime.value) {
                startTime = null
                endTime = null
              } else {
                endTime = new Date()
                const executionTime = endTime - startTime
                elTime.value = formatDuring(executionTime)
              }
            }
            if (Object.prototype.hasOwnProperty.call(details, 'content')) {
              elRef.value?.enqueueRender(details.content || '')
            }
          }
        } else {
          // console.log(event.data)
          elRef.value?.enqueueRender('')
          isStreaming.value = false
        }
      },
      onError (err) {
        console.log('流式接口错误:', err)
      },
      onClose () {
        loading.value = false
        isStreaming.value = false
        console.log('流式接口关闭')
      }
    })
    await currentRequest.promise
  } catch (error) {
    loading.value = false
    isStreaming.value = false
    elRef.value?.enqueueRender('服务器繁忙，请稍后再试。')
    console.error('启动流式接口失败:', error)
  } finally {
    // currentRequest = null
  }
}
const handleCloseMessage = () => {
  currentRequest = null
  loading.value = false
  isStreaming.value = false
}
const handleStopMessage = () => {
  if (currentRequest) {
    currentRequest.abort()
    loading.value = false
    isStreaming.value = false
    console.log('启动流式接口停止')
  }
  handleCloseMessage()
}
</script>
<style lang="scss">
.SubmitSuggestReply {
  width: 990px;
}

.SuggestedReplyList {
  display: flex;
  align-items: center;
  width: 100%;
  border-bottom: 1px solid #ccc;
  padding: 6px 20px 20px 20px;
}

.GlobalAiChatProposalReply {
  width: calc(100% - 20px);
  height: 100%;
  padding: var(--zy-distance-two);
  border-radius: var(--el-border-radius-base);
  border: 1px dashed var(--zy-el-color-primary);
  margin-bottom: 18px !important;

  .GlobalAiChatProposalHead {
    width: 100%;
    display: flex;
    align-items: flex-end;
    padding-bottom: 12px;

    .zy-el-image {
      width: 52px;
      height: 52px;
    }

    .GlobalAiChatProposalName {
      width: 100%;
      font-weight: bold;
      padding: 0 0 10px 12px;
      line-height: var(--zy-line-height);
      font-size: var(--zy-name-font-size);
    }
  }

  .GlobalAiChatProposalLoading {
    width: 100%;
    height: 32px;
    position: relative;

    @keyframes circleRoate {
      from {
        transform: translateY(-50%) rotate(0deg);
      }

      to {
        transform: translateY(-50%) rotate(360deg);
      }
    }

    .answerLoading {
      width: calc((var(--zy-text-font-size) * var(--zy-line-height)) + 20px);
      height: calc((var(--zy-text-font-size) * var(--zy-line-height)) + 20px);
      position: absolute;
      top: 50%;
      left: 0;
      z-index: 3;
      display: flex;
      align-items: center;
      justify-content: center;
      animation: circleRoate 1s infinite linear;

      path {
        fill: var(--zy-el-color-primary);
      }
    }

    .answerLoading+.QuestionsAndAnswersChatText {
      color: var(--zy-el-color-primary);
      padding-left: calc((var(--zy-text-font-size) * var(--zy-line-height)) + 20px);
    }
  }

  .GlobalAiChatProposalPonder {
    width: 100%;
    height: 32px;
    display: flex;
    align-items: center;
    padding: 0 12px;
    border-radius: var(--el-border-radius-base);
    line-height: var(--zy-line-height);
    font-size: var(--zy-text-font-size);
    color: var(--zy-el-text-color-primary);
    background: var(--zy-el-color-info-light-9);
    margin-bottom: 12px;
    cursor: pointer;

    div {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 6px;
    }

    span {
      display: flex;
      align-items: center;
      justify-content: center;
      color: var(--zy-el-text-color-primary);
    }
  }

  .GlobalAiChatProposalPonderContent {
    width: 100%;
    padding-left: 12px;
    border-left: 2px solid var(--zy-el-border-color);
    margin-bottom: 12px;

    * {
      color: var(--zy-el-text-color-secondary);
    }
  }

  .GlobalAiChatProposalContent {
    width: 100%;
  }
}
</style>
