{"ast": null, "code": "import { renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, createCommentVNode as _createCommentVNode } from \"vue\";\nvar _hoisted_1 = {\n  class: \"suggestPrint\",\n  ref: \"printRef\"\n};\nvar _hoisted_2 = {\n  class: \"suggestPrintType\"\n};\nvar _hoisted_3 = {\n  class: \"suggestPrintItem\"\n};\nvar _hoisted_4 = {\n  class: \"suggestPrintItemContent\"\n};\nvar _hoisted_5 = {\n  class: \"suggestPrintItemFlex\"\n};\nvar _hoisted_6 = {\n  class: \"suggestPrintItem\"\n};\nvar _hoisted_7 = {\n  class: \"suggestPrintItemContent\"\n};\nvar _hoisted_8 = {\n  class: \"suggestPrintItem\"\n};\nvar _hoisted_9 = {\n  class: \"suggestPrintItemContent\"\n};\nvar _hoisted_10 = {\n  class: \"suggestPrintItem\"\n};\nvar _hoisted_11 = {\n  class: \"suggestPrintItemContent\"\n};\nvar _hoisted_12 = {\n  class: \"suggestPrintItem\"\n};\nvar _hoisted_13 = {\n  class: \"suggestPrintItemContent\"\n};\nvar _hoisted_14 = {\n  class: \"suggestPrintItemFlex suggestPrintItemInfo\"\n};\nvar _hoisted_15 = {\n  class: \"suggestPrintItem\"\n};\nvar _hoisted_16 = {\n  class: \"suggestPrintItemContent\"\n};\nvar _hoisted_17 = {\n  class: \"suggestPrintItem\"\n};\nvar _hoisted_18 = {\n  class: \"suggestPrintItemContent\"\n};\nvar _hoisted_19 = {\n  class: \"suggestPrintItem suggestPrintItemJoin\"\n};\nvar _hoisted_20 = {\n  class: \"suggestPrintItemContent\"\n};\nvar _hoisted_21 = {\n  class: \"suggestPrintItem\"\n};\nvar _hoisted_22 = {\n  class: \"suggestPrintItemContent\"\n};\nvar _hoisted_23 = {\n  class: \"suggestPrintItemTime\"\n};\nvar _hoisted_24 = {\n  key: 0\n};\nvar _hoisted_25 = [\"rowspan\"];\nvar _hoisted_26 = {\n  key: 1\n};\nvar _hoisted_27 = [\"rowspan\"];\nvar _hoisted_28 = {\n  key: 2\n};\nvar _hoisted_29 = [\"rowspan\"];\nvar _hoisted_30 = {\n  key: 3\n};\nvar _hoisted_31 = [\"rowspan\"];\nvar _hoisted_32 = {\n  key: 4\n};\nvar _hoisted_33 = [\"rowspan\"];\nvar _hoisted_34 = {\n  key: 5\n};\nvar _hoisted_35 = {\n  key: 0\n};\nvar _hoisted_36 = {\n  colspan: \"2\"\n};\nvar _hoisted_37 = [\"innerHTML\"];\nvar _hoisted_38 = {\n  colspan: \"2\"\n};\nvar _hoisted_39 = {\n  colspan: \"2\"\n};\nvar _hoisted_40 = {\n  colspan: \"3\"\n};\nvar _hoisted_41 = {\n  colspan: \"2\"\n};\nvar _hoisted_42 = {\n  colspan: \"2\"\n};\nvar _hoisted_43 = {\n  key: 0\n};\nvar _hoisted_44 = {\n  key: 1\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.printData, function (item) {\n    return _openBlock(), _createElementBlock(\"div\", {\n      class: \"suggestPrintBody\",\n      key: item.id\n    }, [_createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"div\", null, [_cache[0] || (_cache[0] = _createElementVNode(\"span\", null, \"案号：\", -1 /* HOISTED */)), _createTextVNode(_toDisplayString(item.serialNumber), 1 /* TEXT */)]), _createElementVNode(\"div\", null, [_cache[1] || (_cache[1] = _createElementVNode(\"span\", null, \"类别：\", -1 /* HOISTED */)), _createTextVNode(_toDisplayString(item.bigThemeName), 1 /* TEXT */)])]), _createElementVNode(\"div\", {\n      class: \"suggestPrintName\",\n      onClick: $setup.handlePrint\n    }, _toDisplayString(item.redHeadTitle), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_3, [_cache[2] || (_cache[2] = _createElementVNode(\"span\", {\n      class: \"suggestPrintItemTitle\"\n    }, \"提案者\", -1 /* HOISTED */)), _cache[3] || (_cache[3] = _createElementVNode(\"span\", {\n      class: \"suggestPrintItemColon\"\n    }, \"：\", -1 /* HOISTED */)), _createElementVNode(\"span\", _hoisted_4, _toDisplayString(item.suggestUserName), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_5, [_createElementVNode(\"div\", _hoisted_6, [_cache[4] || (_cache[4] = _createElementVNode(\"span\", {\n      class: \"suggestPrintItemTitle\"\n    }, \"委员证号\", -1 /* HOISTED */)), _cache[5] || (_cache[5] = _createElementVNode(\"span\", {\n      class: \"suggestPrintItemColon\"\n    }, \"：\", -1 /* HOISTED */)), _createElementVNode(\"span\", _hoisted_7, _toDisplayString(item.cardNumberNpc), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_8, [_cache[6] || (_cache[6] = _createElementVNode(\"span\", {\n      class: \"suggestPrintItemTitle\"\n    }, \"界别\", -1 /* HOISTED */)), _cache[7] || (_cache[7] = _createElementVNode(\"span\", {\n      class: \"suggestPrintItemColon\"\n    }, \"：\", -1 /* HOISTED */)), _createElementVNode(\"span\", _hoisted_9, _toDisplayString(item.delegationName), 1 /* TEXT */)])]), _createElementVNode(\"div\", _hoisted_10, [_cache[8] || (_cache[8] = _createElementVNode(\"span\", {\n      class: \"suggestPrintItemTitle\"\n    }, \"单位职务\", -1 /* HOISTED */)), _cache[9] || (_cache[9] = _createElementVNode(\"span\", {\n      class: \"suggestPrintItemColon\"\n    }, \"：\", -1 /* HOISTED */)), _createElementVNode(\"span\", _hoisted_11, _toDisplayString(item.position), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_12, [_cache[10] || (_cache[10] = _createElementVNode(\"span\", {\n      class: \"suggestPrintItemTitle\"\n    }, \"通讯地址\", -1 /* HOISTED */)), _cache[11] || (_cache[11] = _createElementVNode(\"span\", {\n      class: \"suggestPrintItemColon\"\n    }, \"：\", -1 /* HOISTED */)), _createElementVNode(\"span\", _hoisted_13, _toDisplayString(item.callAddress), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_14, [_createElementVNode(\"div\", _hoisted_15, [_cache[12] || (_cache[12] = _createElementVNode(\"span\", {\n      class: \"suggestPrintItemTitle\"\n    }, \"邮政编码\", -1 /* HOISTED */)), _cache[13] || (_cache[13] = _createElementVNode(\"span\", {\n      class: \"suggestPrintItemColon\"\n    }, \"：\", -1 /* HOISTED */)), _createElementVNode(\"span\", _hoisted_16, _toDisplayString(item.postcode), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_17, [_cache[14] || (_cache[14] = _createElementVNode(\"span\", {\n      class: \"suggestPrintItemTitle\"\n    }, \"联系电话\", -1 /* HOISTED */)), _cache[15] || (_cache[15] = _createElementVNode(\"span\", {\n      class: \"suggestPrintItemColon\"\n    }, \"：\", -1 /* HOISTED */)), _createElementVNode(\"span\", _hoisted_18, _toDisplayString(item.mobile), 1 /* TEXT */)])]), _createElementVNode(\"div\", _hoisted_19, [_cache[16] || (_cache[16] = _createElementVNode(\"span\", {\n      class: \"suggestPrintItemTitle\"\n    }, \"联名人\", -1 /* HOISTED */)), _cache[17] || (_cache[17] = _createElementVNode(\"span\", {\n      class: \"suggestPrintItemColon\"\n    }, \"：\", -1 /* HOISTED */)), _createElementVNode(\"span\", _hoisted_20, _toDisplayString(item.joinUser), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_21, [_cache[18] || (_cache[18] = _createElementVNode(\"span\", {\n      class: \"suggestPrintItemTitle\"\n    }, \"提案标题\", -1 /* HOISTED */)), _cache[19] || (_cache[19] = _createElementVNode(\"span\", {\n      class: \"suggestPrintItemColon\"\n    }, \"：\", -1 /* HOISTED */)), _createElementVNode(\"span\", _hoisted_22, _toDisplayString(item.titleone), 1 /* TEXT */)]), (_openBlock(true), _createElementBlock(_Fragment, null, _renderList(item.titletwoArr, function (text, index) {\n      return _openBlock(), _createElementBlock(\"div\", {\n        class: \"suggestPrintItemTwo\",\n        key: index\n      }, _toDisplayString(text), 1 /* TEXT */);\n    }), 128 /* KEYED_FRAGMENT */)), _createElementVNode(\"div\", _hoisted_23, \"提出时间：\" + _toDisplayString(item.submitDate), 1 /* TEXT */), _cache[29] || (_cache[29] = _createElementVNode(\"div\", {\n      style: {\n        \"page-break-after\": \"always\"\n      }\n    }, null, -1 /* HOISTED */)), _cache[30] || (_cache[30] = _createElementVNode(\"div\", {\n      class: \"mainModulePageTableName\"\n    }, \"其他相关信息\", -1 /* HOISTED */)), _createElementVNode(\"table\", null, [_createElementVNode(\"tbody\", null, [item.suggestSurveyTypeName ? (_openBlock(), _createElementBlock(\"tr\", _hoisted_24, [_createElementVNode(\"td\", {\n      rowspan: $setup.rowspan(item)\n    }, \"相关情况\", 8 /* PROPS */, _hoisted_25), _createElementVNode(\"td\", null, _toDisplayString(item.suggestSurveyTypeName), 1 /* TEXT */), _createElementVNode(\"td\", null, _toDisplayString(item.suggestSurveyTypeView), 1 /* TEXT */)])) : _createCommentVNode(\"v-if\", true), item.notHandleTimeTypeName ? (_openBlock(), _createElementBlock(\"tr\", _hoisted_26, [!item.suggestSurveyTypeName ? (_openBlock(), _createElementBlock(\"td\", {\n      key: 0,\n      rowspan: $setup.rowspan(item)\n    }, \"相关情况\", 8 /* PROPS */, _hoisted_27)) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"td\", null, _toDisplayString(item.notHandleTimeTypeName), 1 /* TEXT */), _createElementVNode(\"td\", null, _toDisplayString(item.notHandleTimeTypeView), 1 /* TEXT */)])) : _createCommentVNode(\"v-if\", true), item.suggestOpenTypeName ? (_openBlock(), _createElementBlock(\"tr\", _hoisted_28, [!item.suggestSurveyTypeName && !item.notHandleTimeTypeName ? (_openBlock(), _createElementBlock(\"td\", {\n      key: 0,\n      rowspan: $setup.rowspan(item)\n    }, \"相关情况\", 8 /* PROPS */, _hoisted_29)) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"td\", null, _toDisplayString(item.suggestOpenTypeName), 1 /* TEXT */), _createElementVNode(\"td\", null, _toDisplayString(item.suggestOpenTypeView), 1 /* TEXT */)])) : _createCommentVNode(\"v-if\", true), item.isMakeMineJobName ? (_openBlock(), _createElementBlock(\"tr\", _hoisted_30, [!item.suggestSurveyTypeName && !item.notHandleTimeTypeName && !item.suggestOpenTypeName ? (_openBlock(), _createElementBlock(\"td\", {\n      key: 0,\n      rowspan: $setup.rowspan(item)\n    }, \"相关情况\", 8 /* PROPS */, _hoisted_31)) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"td\", null, _toDisplayString(item.isMakeMineJobName), 1 /* TEXT */), _createElementVNode(\"td\", null, _toDisplayString(item.isMakeMineJobView), 1 /* TEXT */)])) : _createCommentVNode(\"v-if\", true), item.isHopeEnhanceTalkName ? (_openBlock(), _createElementBlock(\"tr\", _hoisted_32, [!item.suggestSurveyTypeName && !item.notHandleTimeTypeName && !item.suggestOpenTypeName && !item.isMakeMineJobName ? (_openBlock(), _createElementBlock(\"td\", {\n      key: 0,\n      rowspan: $setup.rowspan(item)\n    }, \" 相关情况\", 8 /* PROPS */, _hoisted_33)) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"td\", null, _toDisplayString(item.isHopeEnhanceTalkName), 1 /* TEXT */), _createElementVNode(\"td\", null, _toDisplayString(item.isHopeEnhanceTalkView), 1 /* TEXT */)])) : _createCommentVNode(\"v-if\", true), item.isNeedPaperAnswerName ? (_openBlock(), _createElementBlock(\"tr\", _hoisted_34, [!item.suggestSurveyTypeName && !item.notHandleTimeTypeName && !item.suggestOpenTypeName && !item.isMakeMineJobName && !item.isHopeEnhanceTalkName ? (_openBlock(), _createElementBlock(\"td\", _hoisted_35, \" 相关情况\")) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"td\", null, _toDisplayString(item.isNeedPaperAnswerName), 1 /* TEXT */), _createElementVNode(\"td\", null, _toDisplayString(item.isNeedPaperAnswerView), 1 /* TEXT */)])) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"tr\", null, [_cache[20] || (_cache[20] = _createElementVNode(\"td\", null, \"希望送交的承办单位（仅供参考）\", -1 /* HOISTED */)), _createElementVNode(\"td\", _hoisted_36, _toDisplayString(item.hopeHandleOfficeName), 1 /* TEXT */)])])]), _createElementVNode(\"div\", {\n      class: \"suggestPrintContent\",\n      innerHTML: item.content\n    }, null, 8 /* PROPS */, _hoisted_37), _cache[31] || (_cache[31] = _createElementVNode(\"div\", {\n      style: {\n        \"page-break-after\": \"always\"\n      }\n    }, null, -1 /* HOISTED */)), _cache[32] || (_cache[32] = _createElementVNode(\"div\", {\n      class: \"mainModulePageTableName\"\n    }, \"联名人信息表\", -1 /* HOISTED */)), _createElementVNode(\"table\", null, [_createElementVNode(\"tbody\", null, [_cache[21] || (_cache[21] = _createElementVNode(\"tr\", null, [_createElementVNode(\"td\", null, \"姓名\"), _createElementVNode(\"td\", null, \"界别\"), _createElementVNode(\"td\", null, \"委员证号\"), _createElementVNode(\"td\", null, \"单位职务\"), _createElementVNode(\"td\", null, \"联系方式\"), _createElementVNode(\"td\", null, \"通讯地址\")], -1 /* HOISTED */)), (_openBlock(true), _createElementBlock(_Fragment, null, _renderList(item.joinUsers, function (row) {\n      return _openBlock(), _createElementBlock(\"tr\", {\n        key: row.userId\n      }, [_createElementVNode(\"td\", null, _toDisplayString(row.userName), 1 /* TEXT */), _createElementVNode(\"td\", null, _toDisplayString(row.sector), 1 /* TEXT */), _createElementVNode(\"td\", null, _toDisplayString(row.cardNumber), 1 /* TEXT */), _createElementVNode(\"td\", null, _toDisplayString(row.position), 1 /* TEXT */), _createElementVNode(\"td\", null, _toDisplayString(row.mobile), 1 /* TEXT */), _createElementVNode(\"td\", null, _toDisplayString(row.callAddress), 1 /* TEXT */)]);\n    }), 128 /* KEYED_FRAGMENT */))])]), _cache[33] || (_cache[33] = _createElementVNode(\"div\", {\n      class: \"mainModulePageTableName\"\n    }, \"提案联系人\", -1 /* HOISTED */)), _createElementVNode(\"table\", null, [_createElementVNode(\"tbody\", null, [_cache[22] || (_cache[22] = _createElementVNode(\"tr\", null, [_createElementVNode(\"td\", {\n      colspan: \"2\"\n    }, \"姓名\"), _createElementVNode(\"td\", {\n      colspan: \"2\"\n    }, \"联系电话\"), _createElementVNode(\"td\", {\n      colspan: \"3\"\n    }, \"通讯地址\")], -1 /* HOISTED */)), (_openBlock(true), _createElementBlock(_Fragment, null, _renderList(item.contacters, function (row) {\n      return _openBlock(), _createElementBlock(\"tr\", {\n        key: row.id\n      }, [_createElementVNode(\"td\", _hoisted_38, _toDisplayString(row.contacterName), 1 /* TEXT */), _createElementVNode(\"td\", _hoisted_39, _toDisplayString(row.contacterMobile), 1 /* TEXT */), _createElementVNode(\"td\", _hoisted_40, _toDisplayString(row.contacterAddress), 1 /* TEXT */)]);\n    }), 128 /* KEYED_FRAGMENT */))])]), _cache[34] || (_cache[34] = _createElementVNode(\"div\", {\n      class: \"mainModulePageTableName\"\n    }, \"审查和办理信息表\", -1 /* HOISTED */)), _createElementVNode(\"table\", null, [_createElementVNode(\"tbody\", null, [_createElementVNode(\"tr\", null, [_cache[23] || (_cache[23] = _createElementVNode(\"td\", null, \"本提案目前状态\", -1 /* HOISTED */)), _createElementVNode(\"td\", _hoisted_41, _toDisplayString(item.currentStatus), 1 /* TEXT */)]), _createElementVNode(\"tr\", null, [_cache[24] || (_cache[24] = _createElementVNode(\"td\", null, \"审查情况\", -1 /* HOISTED */)), _createElementVNode(\"td\", _hoisted_42, _toDisplayString(item.verifyStatus), 1 /* TEXT */)]), item.mainHandleOffice ? (_openBlock(), _createElementBlock(\"tr\", _hoisted_43, [_cache[25] || (_cache[25] = _createElementVNode(\"td\", {\n      rowspan: \"2\"\n    }, \"办理单位\", -1 /* HOISTED */)), _cache[26] || (_cache[26] = _createElementVNode(\"td\", null, \"主办\", -1 /* HOISTED */)), _createElementVNode(\"td\", null, _toDisplayString(item.mainHandleOffice), 1 /* TEXT */)])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" <tr v-if=\\\"item.mainHandleOffice\\\">\\r\\n            <td>协办</td>\\r\\n            <td>{{ item.assistHandleOffice }}</td>\\r\\n          </tr> \"), item.publishHandleOffice ? (_openBlock(), _createElementBlock(\"tr\", _hoisted_44, [_cache[27] || (_cache[27] = _createElementVNode(\"td\", null, \"办理单位\", -1 /* HOISTED */)), _cache[28] || (_cache[28] = _createElementVNode(\"td\", null, \"分办\", -1 /* HOISTED */)), _createElementVNode(\"td\", null, _toDisplayString(item.publishHandleOffice), 1 /* TEXT */)])) : _createCommentVNode(\"v-if\", true)])]), _cache[35] || (_cache[35] = _createElementVNode(\"div\", {\n      style: {\n        \"page-break-after\": \"always\"\n      }\n    }, null, -1 /* HOISTED */))]);\n  }), 128 /* KEYED_FRAGMENT */))], 512 /* NEED_PATCH */);\n}", "map": {"version": 3, "names": ["class", "ref", "key", "colspan", "_createElementBlock", "_hoisted_1", "_Fragment", "_renderList", "$setup", "printData", "item", "id", "_createElementVNode", "_hoisted_2", "_createTextVNode", "_toDisplayString", "serialNumber", "bigThemeName", "onClick", "handlePrint", "redHeadTitle", "_hoisted_3", "_hoisted_4", "suggestUserName", "_hoisted_5", "_hoisted_6", "_hoisted_7", "cardNumberNpc", "_hoisted_8", "_hoisted_9", "delegation<PERSON>ame", "_hoisted_10", "_hoisted_11", "position", "_hoisted_12", "_hoisted_13", "call<PERSON>dd<PERSON>", "_hoisted_14", "_hoisted_15", "_hoisted_16", "postcode", "_hoisted_17", "_hoisted_18", "mobile", "_hoisted_19", "_hoisted_20", "joinUser", "_hoisted_21", "_hoisted_22", "titleone", "titletwoArr", "text", "index", "_hoisted_23", "submitDate", "style", "suggestSurveyTypeName", "_hoisted_24", "rowspan", "_hoisted_25", "suggestSurveyTypeView", "_createCommentVNode", "notHandleTimeTypeName", "_hoisted_26", "_hoisted_27", "notHandleTimeTypeView", "suggestOpenTypeName", "_hoisted_28", "_hoisted_29", "suggestOpenTypeView", "isMakeMineJobName", "_hoisted_30", "_hoisted_31", "isMakeMineJobView", "isHopeEnhanceTalkName", "_hoisted_32", "_hoisted_33", "isHopeEnhanceTalkView", "isNeedPaperAnswerName", "_hoisted_34", "_hoisted_35", "isNeedPaperAnswerView", "_hoisted_36", "hopeHandleOfficeName", "innerHTML", "content", "_hoisted_37", "joinUsers", "row", "userId", "userName", "sector", "cardNumber", "contacters", "_hoisted_38", "contacterName", "_hoisted_39", "contacterMobile", "_hoisted_40", "contacter<PERSON><PERSON><PERSON>", "_hoisted_41", "currentStatus", "_hoisted_42", "verifyStatus", "mainHandleOffice", "_hoisted_43", "publishHandleOffice", "_hoisted_44"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\components\\suggestPrint\\suggestPrint.vue"], "sourcesContent": ["<template>\r\n  <div class=\"suggestPrint\" ref=\"printRef\">\r\n    <div class=\"suggestPrintBody\" v-for=\"item in printData\" :key=\"item.id\">\r\n      <div class=\"suggestPrintType\">\r\n        <div><span>案号：</span>{{ item.serialNumber }}</div>\r\n        <div><span>类别：</span>{{ item.bigThemeName }}</div>\r\n      </div>\r\n      <div class=\"suggestPrintName\" @click=\"handlePrint\">{{ item.redHeadTitle }}</div>\r\n      <div class=\"suggestPrintItem\">\r\n        <span class=\"suggestPrintItemTitle\">提案者</span>\r\n        <span class=\"suggestPrintItemColon\">：</span>\r\n        <span class=\"suggestPrintItemContent\">{{ item.suggestUserName }}</span>\r\n      </div>\r\n      <div class=\"suggestPrintItemFlex\">\r\n        <div class=\"suggestPrintItem\">\r\n          <span class=\"suggestPrintItemTitle\">委员证号</span>\r\n          <span class=\"suggestPrintItemColon\">：</span>\r\n          <span class=\"suggestPrintItemContent\">{{ item.cardNumberNpc }}</span>\r\n        </div>\r\n        <div class=\"suggestPrintItem\">\r\n          <span class=\"suggestPrintItemTitle\">界别</span>\r\n          <span class=\"suggestPrintItemColon\">：</span>\r\n          <span class=\"suggestPrintItemContent\">{{ item.delegationName }}</span>\r\n        </div>\r\n      </div>\r\n      <div class=\"suggestPrintItem\">\r\n        <span class=\"suggestPrintItemTitle\">单位职务</span>\r\n        <span class=\"suggestPrintItemColon\">：</span>\r\n        <span class=\"suggestPrintItemContent\">{{ item.position }}</span>\r\n      </div>\r\n      <div class=\"suggestPrintItem\">\r\n        <span class=\"suggestPrintItemTitle\">通讯地址</span>\r\n        <span class=\"suggestPrintItemColon\">：</span>\r\n        <span class=\"suggestPrintItemContent\">{{ item.callAddress }}</span>\r\n      </div>\r\n      <div class=\"suggestPrintItemFlex suggestPrintItemInfo\">\r\n        <div class=\"suggestPrintItem\">\r\n          <span class=\"suggestPrintItemTitle\">邮政编码</span>\r\n          <span class=\"suggestPrintItemColon\">：</span>\r\n          <span class=\"suggestPrintItemContent\">{{ item.postcode }}</span>\r\n        </div>\r\n        <div class=\"suggestPrintItem\">\r\n          <span class=\"suggestPrintItemTitle\">联系电话</span>\r\n          <span class=\"suggestPrintItemColon\">：</span>\r\n          <span class=\"suggestPrintItemContent\">{{ item.mobile }}</span>\r\n        </div>\r\n      </div>\r\n      <div class=\"suggestPrintItem suggestPrintItemJoin\">\r\n        <span class=\"suggestPrintItemTitle\">联名人</span>\r\n        <span class=\"suggestPrintItemColon\">：</span>\r\n        <span class=\"suggestPrintItemContent\">{{ item.joinUser }}</span>\r\n      </div>\r\n      <div class=\"suggestPrintItem\">\r\n        <span class=\"suggestPrintItemTitle\">提案标题</span>\r\n        <span class=\"suggestPrintItemColon\">：</span>\r\n        <span class=\"suggestPrintItemContent\">{{ item.titleone }}</span>\r\n      </div>\r\n      <div class=\"suggestPrintItemTwo\" v-for=\"(text, index) in item.titletwoArr\" :key=\"index\">{{ text }}</div>\r\n      <div class=\"suggestPrintItemTime\">提出时间：{{ item.submitDate }}</div>\r\n      <div style=\"page-break-after:always\"></div>\r\n      <div class=\"mainModulePageTableName\">其他相关信息</div>\r\n      <table>\r\n        <tbody>\r\n          <tr v-if=\"item.suggestSurveyTypeName\">\r\n            <td :rowspan=\"rowspan(item)\">相关情况</td>\r\n            <td>{{ item.suggestSurveyTypeName }}</td>\r\n            <td>{{ item.suggestSurveyTypeView }}</td>\r\n          </tr>\r\n          <tr v-if=\"item.notHandleTimeTypeName\">\r\n            <td :rowspan=\"rowspan(item)\" v-if=\"!item.suggestSurveyTypeName\">相关情况</td>\r\n            <td>{{ item.notHandleTimeTypeName }}</td>\r\n            <td>{{ item.notHandleTimeTypeView }}</td>\r\n          </tr>\r\n          <tr v-if=\"item.suggestOpenTypeName\">\r\n            <td :rowspan=\"rowspan(item)\" v-if=\"!item.suggestSurveyTypeName && !item.notHandleTimeTypeName\">相关情况</td>\r\n            <td>{{ item.suggestOpenTypeName }}</td>\r\n            <td>{{ item.suggestOpenTypeView }}</td>\r\n          </tr>\r\n          <tr v-if=\"item.isMakeMineJobName\">\r\n            <td :rowspan=\"rowspan(item)\"\r\n              v-if=\"!item.suggestSurveyTypeName && !item.notHandleTimeTypeName && !item.suggestOpenTypeName\">相关情况</td>\r\n            <td>{{ item.isMakeMineJobName }}</td>\r\n            <td>{{ item.isMakeMineJobView }}</td>\r\n          </tr>\r\n          <tr v-if=\"item.isHopeEnhanceTalkName\">\r\n            <td :rowspan=\"rowspan(item)\"\r\n              v-if=\"!item.suggestSurveyTypeName && !item.notHandleTimeTypeName && !item.suggestOpenTypeName && !item.isMakeMineJobName\">\r\n              相关情况</td>\r\n            <td>{{ item.isHopeEnhanceTalkName }}</td>\r\n            <td>{{ item.isHopeEnhanceTalkView }}</td>\r\n          </tr>\r\n          <tr v-if=\"item.isNeedPaperAnswerName\">\r\n            <td\r\n              v-if=\"!item.suggestSurveyTypeName && !item.notHandleTimeTypeName && !item.suggestOpenTypeName && !item.isMakeMineJobName && !item.isHopeEnhanceTalkName\">\r\n              相关情况</td>\r\n            <td>{{ item.isNeedPaperAnswerName }}</td>\r\n            <td>{{ item.isNeedPaperAnswerView }}</td>\r\n          </tr>\r\n          <tr>\r\n            <td>希望送交的承办单位（仅供参考）</td>\r\n            <td colspan=\"2\">{{ item.hopeHandleOfficeName }}</td>\r\n          </tr>\r\n        </tbody>\r\n      </table>\r\n      <div class=\"suggestPrintContent\" v-html=\"item.content\"></div>\r\n      <div style=\"page-break-after:always\"></div>\r\n      <div class=\"mainModulePageTableName\">联名人信息表</div>\r\n      <table>\r\n        <tbody>\r\n          <tr>\r\n            <td>姓名</td>\r\n            <td>界别</td>\r\n            <td>委员证号</td>\r\n            <td>单位职务</td>\r\n            <td>联系方式</td>\r\n            <td>通讯地址</td>\r\n          </tr>\r\n          <tr v-for=\"row in item.joinUsers\" :key=\"row.userId\">\r\n            <td>{{ row.userName }}</td>\r\n            <td>{{ row.sector }}</td>\r\n            <td>{{ row.cardNumber }}</td>\r\n            <td>{{ row.position }}</td>\r\n            <td>{{ row.mobile }}</td>\r\n            <td>{{ row.callAddress }}</td>\r\n          </tr>\r\n        </tbody>\r\n      </table>\r\n      <div class=\"mainModulePageTableName\">提案联系人</div>\r\n      <table>\r\n        <tbody>\r\n          <tr>\r\n            <td colspan=\"2\">姓名</td>\r\n            <td colspan=\"2\">联系电话</td>\r\n            <td colspan=\"3\">通讯地址</td>\r\n          </tr>\r\n          <tr v-for=\"row in item.contacters\" :key=\"row.id\">\r\n            <td colspan=\"2\">{{ row.contacterName }}</td>\r\n            <td colspan=\"2\">{{ row.contacterMobile }}</td>\r\n            <td colspan=\"3\">{{ row.contacterAddress }}</td>\r\n          </tr>\r\n        </tbody>\r\n      </table>\r\n      <div class=\"mainModulePageTableName\">审查和办理信息表</div>\r\n      <table>\r\n        <tbody>\r\n          <tr>\r\n            <td>本提案目前状态</td>\r\n            <td colspan=\"2\">{{ item.currentStatus }}</td>\r\n          </tr>\r\n          <tr>\r\n            <td>审查情况</td>\r\n            <td colspan=\"2\">{{ item.verifyStatus }}</td>\r\n          </tr>\r\n          <tr v-if=\"item.mainHandleOffice\">\r\n            <td rowspan=\"2\">办理单位</td>\r\n            <td>主办</td>\r\n            <td>{{ item.mainHandleOffice }}</td>\r\n          </tr>\r\n          <!-- <tr v-if=\"item.mainHandleOffice\">\r\n            <td>协办</td>\r\n            <td>{{ item.assistHandleOffice }}</td>\r\n          </tr> -->\r\n          <tr v-if=\"item.publishHandleOffice\">\r\n            <td>办理单位</td>\r\n            <td>分办</td>\r\n            <td>{{ item.publishHandleOffice }}</td>\r\n          </tr>\r\n        </tbody>\r\n      </table>\r\n      <div style=\"page-break-after:always\"></div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'suggestPrint' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, onMounted, nextTick } from 'vue'\r\nimport { filterTableData } from '@/assets/js/suggestExportWord'\r\nimport { Print } from 'common/js/print'\r\nconst props = defineProps({ params: { type: Object, default: () => ({}) } })\r\nconst emit = defineEmits(['callback'])\r\nconst printData = ref([])\r\nconst printRef = ref()\r\nconst handlePrint = () => {\r\n  Print.init(printRef.value)\r\n}\r\nonMounted(() => { suggestionWord() })\r\nconst rowspan = (item) => {\r\n  let rowspannum = 0\r\n  if (item.suggestSurveyTypeName) { rowspannum += 1 }\r\n  if (item.notHandleTimeTypeName) { rowspannum += 1 }\r\n  if (item.suggestOpenTypeName) { rowspannum += 1 }\r\n  if (item.isMakeMineJobName) { rowspannum += 1 }\r\n  if (item.isHopeEnhanceTalkName) { rowspannum += 1 }\r\n  if (item.isNeedPaperAnswerName) { rowspannum += 1 }\r\n  return rowspannum\r\n}\r\nconst suggestionWord = async () => {\r\n  const { data } = await api.suggestionWord(props.params)\r\n  if (data.length) {\r\n    printData.value = []\r\n    for (let index = 0; index < data.length; index++) {\r\n      printData.value.push(filterTableData(data[index]))\r\n    }\r\n    nextTick(() => {\r\n      handlePrint()\r\n      emit('callback')\r\n    })\r\n  }\r\n}\r\ndefineExpose({ print: handlePrint })\r\n</script>\r\n<style lang=\"scss\">\r\n.suggestPrint {\r\n  width: 100%;\r\n\r\n  .suggestPrintBody {\r\n    width: 100%;\r\n\r\n    .suggestPrintType {\r\n      font-size: 16pt;\r\n      font-weight: bold;\r\n      padding-bottom: 40pt;\r\n      display: flex;\r\n      justify-content: space-between;\r\n\r\n      span {\r\n        color: red;\r\n      }\r\n    }\r\n\r\n    .suggestPrintName {\r\n      color: red;\r\n      font-size: 24pt;\r\n      line-height: 1.2;\r\n      font-weight: bold;\r\n      text-align: center;\r\n      padding-bottom: 60pt;\r\n    }\r\n\r\n    .suggestPrintItem {\r\n      width: 100%;\r\n      display: flex;\r\n      font-size: 16pt;\r\n      line-height: 1.5;\r\n      padding-bottom: 8pt;\r\n\r\n      span {\r\n        display: inline-block;\r\n      }\r\n\r\n      .suggestPrintItemTitle {\r\n        width: 92px;\r\n        color: red;\r\n        font-weight: bold;\r\n        text-align: justify;\r\n        text-align-last: justify;\r\n      }\r\n\r\n      .suggestPrintItemColon {\r\n        width: 16px;\r\n        color: red;\r\n      }\r\n\r\n      .suggestPrintItemContent {\r\n        width: calc(100% - 108px);\r\n        border-bottom: 1px solid #262626;\r\n      }\r\n    }\r\n\r\n    .suggestPrintItemInfo {\r\n      padding-bottom: 54pt;\r\n    }\r\n\r\n    .suggestPrintItemJoin {\r\n      padding-bottom: 32pt;\r\n    }\r\n\r\n    .suggestPrintItemTwo {\r\n      width: 100%;\r\n      font-size: 16pt;\r\n      line-height: 1.5;\r\n      min-height: calc(16pt * 1.5);\r\n      border-bottom: 1px solid #262626;\r\n      margin-bottom: 8pt;\r\n    }\r\n\r\n    .suggestPrintItemFlex {\r\n      width: 100%;\r\n      display: flex;\r\n      justify-content: space-between;\r\n\r\n      .suggestPrintItem {\r\n        width: 46%;\r\n      }\r\n    }\r\n\r\n    .suggestPrintItemTime {\r\n      color: red;\r\n      text-align: center;\r\n      font-size: 14pt;\r\n      line-height: 1.5;\r\n      padding-top: 80pt;\r\n      font-family: \"Times New Roman\";\r\n    }\r\n\r\n    .mainModulePageTableName {\r\n      font-size: 14pt;\r\n      line-height: 1.5;\r\n      text-align: center;\r\n      margin-bottom: 7pt;\r\n    }\r\n\r\n    table {\r\n      width: 100%;\r\n      table-layout: fixed;\r\n      word-break: break-all;\r\n      border-collapse: collapse;\r\n      margin-bottom: 32pt;\r\n\r\n      tr {\r\n        page-break-inside: avoid;\r\n\r\n        td {\r\n          text-align: center;\r\n          line-height: 1.5;\r\n          padding: 8px;\r\n          font-size: 12pt;\r\n          border: 1px solid #000;\r\n          font-family: \"宋体\";\r\n          display: table-cell;\r\n          vertical-align: middle;\r\n        }\r\n      }\r\n    }\r\n\r\n    .suggestPrintContent {\r\n      overflow: hidden;\r\n      line-height: var(--zy-line-height);\r\n\r\n      img,\r\n      video {\r\n        max-width: 100%;\r\n        height: auto !important;\r\n      }\r\n\r\n      table {\r\n        max-width: 100%;\r\n        border-collapse: collapse;\r\n        border-spacing: 0;\r\n\r\n        tr {\r\n          page-break-inside: avoid;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC,cAAc;EAACC,GAAG,EAAC;;;EAErBD,KAAK,EAAC;AAAkB;;EAKxBA,KAAK,EAAC;AAAkB;;EAGrBA,KAAK,EAAC;AAAyB;;EAElCA,KAAK,EAAC;AAAsB;;EAC1BA,KAAK,EAAC;AAAkB;;EAGrBA,KAAK,EAAC;AAAyB;;EAElCA,KAAK,EAAC;AAAkB;;EAGrBA,KAAK,EAAC;AAAyB;;EAGpCA,KAAK,EAAC;AAAkB;;EAGrBA,KAAK,EAAC;AAAyB;;EAElCA,KAAK,EAAC;AAAkB;;EAGrBA,KAAK,EAAC;AAAyB;;EAElCA,KAAK,EAAC;AAA2C;;EAC/CA,KAAK,EAAC;AAAkB;;EAGrBA,KAAK,EAAC;AAAyB;;EAElCA,KAAK,EAAC;AAAkB;;EAGrBA,KAAK,EAAC;AAAyB;;EAGpCA,KAAK,EAAC;AAAuC;;EAG1CA,KAAK,EAAC;AAAyB;;EAElCA,KAAK,EAAC;AAAkB;;EAGrBA,KAAK,EAAC;AAAyB;;EAGlCA,KAAK,EAAC;AAAsB;;EA1DvCE,GAAA;AAAA;kBAAA;;EAAAA,GAAA;AAAA;kBAAA;;EAAAA,GAAA;AAAA;kBAAA;;EAAAA,GAAA;AAAA;kBAAA;;EAAAA,GAAA;AAAA;kBAAA;;EAAAA,GAAA;AAAA;;EAAAA,GAAA;AAAA;;EAoGgBC,OAAO,EAAC;AAAG;kBApG3B;;EAwIgBA,OAAO,EAAC;AAAG;;EACXA,OAAO,EAAC;AAAG;;EACXA,OAAO,EAAC;AAAG;;EASXA,OAAO,EAAC;AAAG;;EAIXA,OAAO,EAAC;AAAG;;EAvJ3BD,GAAA;AAAA;;EAAAA,GAAA;AAAA;;uBACEE,mBAAA,CA0KM,OA1KNC,UA0KM,I,kBAzKJD,mBAAA,CAwKME,SAAA,QA1KVC,WAAA,CAEiDC,MAAA,CAAAC,SAAS,EAF1D,UAEyCC,IAAI;yBAAzCN,mBAAA,CAwKM;MAxKDJ,KAAK,EAAC,kBAAkB;MAA4BE,GAAG,EAAEQ,IAAI,CAACC;QACjEC,mBAAA,CAGM,OAHNC,UAGM,GAFJD,mBAAA,CAAkD,c,0BAA7CA,mBAAA,CAAgB,cAAV,KAAG,sBAJtBE,gBAAA,CAAAC,gBAAA,CAIgCL,IAAI,CAACM,YAAY,iB,GACzCJ,mBAAA,CAAkD,c,0BAA7CA,mBAAA,CAAgB,cAAV,KAAG,sBALtBE,gBAAA,CAAAC,gBAAA,CAKgCL,IAAI,CAACO,YAAY,iB,KAE3CL,mBAAA,CAAgF;MAA3EZ,KAAK,EAAC,kBAAkB;MAAEkB,OAAK,EAAEV,MAAA,CAAAW;wBAAgBT,IAAI,CAACU,YAAY,kBACvER,mBAAA,CAIM,OAJNS,UAIM,G,0BAHJT,mBAAA,CAA8C;MAAxCZ,KAAK,EAAC;IAAuB,GAAC,KAAG,sB,0BACvCY,mBAAA,CAA4C;MAAtCZ,KAAK,EAAC;IAAuB,GAAC,GAAC,sBACrCY,mBAAA,CAAuE,QAAvEU,UAAuE,EAAAP,gBAAA,CAA9BL,IAAI,CAACa,eAAe,iB,GAE/DX,mBAAA,CAWM,OAXNY,UAWM,GAVJZ,mBAAA,CAIM,OAJNa,UAIM,G,0BAHJb,mBAAA,CAA+C;MAAzCZ,KAAK,EAAC;IAAuB,GAAC,MAAI,sB,0BACxCY,mBAAA,CAA4C;MAAtCZ,KAAK,EAAC;IAAuB,GAAC,GAAC,sBACrCY,mBAAA,CAAqE,QAArEc,UAAqE,EAAAX,gBAAA,CAA5BL,IAAI,CAACiB,aAAa,iB,GAE7Df,mBAAA,CAIM,OAJNgB,UAIM,G,0BAHJhB,mBAAA,CAA6C;MAAvCZ,KAAK,EAAC;IAAuB,GAAC,IAAE,sB,0BACtCY,mBAAA,CAA4C;MAAtCZ,KAAK,EAAC;IAAuB,GAAC,GAAC,sBACrCY,mBAAA,CAAsE,QAAtEiB,UAAsE,EAAAd,gBAAA,CAA7BL,IAAI,CAACoB,cAAc,iB,KAGhElB,mBAAA,CAIM,OAJNmB,WAIM,G,0BAHJnB,mBAAA,CAA+C;MAAzCZ,KAAK,EAAC;IAAuB,GAAC,MAAI,sB,0BACxCY,mBAAA,CAA4C;MAAtCZ,KAAK,EAAC;IAAuB,GAAC,GAAC,sBACrCY,mBAAA,CAAgE,QAAhEoB,WAAgE,EAAAjB,gBAAA,CAAvBL,IAAI,CAACuB,QAAQ,iB,GAExDrB,mBAAA,CAIM,OAJNsB,WAIM,G,4BAHJtB,mBAAA,CAA+C;MAAzCZ,KAAK,EAAC;IAAuB,GAAC,MAAI,sB,4BACxCY,mBAAA,CAA4C;MAAtCZ,KAAK,EAAC;IAAuB,GAAC,GAAC,sBACrCY,mBAAA,CAAmE,QAAnEuB,WAAmE,EAAApB,gBAAA,CAA1BL,IAAI,CAAC0B,WAAW,iB,GAE3DxB,mBAAA,CAWM,OAXNyB,WAWM,GAVJzB,mBAAA,CAIM,OAJN0B,WAIM,G,4BAHJ1B,mBAAA,CAA+C;MAAzCZ,KAAK,EAAC;IAAuB,GAAC,MAAI,sB,4BACxCY,mBAAA,CAA4C;MAAtCZ,KAAK,EAAC;IAAuB,GAAC,GAAC,sBACrCY,mBAAA,CAAgE,QAAhE2B,WAAgE,EAAAxB,gBAAA,CAAvBL,IAAI,CAAC8B,QAAQ,iB,GAExD5B,mBAAA,CAIM,OAJN6B,WAIM,G,4BAHJ7B,mBAAA,CAA+C;MAAzCZ,KAAK,EAAC;IAAuB,GAAC,MAAI,sB,4BACxCY,mBAAA,CAA4C;MAAtCZ,KAAK,EAAC;IAAuB,GAAC,GAAC,sBACrCY,mBAAA,CAA8D,QAA9D8B,WAA8D,EAAA3B,gBAAA,CAArBL,IAAI,CAACiC,MAAM,iB,KAGxD/B,mBAAA,CAIM,OAJNgC,WAIM,G,4BAHJhC,mBAAA,CAA8C;MAAxCZ,KAAK,EAAC;IAAuB,GAAC,KAAG,sB,4BACvCY,mBAAA,CAA4C;MAAtCZ,KAAK,EAAC;IAAuB,GAAC,GAAC,sBACrCY,mBAAA,CAAgE,QAAhEiC,WAAgE,EAAA9B,gBAAA,CAAvBL,IAAI,CAACoC,QAAQ,iB,GAExDlC,mBAAA,CAIM,OAJNmC,WAIM,G,4BAHJnC,mBAAA,CAA+C;MAAzCZ,KAAK,EAAC;IAAuB,GAAC,MAAI,sB,4BACxCY,mBAAA,CAA4C;MAAtCZ,KAAK,EAAC;IAAuB,GAAC,GAAC,sBACrCY,mBAAA,CAAgE,QAAhEoC,WAAgE,EAAAjC,gBAAA,CAAvBL,IAAI,CAACuC,QAAQ,iB,sBAExD7C,mBAAA,CAAwGE,SAAA,QAzD9GC,WAAA,CAyD+DG,IAAI,CAACwC,WAAW,EAzD/E,UAyD+CC,IAAI,EAAEC,KAAK;2BAApDhD,mBAAA,CAAwG;QAAnGJ,KAAK,EAAC,qBAAqB;QAA4CE,GAAG,EAAEkD;0BAAUD,IAAI;oCAC/FvC,mBAAA,CAAkE,OAAlEyC,WAAkE,EAAhC,OAAK,GAAAtC,gBAAA,CAAGL,IAAI,CAAC4C,UAAU,kB,4BACzD1C,mBAAA,CAA2C;MAAtC2C,KAA+B,EAA/B;QAAA;MAAA;IAA+B,6B,4BACpC3C,mBAAA,CAAiD;MAA5CZ,KAAK,EAAC;IAAyB,GAAC,QAAM,sBAC3CY,mBAAA,CA0CQ,gBAzCNA,mBAAA,CAwCQ,gBAvCIF,IAAI,CAAC8C,qBAAqB,I,cAApCpD,mBAAA,CAIK,MAnEfqD,WAAA,GAgEY7C,mBAAA,CAAsC;MAAjC8C,OAAO,EAAElD,MAAA,CAAAkD,OAAO,CAAChD,IAAI;OAAG,MAAI,iBAhE7CiD,WAAA,GAiEY/C,mBAAA,CAAyC,YAAAG,gBAAA,CAAlCL,IAAI,CAAC8C,qBAAqB,kBACjC5C,mBAAA,CAAyC,YAAAG,gBAAA,CAAlCL,IAAI,CAACkD,qBAAqB,iB,KAlE7CC,mBAAA,gBAoEoBnD,IAAI,CAACoD,qBAAqB,I,cAApC1D,mBAAA,CAIK,MAxEf2D,WAAA,G,CAqEgDrD,IAAI,CAAC8C,qBAAqB,I,cAA9DpD,mBAAA,CAAyE;MArErFF,GAAA;MAqEiBwD,OAAO,EAAElD,MAAA,CAAAkD,OAAO,CAAChD,IAAI;OAAsC,MAAI,iBArEhFsD,WAAA,KAAAH,mBAAA,gBAsEYjD,mBAAA,CAAyC,YAAAG,gBAAA,CAAlCL,IAAI,CAACoD,qBAAqB,kBACjClD,mBAAA,CAAyC,YAAAG,gBAAA,CAAlCL,IAAI,CAACuD,qBAAqB,iB,KAvE7CJ,mBAAA,gBAyEoBnD,IAAI,CAACwD,mBAAmB,I,cAAlC9D,mBAAA,CAIK,MA7Ef+D,WAAA,G,CA0EgDzD,IAAI,CAAC8C,qBAAqB,KAAK9C,IAAI,CAACoD,qBAAqB,I,cAA7F1D,mBAAA,CAAwG;MA1EpHF,GAAA;MA0EiBwD,OAAO,EAAElD,MAAA,CAAAkD,OAAO,CAAChD,IAAI;OAAqE,MAAI,iBA1E/G0D,WAAA,KAAAP,mBAAA,gBA2EYjD,mBAAA,CAAuC,YAAAG,gBAAA,CAAhCL,IAAI,CAACwD,mBAAmB,kBAC/BtD,mBAAA,CAAuC,YAAAG,gBAAA,CAAhCL,IAAI,CAAC2D,mBAAmB,iB,KA5E3CR,mBAAA,gBA8EoBnD,IAAI,CAAC4D,iBAAiB,I,cAAhClE,mBAAA,CAKK,MAnFfmE,WAAA,G,CAgFqB7D,IAAI,CAAC8C,qBAAqB,KAAK9C,IAAI,CAACoD,qBAAqB,KAAKpD,IAAI,CAACwD,mBAAmB,I,cAD/F9D,mBAAA,CAC0G;MAhFtHF,GAAA;MA+EiBwD,OAAO,EAAElD,MAAA,CAAAkD,OAAO,CAAChD,IAAI;OACuE,MAAI,iBAhFjH8D,WAAA,KAAAX,mBAAA,gBAiFYjD,mBAAA,CAAqC,YAAAG,gBAAA,CAA9BL,IAAI,CAAC4D,iBAAiB,kBAC7B1D,mBAAA,CAAqC,YAAAG,gBAAA,CAA9BL,IAAI,CAAC+D,iBAAiB,iB,KAlFzCZ,mBAAA,gBAoFoBnD,IAAI,CAACgE,qBAAqB,I,cAApCtE,mBAAA,CAMK,MA1FfuE,WAAA,G,CAsFqBjE,IAAI,CAAC8C,qBAAqB,KAAK9C,IAAI,CAACoD,qBAAqB,KAAKpD,IAAI,CAACwD,mBAAmB,KAAKxD,IAAI,CAAC4D,iBAAiB,I,cAD1HlE,mBAAA,CAEW;MAvFvBF,GAAA;MAqFiBwD,OAAO,EAAElD,MAAA,CAAAkD,OAAO,CAAChD,IAAI;OACkG,OACtH,iBAvFlBkE,WAAA,KAAAf,mBAAA,gBAwFYjD,mBAAA,CAAyC,YAAAG,gBAAA,CAAlCL,IAAI,CAACgE,qBAAqB,kBACjC9D,mBAAA,CAAyC,YAAAG,gBAAA,CAAlCL,IAAI,CAACmE,qBAAqB,iB,KAzF7ChB,mBAAA,gBA2FoBnD,IAAI,CAACoE,qBAAqB,I,cAApC1E,mBAAA,CAMK,MAjGf2E,WAAA,G,CA6FqBrE,IAAI,CAAC8C,qBAAqB,KAAK9C,IAAI,CAACoD,qBAAqB,KAAKpD,IAAI,CAACwD,mBAAmB,KAAKxD,IAAI,CAAC4D,iBAAiB,KAAK5D,IAAI,CAACgE,qBAAqB,I,cADzJtE,mBAAA,CAEW,MA9FvB4E,WAAA,EA6FuK,OACrJ,KA9FlBnB,mBAAA,gBA+FYjD,mBAAA,CAAyC,YAAAG,gBAAA,CAAlCL,IAAI,CAACoE,qBAAqB,kBACjClE,mBAAA,CAAyC,YAAAG,gBAAA,CAAlCL,IAAI,CAACuE,qBAAqB,iB,KAhG7CpB,mBAAA,gBAkGUjD,mBAAA,CAGK,a,4BAFHA,mBAAA,CAAwB,YAApB,iBAAe,sBACnBA,mBAAA,CAAoD,MAApDsE,WAAoD,EAAAnE,gBAAA,CAAjCL,IAAI,CAACyE,oBAAoB,iB,OAIlDvE,mBAAA,CAA6D;MAAxDZ,KAAK,EAAC,qBAAqB;MAACoF,SAAqB,EAAb1E,IAAI,CAAC2E;4BAxGpDC,WAAA,G,4BAyGM1E,mBAAA,CAA2C;MAAtC2C,KAA+B,EAA/B;QAAA;MAAA;IAA+B,6B,4BACpC3C,mBAAA,CAAiD;MAA5CZ,KAAK,EAAC;IAAyB,GAAC,QAAM,sBAC3CY,mBAAA,CAmBQ,gBAlBNA,mBAAA,CAiBQ,gB,4BAhBNA,mBAAA,CAOK,aANHA,mBAAA,CAAW,YAAP,IAAE,GACNA,mBAAA,CAAW,YAAP,IAAE,GACNA,mBAAA,CAAa,YAAT,MAAI,GACRA,mBAAA,CAAa,YAAT,MAAI,GACRA,mBAAA,CAAa,YAAT,MAAI,GACRA,mBAAA,CAAa,YAAT,MAAI,E,yCAEVR,mBAAA,CAOKE,SAAA,QA5HfC,WAAA,CAqH4BG,IAAI,CAAC6E,SAAS,EArH1C,UAqHqBC,GAAG;2BAAdpF,mBAAA,CAOK;QAP8BF,GAAG,EAAEsF,GAAG,CAACC;UAC1C7E,mBAAA,CAA2B,YAAAG,gBAAA,CAApByE,GAAG,CAACE,QAAQ,kBACnB9E,mBAAA,CAAyB,YAAAG,gBAAA,CAAlByE,GAAG,CAACG,MAAM,kBACjB/E,mBAAA,CAA6B,YAAAG,gBAAA,CAAtByE,GAAG,CAACI,UAAU,kBACrBhF,mBAAA,CAA2B,YAAAG,gBAAA,CAApByE,GAAG,CAACvD,QAAQ,kBACnBrB,mBAAA,CAAyB,YAAAG,gBAAA,CAAlByE,GAAG,CAAC7C,MAAM,kBACjB/B,mBAAA,CAA8B,YAAAG,gBAAA,CAAvByE,GAAG,CAACpD,WAAW,iB;oEAI5BxB,mBAAA,CAAgD;MAA3CZ,KAAK,EAAC;IAAyB,GAAC,OAAK,sBAC1CY,mBAAA,CAaQ,gBAZNA,mBAAA,CAWQ,gB,4BAVNA,mBAAA,CAIK,aAHHA,mBAAA,CAAuB;MAAnBT,OAAO,EAAC;IAAG,GAAC,IAAE,GAClBS,mBAAA,CAAyB;MAArBT,OAAO,EAAC;IAAG,GAAC,MAAI,GACpBS,mBAAA,CAAyB;MAArBT,OAAO,EAAC;IAAG,GAAC,MAAI,E,yCAEtBC,mBAAA,CAIKE,SAAA,QA3IfC,WAAA,CAuI4BG,IAAI,CAACmF,UAAU,EAvI3C,UAuIqBL,GAAG;2BAAdpF,mBAAA,CAIK;QAJ+BF,GAAG,EAAEsF,GAAG,CAAC7E;UAC3CC,mBAAA,CAA4C,MAA5CkF,WAA4C,EAAA/E,gBAAA,CAAzByE,GAAG,CAACO,aAAa,kBACpCnF,mBAAA,CAA8C,MAA9CoF,WAA8C,EAAAjF,gBAAA,CAA3ByE,GAAG,CAACS,eAAe,kBACtCrF,mBAAA,CAA+C,MAA/CsF,WAA+C,EAAAnF,gBAAA,CAA5ByE,GAAG,CAACW,gBAAgB,iB;oEAI7CvF,mBAAA,CAAmD;MAA9CZ,KAAK,EAAC;IAAyB,GAAC,UAAQ,sBAC7CY,mBAAA,CAyBQ,gBAxBNA,mBAAA,CAuBQ,gBAtBNA,mBAAA,CAGK,a,4BAFHA,mBAAA,CAAgB,YAAZ,SAAO,sBACXA,mBAAA,CAA6C,MAA7CwF,WAA6C,EAAArF,gBAAA,CAA1BL,IAAI,CAAC2F,aAAa,iB,GAEvCzF,mBAAA,CAGK,a,4BAFHA,mBAAA,CAAa,YAAT,MAAI,sBACRA,mBAAA,CAA4C,MAA5C0F,WAA4C,EAAAvF,gBAAA,CAAzBL,IAAI,CAAC6F,YAAY,iB,GAE5B7F,IAAI,CAAC8F,gBAAgB,I,cAA/BpG,mBAAA,CAIK,MA7JfqG,WAAA,G,4BA0JY7F,mBAAA,CAAyB;MAArB8C,OAAO,EAAC;IAAG,GAAC,MAAI,sB,4BACpB9C,mBAAA,CAAW,YAAP,IAAE,sBACNA,mBAAA,CAAoC,YAAAG,gBAAA,CAA7BL,IAAI,CAAC8F,gBAAgB,iB,KA5JxC3C,mBAAA,gBA8JUA,mBAAA,6IAGS,EACCnD,IAAI,CAACgG,mBAAmB,I,cAAlCtG,mBAAA,CAIK,MAtKfuG,WAAA,G,4BAmKY/F,mBAAA,CAAa,YAAT,MAAI,sB,4BACRA,mBAAA,CAAW,YAAP,IAAE,sBACNA,mBAAA,CAAuC,YAAAG,gBAAA,CAAhCL,IAAI,CAACgG,mBAAmB,iB,KArK3C7C,mBAAA,e,iCAyKMjD,mBAAA,CAA2C;MAAtC2C,KAA+B,EAA/B;QAAA;MAAA;IAA+B,4B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}