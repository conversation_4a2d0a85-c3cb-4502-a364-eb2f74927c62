{"ast": null, "code": "import { renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createBlock as _createBlock, createVNode as _createVNode, normalizeClass as _normalizeClass, createElementVNode as _createElementVNode, createCommentVNode as _createCommentVNode, vShow as _vShow, withDirectives as _withDirectives, withKeys as _withKeys } from \"vue\";\nvar _hoisted_1 = {\n  class: \"SuggestStatistics\"\n};\nvar _hoisted_2 = {\n  class: \"SuggestStatisticsChart\"\n};\nvar _hoisted_3 = {\n  class: \"SuggestStatisticsChartType\"\n};\nvar _hoisted_4 = {\n  key: 0,\n  class: \"SuggestStatisticsChartEmpty\"\n};\nvar _hoisted_5 = {\n  class: \"globalTable\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_xyl_statistics_label_item = _resolveComponent(\"xyl-statistics-label-item\");\n  var _component_xyl_statistics_label = _resolveComponent(\"xyl-statistics-label\");\n  var _component_el_empty = _resolveComponent(\"el-empty\");\n  var _component_el_input = _resolveComponent(\"el-input\");\n  var _component_el_option = _resolveComponent(\"el-option\");\n  var _component_el_select = _resolveComponent(\"el-select\");\n  var _component_xyl_search_button = _resolveComponent(\"xyl-search-button\");\n  var _component_el_table = _resolveComponent(\"el-table\");\n  var _component_el_scrollbar = _resolveComponent(\"el-scrollbar\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_xyl_statistics_label, {\n    modelValue: $setup.labelId,\n    \"onUpdate:modelValue\": _cache[0] || (_cache[0] = function ($event) {\n      return $setup.labelId = $event;\n    }),\n    onChange: $setup.handleLabel\n  }, {\n    default: _withCtx(function () {\n      return [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.labelList, function (item) {\n        return _openBlock(), _createBlock(_component_xyl_statistics_label_item, {\n          key: item.key,\n          value: item.key\n        }, {\n          default: _withCtx(function () {\n            return [_createTextVNode(_toDisplayString(item.name), 1 /* TEXT */)];\n          }),\n          _: 2 /* DYNAMIC */\n        }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"value\"]);\n      }), 128 /* KEYED_FRAGMENT */))];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_el_scrollbar, {\n    class: \"SuggestStatisticsBody\"\n  }, {\n    default: _withCtx(function () {\n      return [_withDirectives(_createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"div\", {\n        class: _normalizeClass([\"SuggestStatisticsChartTypeItem\", {\n          'is-active': $setup.isActive === '1'\n        }]),\n        onClick: _cache[1] || (_cache[1] = function ($event) {\n          return $setup.handleChartType('1');\n        })\n      }, \" 默认 \", 2 /* CLASS */), _createElementVNode(\"div\", {\n        class: _normalizeClass([\"SuggestStatisticsChartTypeItem\", {\n          'is-active': $setup.isActive === '2'\n        }]),\n        onClick: _cache[2] || (_cache[2] = function ($event) {\n          return $setup.handleChartType('2');\n        })\n      }, \" 饼状 \", 2 /* CLASS */), _createElementVNode(\"div\", {\n        class: _normalizeClass([\"SuggestStatisticsChartTypeItem\", {\n          'is-active': $setup.isActive === '3'\n        }]),\n        onClick: _cache[3] || (_cache[3] = function ($event) {\n          return $setup.handleChartType('3');\n        })\n      }, \" 柱状 \", 2 /* CLASS */)]), !$setup.chartData.length ? (_openBlock(), _createElementBlock(\"div\", _hoisted_4, [_createVNode(_component_el_empty, {\n        \"image-size\": 100\n      })])) : _createCommentVNode(\"v-if\", true), _withDirectives(_createVNode($setup[\"PieChart\"], {\n        data: $setup.chartData\n      }, null, 8 /* PROPS */, [\"data\"]), [[_vShow, ($setup.isActive === '2' || $setup.isActive === '1' && $setup.chartTypeObj[$setup.labelId] === '1') && $setup.chartData.length]]), _withDirectives(_createVNode($setup[\"ColumnChart\"], {\n        data: $setup.chartData\n      }, null, 8 /* PROPS */, [\"data\"]), [[_vShow, ($setup.isActive === '3' || $setup.isActive === '1' && $setup.chartTypeObj[$setup.labelId] === '2') && $setup.chartData.length]])], 512 /* NEED_PATCH */), [[_vShow, $setup.chartTypeObj[$setup.labelId] !== 'no']]), _createElementVNode(\"div\", {\n        class: _normalizeClass([\"globalLayout\", {\n          'is-active': $setup.chartTypeObj[$setup.labelId] === 'no'\n        }])\n      }, [_createVNode(_component_xyl_search_button, {\n        onQueryClick: $setup.handleQuery,\n        onResetClick: $setup.handleReset,\n        onHandleButton: $setup.handleButton,\n        buttonList: $setup.buttonList\n      }, {\n        search: _withCtx(function () {\n          return [_createVNode(_component_el_input, {\n            modelValue: $setup.keyword,\n            \"onUpdate:modelValue\": _cache[4] || (_cache[4] = function ($event) {\n              return $setup.keyword = $event;\n            }),\n            placeholder: \"请输入关键词\",\n            onKeyup: _withKeys($setup.handleQuery, [\"enter\"]),\n            clearable: \"\"\n          }, null, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_el_select, {\n            modelValue: $setup.termYearId,\n            \"onUpdate:modelValue\": _cache[5] || (_cache[5] = function ($event) {\n              return $setup.termYearId = $event;\n            }),\n            placeholder: \"请选择届次\",\n            multiple: \"\",\n            \"collapse-tags\": \"\",\n            clearable: \"\"\n          }, {\n            default: _withCtx(function () {\n              return [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.termYearData, function (item) {\n                return _openBlock(), _createBlock(_component_el_option, {\n                  key: item.key,\n                  label: item.name,\n                  value: item.key\n                }, null, 8 /* PROPS */, [\"label\", \"value\"]);\n              }), 128 /* KEYED_FRAGMENT */))];\n            }),\n            _: 1 /* STABLE */\n          }, 8 /* PROPS */, [\"modelValue\"]), $setup.labelId == 'office_answer_type' ? (_openBlock(), _createBlock(_component_el_select, {\n            key: 0,\n            modelValue: $setup.zhuxieban,\n            \"onUpdate:modelValue\": _cache[6] || (_cache[6] = function ($event) {\n              return $setup.zhuxieban = $event;\n            }),\n            placeholder: \"请选择办理方式\",\n            clearable: \"\"\n          }, {\n            default: _withCtx(function () {\n              return [_createVNode(_component_el_option, {\n                label: \"主办\",\n                value: \"1\"\n              }), _createCommentVNode(\" <el-option label=\\\"协办\\\" value=\\\"2\\\" /> \")];\n            }),\n            _: 1 /* STABLE */\n          }, 8 /* PROPS */, [\"modelValue\"])) : _createCommentVNode(\"v-if\", true), _createVNode(_component_el_select, {\n            modelValue: $setup.suggestMeetingType,\n            \"onUpdate:modelValue\": _cache[7] || (_cache[7] = function ($event) {\n              return $setup.suggestMeetingType = $event;\n            }),\n            placeholder: \"请选择提案类型\",\n            clearable: \"\"\n          }, {\n            default: _withCtx(function () {\n              return [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.suggestionMeetingTypeData, function (item) {\n                return _openBlock(), _createBlock(_component_el_option, {\n                  key: item.id,\n                  label: item.name,\n                  value: item.id\n                }, null, 8 /* PROPS */, [\"label\", \"value\"]);\n              }), 128 /* KEYED_FRAGMENT */))];\n            }),\n            _: 1 /* STABLE */\n          }, 8 /* PROPS */, [\"modelValue\"]), ['delegation', 'npc_member', 'big_theme', 'small_theme', 'about_some'].includes($setup.labelId) ? (_openBlock(), _createBlock(_component_el_select, {\n            key: 1,\n            modelValue: $setup.suggestStutas,\n            \"onUpdate:modelValue\": _cache[8] || (_cache[8] = function ($event) {\n              return $setup.suggestStutas = $event;\n            }),\n            placeholder: \"请选择提案状态\",\n            \"collapse-tags\": \"\",\n            multiple: \"\",\n            clearable: \"\"\n          }, {\n            default: _withCtx(function () {\n              return [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.suggestStutasTypeData, function (item) {\n                return _openBlock(), _createBlock(_component_el_option, {\n                  key: item.id,\n                  label: item.label,\n                  value: item.id\n                }, null, 8 /* PROPS */, [\"label\", \"value\"]);\n              }), 128 /* KEYED_FRAGMENT */))];\n            }),\n            _: 1 /* STABLE */\n          }, 8 /* PROPS */, [\"modelValue\"])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" <el-checkbox v-model=\\\"isReceive\\\"\\r\\n                         @change=\\\"handleQuery\\\">立案提案</el-checkbox> \")];\n        }),\n        _: 1 /* STABLE */\n      }), _createElementVNode(\"div\", _hoisted_5, [_createVNode(_component_el_table, {\n        border: \"\",\n        ref: \"tableRef\",\n        data: $setup.tableData,\n        \"summary-method\": $setup.getSummaries,\n        \"show-summary\": \"\",\n        onSortChange: $setup.handleSortChange\n      }, {\n        default: _withCtx(function () {\n          return [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.tableHead, function (item) {\n            return _openBlock(), _createBlock($setup[\"TableTree\"], {\n              tableColumn: item,\n              onChange: $setup.handleChange,\n              onDetails: $setup.handleDetails,\n              key: item.headCode + $setup.labelId\n            }, null, 8 /* PROPS */, [\"tableColumn\"]);\n          }), 128 /* KEYED_FRAGMENT */))];\n        }),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"data\"])])], 2 /* CLASS */)];\n    }),\n    _: 1 /* STABLE */\n  })]);\n}", "map": {"version": 3, "names": ["class", "key", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_xyl_statistics_label", "modelValue", "$setup", "labelId", "_cache", "$event", "onChange", "handleLabel", "default", "_withCtx", "_Fragment", "_renderList", "labelList", "item", "_createBlock", "_component_xyl_statistics_label_item", "value", "_createTextVNode", "_toDisplayString", "name", "_", "_component_el_scrollbar", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_normalizeClass", "isActive", "onClick", "handleChartType", "chartData", "length", "_hoisted_4", "_component_el_empty", "_createCommentVNode", "data", "chartTypeObj", "_component_xyl_search_button", "onQueryClick", "handleQuery", "onResetClick", "handleReset", "onHandleButton", "handleButton", "buttonList", "search", "_component_el_input", "keyword", "placeholder", "onKeyup", "_with<PERSON><PERSON><PERSON>", "clearable", "_component_el_select", "termYearId", "multiple", "termYearData", "_component_el_option", "label", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suggestMeetingType", "suggestionMeetingTypeData", "id", "includes", "suggestStutas", "suggestStutasTypeData", "_hoisted_5", "_component_el_table", "border", "ref", "tableData", "getSummaries", "onSortChange", "handleSortChange", "tableHead", "tableColumn", "handleChange", "onDetails", "handleDetails", "headCode"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestStatistics\\SuggestStatistics.vue"], "sourcesContent": ["<template>\r\n  <div class=\"SuggestStatistics\">\r\n    <xyl-statistics-label v-model=\"labelId\" @change=\"handleLabel\">\r\n      <xyl-statistics-label-item v-for=\"item in labelList\" :key=\"item.key\" :value=\"item.key\">\r\n        {{ item.name }}\r\n      </xyl-statistics-label-item>\r\n    </xyl-statistics-label>\r\n    <el-scrollbar class=\"SuggestStatisticsBody\">\r\n      <div class=\"SuggestStatisticsChart\" v-show=\"chartTypeObj[labelId] !== 'no'\">\r\n        <div class=\"SuggestStatisticsChartType\">\r\n          <div class=\"SuggestStatisticsChartTypeItem\" :class=\"{ 'is-active': isActive === '1' }\"\r\n            @click=\"handleChartType('1')\">\r\n            默认\r\n          </div>\r\n          <div class=\"SuggestStatisticsChartTypeItem\" :class=\"{ 'is-active': isActive === '2' }\"\r\n            @click=\"handleChartType('2')\">\r\n            饼状\r\n          </div>\r\n          <div class=\"SuggestStatisticsChartTypeItem\" :class=\"{ 'is-active': isActive === '3' }\"\r\n            @click=\"handleChartType('3')\">\r\n            柱状\r\n          </div>\r\n        </div>\r\n        <div v-if=\"!chartData.length\" class=\"SuggestStatisticsChartEmpty\">\r\n          <el-empty :image-size=\"100\" />\r\n        </div>\r\n        <PieChart v-show=\"(isActive === '2' || (isActive === '1' && chartTypeObj[labelId] === '1')) && chartData.length\"\r\n          :data=\"chartData\"></PieChart>\r\n        <ColumnChart\r\n          v-show=\"(isActive === '3' || (isActive === '1' && chartTypeObj[labelId] === '2')) && chartData.length\"\r\n          :data=\"chartData\"></ColumnChart>\r\n      </div>\r\n      <div class=\"globalLayout\" :class=\"{ 'is-active': chartTypeObj[labelId] === 'no' }\">\r\n        <xyl-search-button @queryClick=\"handleQuery\" @resetClick=\"handleReset\" @handleButton=\"handleButton\"\r\n          :buttonList=\"buttonList\">\r\n          <template #search>\r\n            <el-input v-model=\"keyword\" placeholder=\"请输入关键词\" @keyup.enter=\"handleQuery\" clearable />\r\n            <el-select v-model=\"termYearId\" placeholder=\"请选择届次\" multiple collapse-tags clearable>\r\n              <el-option v-for=\"item in termYearData\" :key=\"item.key\" :label=\"item.name\" :value=\"item.key\" />\r\n            </el-select>\r\n            <el-select v-model=\"zhuxieban\" v-if=\"labelId == 'office_answer_type'\" placeholder=\"请选择办理方式\" clearable>\r\n              <el-option label=\"主办\" value=\"1\" />\r\n              <!-- <el-option label=\"协办\" value=\"2\" /> -->\r\n            </el-select>\r\n            <el-select v-model=\"suggestMeetingType\" placeholder=\"请选择提案类型\" clearable>\r\n              <el-option v-for=\"item in suggestionMeetingTypeData\" :key=\"item.id\" :label=\"item.name\" :value=\"item.id\" />\r\n            </el-select>\r\n            <el-select v-model=\"suggestStutas\"\r\n              v-if=\"['delegation', 'npc_member', 'big_theme', 'small_theme', 'about_some'].includes(labelId)\"\r\n              placeholder=\"请选择提案状态\" collapse-tags multiple clearable>\r\n              <el-option v-for=\"item in suggestStutasTypeData\" :key=\"item.id\" :label=\"item.label\" :value=\"item.id\" />\r\n            </el-select>\r\n\r\n            <!-- <el-checkbox v-model=\"isReceive\"\r\n                         @change=\"handleQuery\">立案提案</el-checkbox> -->\r\n          </template>\r\n        </xyl-search-button>\r\n        <div class=\"globalTable\">\r\n          <el-table border ref=\"tableRef\" :data=\"tableData\" :summary-method=\"getSummaries\" show-summary\r\n            @sort-change=\"handleSortChange\">\r\n            <TableTree v-for=\"item in tableHead\" :tableColumn=\"item\" @change=\"handleChange\" @details=\"handleDetails\"\r\n              :key=\"item.headCode + labelId\"></TableTree>\r\n          </el-table>\r\n        </div>\r\n      </div>\r\n    </el-scrollbar>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'SuggestStatistics' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, onActivated, nextTick } from 'vue'\r\nimport { GlobalExportExcel } from 'common/Excel/GlobalExportExcel'\r\nimport { qiankunMicro } from 'common/config/MicroGlobal'\r\nimport { ElMessage } from 'element-plus'\r\nimport TableTree from './TableTree.vue'\r\nimport PieChart from './PieChart.vue'\r\nimport ColumnChart from './ColumnChart.vue'\r\nimport { useRoute } from 'vue-router'\r\nconst route = useRoute()\r\nconst { exportExcelTree } = GlobalExportExcel()\r\nconst buttonList = [{ id: 'export', name: '导出Excel', type: 'primary', has: '' }]\r\nconst labelId = ref('')\r\nconst labelObj = ref({})\r\nconst labelList = ref([])\r\nconst chartTypeObj = ref({})\r\nconst isActive = ref('1')\r\nconst keyword = ref('')\r\nconst termYearId = ref([])\r\nconst termYearData = ref([])\r\nconst isReceive = ref(false)\r\nconst tableRef = ref()\r\nconst tableHead = ref([])\r\nconst tableData = ref([])\r\nconst chartData = ref([])\r\nconst sortData = ref({})\r\nconst zhuxieban = ref('')\r\nconst suggestStutas = ref([])\r\nconst suggestStutasTypeData = ref([])\r\nconst suggestMeetingType = ref('all')\r\nconst suggestionMeetingTypeData = ref([])\r\n\r\nonActivated(() => {\r\n  termYearSelect()\r\n  getsuggestStutas()\r\n  suggestionMeetingType()\r\n})\r\nconst getsuggestStutas = async () => {\r\n  const { data } = await api.globalJson('/customColumn/selector/id_prop_proposal_current_node_id')\r\n  suggestStutasTypeData.value = data\r\n}\r\nconst suggestionMeetingType = async () => {\r\n  const { data } = await api.suggestionMeetingType()\r\n  suggestionMeetingTypeData.value = data.map((v) => ({ ...v, id: v.id ? v.id : 'all' }))\r\n}\r\n\r\n// 获取当前届次\r\nconst termYearCurrent = async () => {\r\n  const { data } = await api.termYearCurrent({ termYearType: 'cppcc_member' })\r\n  if (!termYearId.value.length) {\r\n    termYearId.value = [data.id]\r\n  }\r\n  dictionaryData()\r\n}\r\nconst termYearSelect = async () => {\r\n  const { data } = await api.termYearSelect({ termYearType: 'cppcc_member' })\r\n  termYearData.value = data\r\n  termYearCurrent()\r\n}\r\nconst dictionaryData = async () => {\r\n  const { data } = await api.dictionaryData({ dictCodes: ['proposal_statistics'] })\r\n  chartTypeObj.value = {}\r\n  labelObj.value = {}\r\n  labelList.value = []\r\n  if (data?.proposal_statistics?.length) {\r\n    for (let index = 0; index < data?.proposal_statistics?.length || 0; index++) {\r\n      const item = data.proposal_statistics[index]\r\n      const itemArr = item.id.split('=')\r\n      labelObj.value[itemArr[0]] = item.name\r\n      chartTypeObj.value[itemArr[0]] = itemArr[1] || 'no'\r\n      labelList.value.push({ key: itemArr[0], type: itemArr[1] || 'no', name: item.name })\r\n      if (!labelId.value && !index) {\r\n        labelId.value = itemArr[0] || ''\r\n      }\r\n    }\r\n    if (route.query.labelId) {\r\n      labelId.value = route.query.labelId\r\n    }\r\n    suggestionStatistics()\r\n  } else {\r\n    labelId.value = ''\r\n  }\r\n}\r\nconst handleLabel = () => {\r\n  tableHead.value = []\r\n  tableData.value = []\r\n  tableRef.value?.doLayout()\r\n  tableRef.value?.clearSort()\r\n  nextTick(() => {\r\n    suggestionStatistics()\r\n  })\r\n}\r\nconst getSummaries = (param) => {\r\n  const { columns, data } = param\r\n  const sums = []\r\n  const showarr = [\r\n    'handle_office',\r\n    'office_handle_status',\r\n    'office_answer_type',\r\n    'office_handle_info',\r\n    'group_contact_num',\r\n    'group_answer_open'\r\n  ]\r\n  columns.forEach((column, index) => {\r\n    if (index === 0) {\r\n      sums[index] = '合计 ' + (showarr.includes(labelId.value) ? `(${data.length})` : '')\r\n      return\r\n    }\r\n    const values = data.map((item) => {\r\n      const keys = column.property.split('.')\r\n      return keys.length === 1 ? Number(item[column.property]) : item[keys[0]]?.amount\r\n    })\r\n    if (!values.every((value) => Number.isNaN(value))) {\r\n      sums[index] = values.reduce((prev, curr) => {\r\n        const value = Number(curr)\r\n        if (!Number.isNaN(value)) {\r\n          return prev + curr\r\n        } else {\r\n          return prev\r\n        }\r\n      }, 0)\r\n    } else {\r\n      sums[index] = ''\r\n    }\r\n  })\r\n  return sums\r\n}\r\nconst handleQuery = () => {\r\n  suggestionStatistics()\r\n}\r\nconst handleReset = () => {\r\n  keyword.value = ''\r\n  termYearId.value = []\r\n  suggestStutas.value = []\r\n  suggestMeetingType.value = 'all'\r\n  termYearCurrent()\r\n}\r\nconst handleSortChange = ({ prop, order }) => {\r\n  sortData.value = { prop, order }\r\n}\r\nconst handleButton = (isType) => {\r\n  switch (isType) {\r\n    case 'export':\r\n      if (!tableData.value.length) return ElMessage({ type: 'info', message: '暂无统计数据可导出！' })\r\n      handleExportExcel()\r\n      break\r\n    default:\r\n      break\r\n  }\r\n}\r\nconst handleExportExcel = () => {\r\n  let excelData = []\r\n  const newTableData = []\r\n  for (let index = 0; index < tableData.value.length; index++) {\r\n    const item = tableData.value[index]\r\n    newTableData.push(filterTableData(item))\r\n  }\r\n  const key = sortData.value.prop.split('.')[0]\r\n  if (sortData.value.order === 'descending') {\r\n    excelData = newTableData.sort((a, b) => b[key] - a[key])\r\n  } else if (sortData.value.order === 'ascending') {\r\n    excelData = newTableData.sort((a, b) => a[key] - b[key])\r\n  } else {\r\n    excelData = newTableData\r\n  }\r\n  exportExcelTree(filtertableHead(tableHead.value), excelData, `${labelObj.value[labelId.value]}统计`)\r\n}\r\nconst suggestionStatistics = async () => {\r\n  console.log(termYearId.value)\r\n  const { data } = await api.suggestionStatistics({\r\n    countView: labelId.value,\r\n    // keyword: keyword.value,\r\n    termYearId: termYearId.value.join(','),\r\n    isReceive: isReceive.value ? 1 : 0,\r\n    zhuxieban: zhuxieban.value,\r\n    query: {\r\n      suggestMeetingType: suggestMeetingType.value === 'all' ? null : suggestMeetingType.value\r\n    },\r\n    currentNodeIds: suggestStutas.value\r\n  })\r\n  const filterKey = data.tableHeader[0]?.headCode || ''\r\n  const getTableData = keyword.value\r\n    ? data.tableData?.filter((v) => (filterKey ? v[filterKey].includes(keyword.value) : true))\r\n    : data.tableData\r\n  tableHead.value = data.tableHeader\r\n  tableData.value = getTableData\r\n  nextTick(() => {\r\n    tableRef.value?.doLayout()\r\n    nextTick(() => {\r\n      for (let index = 0; index < data.tableHeader.length; index++) {\r\n        const item = data.tableHeader[index]\r\n        if (index === 1) {\r\n          tableRef.value?.sort(`${item.headCode}.amount`, 'descending')\r\n        }\r\n      }\r\n    })\r\n  })\r\n  chartData.value = []\r\n  if (chartTypeObj.value[labelId.value] !== 'no') {\r\n    for (let index = 0; index < getTableData.length; index++) {\r\n      const item = getTableData[index]\r\n      var chartObj = {}\r\n      for (let i = 0; i < data.tableHeader.length; i++) {\r\n        const row = data.tableHeader[i]\r\n        if (row.showType) {\r\n          if (row.showType === 'link') {\r\n            chartObj.value = item[row.headCode]?.amount\r\n          }\r\n        } else {\r\n          chartObj.name = item[row.headCode]\r\n        }\r\n      }\r\n      chartData.value.push(chartObj)\r\n    }\r\n  }\r\n}\r\nconst whetherDataType = (obj) => {\r\n  var toString = Object.prototype.toString\r\n  var map = {\r\n    '[object Boolean]': 'boolean',\r\n    '[object Number]': 'number',\r\n    '[object String]': 'string',\r\n    '[object Function]': 'function',\r\n    '[object Array]': 'array',\r\n    '[object Date]': 'date',\r\n    '[object RegExp]': 'regExp',\r\n    '[object Undefined]': 'undefined',\r\n    '[object Null]': 'null',\r\n    '[object Object]': 'object'\r\n  }\r\n  return map[toString.call(obj)]\r\n}\r\nconst filterTableData = (row) => {\r\n  var rowObj = {}\r\n  for (let key in row) {\r\n    const type = whetherDataType(row[key])\r\n    if (type === 'array') {\r\n      rowObj[key] = row[key].map((v) => v.serialNumber).join('、')\r\n    } else if (type === 'object') {\r\n      rowObj[key] = row[key]?.amount\r\n    } else {\r\n      rowObj[key] = row[key]\r\n    }\r\n  }\r\n  return rowObj\r\n}\r\nconst filtertableHead = (data) =>\r\n  data.map((v) => ({ id: v.headCode, key: v.headCode, label: v.headName, children: filtertableHead(v.children || []) }))\r\nconst handleChartType = (type) => {\r\n  isActive.value = type\r\n}\r\nconst handleChange = (item, column) => {\r\n  qiankunMicro.setGlobalState({\r\n    openRoute: {\r\n      name: '提案统计列表',\r\n      path: '/proposal/SuggestStatisticsList',\r\n      query: { ids: JSON.stringify(item[column.headCode]?.proposalIds) }\r\n    }\r\n  })\r\n}\r\nconst handleDetails = (item, row) => {\r\n  qiankunMicro.setGlobalState({\r\n    openRoute: { name: '提案详情', path: '/proposal/SuggestDetail', query: { id: row.proposalId } }\r\n  })\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.SuggestStatistics {\r\n  width: 100%;\r\n  height: 100%;\r\n  background: var(--zy-el-color-info-light-9);\r\n  display: flex;\r\n  justify-content: space-between;\r\n\r\n  .SuggestStatisticsBody {\r\n    width: calc(100% - 190px);\r\n    height: 100%;\r\n\r\n    .zy-el-scrollbar__view {\r\n      height: 100%;\r\n    }\r\n\r\n    .SuggestStatisticsChart {\r\n      width: 100%;\r\n      height: 300px;\r\n      margin-bottom: 10px;\r\n      background-color: #fff;\r\n\r\n      .SuggestStatisticsChartType {\r\n        display: flex;\r\n        align-items: center;\r\n        width: 100%;\r\n        height: var(--zy-height);\r\n\r\n        .SuggestStatisticsChartTypeItem {\r\n          line-height: var(--zy-line-height);\r\n          font-size: var(--zy-text-font-size);\r\n          color: var(--zy-el-text-color-regular);\r\n          padding-left: 20px;\r\n          margin-left: 20px;\r\n          position: relative;\r\n          cursor: pointer;\r\n\r\n          &::after {\r\n            content: '';\r\n            position: absolute;\r\n            width: 20px;\r\n            height: 20px;\r\n            top: 50%;\r\n            left: 0;\r\n            transform: translateY(-50%);\r\n          }\r\n\r\n          &:nth-child(1) {\r\n            &::after {\r\n              content: '';\r\n              position: absolute;\r\n              width: 20px;\r\n              height: 20px;\r\n              top: 50%;\r\n              left: 0;\r\n              transform: translateY(-50%);\r\n              background: url('../../assets/img/default_chart.png') no-repeat;\r\n              background-color: var(--zy-el-text-color-regular);\r\n              background-size: 100% 100%;\r\n            }\r\n          }\r\n\r\n          &:nth-child(2) {\r\n            &::after {\r\n              content: '';\r\n              position: absolute;\r\n              width: 20px;\r\n              height: 20px;\r\n              top: 50%;\r\n              left: 0;\r\n              transform: translateY(-50%);\r\n              background: url('../../assets/img/pie_chart.png') no-repeat;\r\n              background-color: var(--zy-el-text-color-regular);\r\n              background-size: 100% 100%;\r\n            }\r\n          }\r\n\r\n          &:nth-child(3) {\r\n            &::after {\r\n              content: '';\r\n              position: absolute;\r\n              width: 20px;\r\n              height: 20px;\r\n              top: 50%;\r\n              left: 0;\r\n              transform: translateY(-50%);\r\n              background: url('../../assets/img/column_chart.png') no-repeat;\r\n              background-color: var(--zy-el-text-color-regular);\r\n              background-size: 100% 100%;\r\n            }\r\n          }\r\n\r\n          &.is-active {\r\n            color: var(--zy-el-color-primary);\r\n\r\n            &::after {\r\n              background-color: var(--zy-el-color-primary);\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      .SuggestStatisticsChartEmpty {\r\n        width: 100%;\r\n        height: calc(100% - var(--zy-height));\r\n\r\n        .zy-el-empty {\r\n          height: 100%;\r\n        }\r\n      }\r\n\r\n      .PieChart {\r\n        height: calc(100% - var(--zy-height));\r\n      }\r\n\r\n      .ColumnChart {\r\n        height: calc(100% - var(--zy-height));\r\n      }\r\n    }\r\n\r\n    .globalLayout {\r\n      width: 100%;\r\n      height: calc(100% - 310px);\r\n      min-height: 500px;\r\n      padding: 0 20px;\r\n      background-color: #fff;\r\n\r\n      .xyl-search-button {\r\n        .xyl-button {\r\n          width: calc(100% - 880px);\r\n        }\r\n\r\n        .xyl-search {\r\n          width: 880px;\r\n\r\n          .zy-el-select {\r\n            margin-left: 20px;\r\n          }\r\n\r\n          .zy-el-checkbox {\r\n            margin-left: 20px;\r\n          }\r\n        }\r\n      }\r\n\r\n      .globalTable {\r\n        width: 100%;\r\n        height: calc(100% - (var(--zy-height) + (var(--zy-distance-four) * 2)));\r\n\r\n        .zy-el-table__cell {\r\n          border-right: var(--zy-el-table-border) !important;\r\n          border-bottom: var(--zy-el-table-border) !important;\r\n        }\r\n\r\n        .SuggestStatisticsLinkList {\r\n          margin-right: var(--zy-distance-two);\r\n        }\r\n      }\r\n    }\r\n\r\n    .globalLayout.is-active {\r\n      height: 100%;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAmB;;EAOrBA,KAAK,EAAC;AAAwB;;EAC5BA,KAAK,EAAC;AAA4B;;EAT/CC,GAAA;EAuBsCD,KAAK,EAAC;;;EAkC/BA,KAAK,EAAC;AAAa;;;;;;;;;;;uBAxD9BE,mBAAA,CAiEM,OAjENC,UAiEM,GAhEJC,YAAA,CAIuBC,+BAAA;IAN3BC,UAAA,EAEmCC,MAAA,CAAAC,OAAO;IAF1C,uBAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAEmCH,MAAA,CAAAC,OAAO,GAAAE,MAAA;IAAA;IAAGC,QAAM,EAAEJ,MAAA,CAAAK;;IAFrDC,OAAA,EAAAC,QAAA,CAGiC;MAAA,OAAyB,E,kBAApDZ,mBAAA,CAE4Ba,SAAA,QALlCC,WAAA,CAGgDT,MAAA,CAAAU,SAAS,EAHzD,UAGwCC,IAAI;6BAAtCC,YAAA,CAE4BC,oCAAA;UAF0BnB,GAAG,EAAEiB,IAAI,CAACjB,GAAG;UAAGoB,KAAK,EAAEH,IAAI,CAACjB;;UAHxFY,OAAA,EAAAC,QAAA,CAIQ;YAAA,OAAe,CAJvBQ,gBAAA,CAAAC,gBAAA,CAIWL,IAAI,CAACM,IAAI,iB;;UAJpBC,CAAA;;;;IAAAA,CAAA;qCAOIrB,YAAA,CA0DesB,uBAAA;IA1DD1B,KAAK,EAAC;EAAuB;IAP/Ca,OAAA,EAAAC,QAAA,CAQM;MAAA,OAuBM,C,gBAvBNa,mBAAA,CAuBM,OAvBNC,UAuBM,GAtBJD,mBAAA,CAaM,OAbNE,UAaM,GAZJF,mBAAA,CAGM;QAHD3B,KAAK,EAVpB8B,eAAA,EAUqB,gCAAgC;UAAA,aAAwBvB,MAAA,CAAAwB,QAAQ;QAAA;QACxEC,OAAK,EAAAvB,MAAA,QAAAA,MAAA,gBAAAC,MAAA;UAAA,OAAEH,MAAA,CAAA0B,eAAe;QAAA;SAAO,MAEhC,kBACAN,mBAAA,CAGM;QAHD3B,KAAK,EAdpB8B,eAAA,EAcqB,gCAAgC;UAAA,aAAwBvB,MAAA,CAAAwB,QAAQ;QAAA;QACxEC,OAAK,EAAAvB,MAAA,QAAAA,MAAA,gBAAAC,MAAA;UAAA,OAAEH,MAAA,CAAA0B,eAAe;QAAA;SAAO,MAEhC,kBACAN,mBAAA,CAGM;QAHD3B,KAAK,EAlBpB8B,eAAA,EAkBqB,gCAAgC;UAAA,aAAwBvB,MAAA,CAAAwB,QAAQ;QAAA;QACxEC,OAAK,EAAAvB,MAAA,QAAAA,MAAA,gBAAAC,MAAA;UAAA,OAAEH,MAAA,CAAA0B,eAAe;QAAA;SAAO,MAEhC,iB,IAEU1B,MAAA,CAAA2B,SAAS,CAACC,MAAM,I,cAA5BjC,mBAAA,CAEM,OAFNkC,UAEM,GADJhC,YAAA,CAA8BiC,mBAAA;QAAnB,YAAU,EAAE;MAAG,G,KAxBpCC,mBAAA,gB,gBA0BQlC,YAAA,CAC+BG,MAAA;QAA5BgC,IAAI,EAAEhC,MAAA,CAAA2B;MAAS,oC,UADC3B,MAAA,CAAAwB,QAAQ,YAAaxB,MAAA,CAAAwB,QAAQ,YAAYxB,MAAA,CAAAiC,YAAY,CAACjC,MAAA,CAAAC,OAAO,cAAeD,MAAA,CAAA2B,SAAS,CAACC,MAAM,E,mBAE/G/B,YAAA,CAEkCG,MAAA;QAA/BgC,IAAI,EAAEhC,MAAA,CAAA2B;MAAS,oC,UADP3B,MAAA,CAAAwB,QAAQ,YAAaxB,MAAA,CAAAwB,QAAQ,YAAYxB,MAAA,CAAAiC,YAAY,CAACjC,MAAA,CAAAC,OAAO,cAAeD,MAAA,CAAA2B,SAAS,CAACC,MAAM,E,qCArB7D5B,MAAA,CAAAiC,YAAY,CAACjC,MAAA,CAAAC,OAAO,Y,GAwBhEmB,mBAAA,CAgCM;QAhCD3B,KAAK,EAhChB8B,eAAA,EAgCiB,cAAc;UAAA,aAAwBvB,MAAA,CAAAiC,YAAY,CAACjC,MAAA,CAAAC,OAAO;QAAA;UACnEJ,YAAA,CAuBoBqC,4BAAA;QAvBAC,YAAU,EAAEnC,MAAA,CAAAoC,WAAW;QAAGC,YAAU,EAAErC,MAAA,CAAAsC,WAAW;QAAGC,cAAY,EAAEvC,MAAA,CAAAwC,YAAY;QAC/FC,UAAU,EAAEzC,MAAA,CAAAyC;;QACFC,MAAM,EAAAnC,QAAA,CACf;UAAA,OAAwF,CAAxFV,YAAA,CAAwF8C,mBAAA;YApCpG5C,UAAA,EAoC+BC,MAAA,CAAA4C,OAAO;YApCtC,uBAAA1C,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OAoC+BH,MAAA,CAAA4C,OAAO,GAAAzC,MAAA;YAAA;YAAE0C,WAAW,EAAC,QAAQ;YAAEC,OAAK,EApCnEC,SAAA,CAoC2E/C,MAAA,CAAAoC,WAAW;YAAEY,SAAS,EAAT;mDAC5EnD,YAAA,CAEYoD,oBAAA;YAvCxBlD,UAAA,EAqCgCC,MAAA,CAAAkD,UAAU;YArC1C,uBAAAhD,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OAqCgCH,MAAA,CAAAkD,UAAU,GAAA/C,MAAA;YAAA;YAAE0C,WAAW,EAAC,OAAO;YAACM,QAAQ,EAAR,EAAQ;YAAC,eAAa,EAAb,EAAa;YAACH,SAAS,EAAT;;YArCvF1C,OAAA,EAAAC,QAAA,CAsCyB;cAAA,OAA4B,E,kBAAvCZ,mBAAA,CAA+Fa,SAAA,QAtC7GC,WAAA,CAsCwCT,MAAA,CAAAoD,YAAY,EAtCpD,UAsCgCzC,IAAI;qCAAtBC,YAAA,CAA+FyC,oBAAA;kBAAtD3D,GAAG,EAAEiB,IAAI,CAACjB,GAAG;kBAAG4D,KAAK,EAAE3C,IAAI,CAACM,IAAI;kBAAGH,KAAK,EAAEH,IAAI,CAACjB;;;;YAtCtGwB,CAAA;6CAwCiDlB,MAAA,CAAAC,OAAO,4B,cAA5CW,YAAA,CAGYqC,oBAAA;YA3CxBvD,GAAA;YAAAK,UAAA,EAwCgCC,MAAA,CAAAuD,SAAS;YAxCzC,uBAAArD,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OAwCgCH,MAAA,CAAAuD,SAAS,GAAApD,MAAA;YAAA;YAAyC0C,WAAW,EAAC,SAAS;YAACG,SAAS,EAAT;;YAxCxG1C,OAAA,EAAAC,QAAA,CAyCc;cAAA,OAAkC,CAAlCV,YAAA,CAAkCwD,oBAAA;gBAAvBC,KAAK,EAAC,IAAI;gBAACxC,KAAK,EAAC;kBAC5BiB,mBAAA,4CAA2C,C;;YA1CzDb,CAAA;+CAAAa,mBAAA,gBA4CYlC,YAAA,CAEYoD,oBAAA;YA9CxBlD,UAAA,EA4CgCC,MAAA,CAAAwD,kBAAkB;YA5ClD,uBAAAtD,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OA4CgCH,MAAA,CAAAwD,kBAAkB,GAAArD,MAAA;YAAA;YAAE0C,WAAW,EAAC,SAAS;YAACG,SAAS,EAAT;;YA5C1E1C,OAAA,EAAAC,QAAA,CA6CyB;cAAA,OAAyC,E,kBAApDZ,mBAAA,CAA0Ga,SAAA,QA7CxHC,WAAA,CA6CwCT,MAAA,CAAAyD,yBAAyB,EA7CjE,UA6CgC9C,IAAI;qCAAtBC,YAAA,CAA0GyC,oBAAA;kBAApD3D,GAAG,EAAEiB,IAAI,CAAC+C,EAAE;kBAAGJ,KAAK,EAAE3C,IAAI,CAACM,IAAI;kBAAGH,KAAK,EAAEH,IAAI,CAAC+C;;;;YA7ClHxC,CAAA;oHAgD2FyC,QAAQ,CAAC3D,MAAA,CAAAC,OAAO,K,cAD/FW,YAAA,CAIYqC,oBAAA;YAnDxBvD,GAAA;YAAAK,UAAA,EA+CgCC,MAAA,CAAA4D,aAAa;YA/C7C,uBAAA1D,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OA+CgCH,MAAA,CAAA4D,aAAa,GAAAzD,MAAA;YAAA;YAE/B0C,WAAW,EAAC,SAAS;YAAC,eAAa,EAAb,EAAa;YAACM,QAAQ,EAAR,EAAQ;YAACH,SAAS,EAAT;;YAjD3D1C,OAAA,EAAAC,QAAA,CAkDyB;cAAA,OAAqC,E,kBAAhDZ,mBAAA,CAAuGa,SAAA,QAlDrHC,WAAA,CAkDwCT,MAAA,CAAA6D,qBAAqB,EAlD7D,UAkDgClD,IAAI;qCAAtBC,YAAA,CAAuGyC,oBAAA;kBAArD3D,GAAG,EAAEiB,IAAI,CAAC+C,EAAE;kBAAGJ,KAAK,EAAE3C,IAAI,CAAC2C,KAAK;kBAAGxC,KAAK,EAAEH,IAAI,CAAC+C;;;;YAlD/GxC,CAAA;+CAAAa,mBAAA,gBAqDYA,mBAAA,+GACyD,C;;QAtDrEb,CAAA;UAyDQE,mBAAA,CAMM,OANN0C,UAMM,GALJjE,YAAA,CAIWkE,mBAAA;QAJDC,MAAM,EAAN,EAAM;QAACC,GAAG,EAAC,UAAU;QAAEjC,IAAI,EAAEhC,MAAA,CAAAkE,SAAS;QAAG,gBAAc,EAAElE,MAAA,CAAAmE,YAAY;QAAE,cAAY,EAAZ,EAAY;QAC1FC,YAAW,EAAEpE,MAAA,CAAAqE;;QA3D1B/D,OAAA,EAAAC,QAAA,CA4DuB;UAAA,OAAyB,E,kBAApCZ,mBAAA,CAC6Ca,SAAA,QA7DzDC,WAAA,CA4DsCT,MAAA,CAAAsE,SAAS,EA5D/C,UA4D8B3D,IAAI;iCAAtBC,YAAA,CAC6CZ,MAAA;cADPuE,WAAW,EAAE5D,IAAI;cAAGP,QAAM,EAAEJ,MAAA,CAAAwE,YAAY;cAAGC,SAAO,EAAEzE,MAAA,CAAA0E,aAAa;cACpGhF,GAAG,EAAEiB,IAAI,CAACgE,QAAQ,GAAG3E,MAAA,CAAAC;;;;QA7DpCiB,CAAA;;;IAAAA,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}