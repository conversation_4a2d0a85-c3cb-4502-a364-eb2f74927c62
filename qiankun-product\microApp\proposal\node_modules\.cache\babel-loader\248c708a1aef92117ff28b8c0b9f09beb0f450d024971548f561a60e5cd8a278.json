{"ast": null, "code": "import { createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, openBlock as _openBlock, createBlock as _createBlock, createCommentVNode as _createCommentVNode, renderList as _renderList, Fragment as _Fragment, createElementBlock as _createElementBlock, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, withCtx as _withCtx, createVNode as _createVNode } from \"vue\";\nvar _hoisted_1 = {\n  class: \"ApplyForAdjustResult\"\n};\nvar _hoisted_2 = {\n  key: 0\n};\nvar _hoisted_3 = {\n  key: 1\n};\nvar _hoisted_4 = {\n  key: 0\n};\nvar _hoisted_5 = {\n  key: 1\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_empty = _resolveComponent(\"el-empty\");\n  var _component_global_info_item = _resolveComponent(\"global-info-item\");\n  var _component_global_info_line = _resolveComponent(\"global-info-line\");\n  var _component_global_info = _resolveComponent(\"global-info\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_cache[0] || (_cache[0] = _createElementVNode(\"div\", {\n    class: \"ApplyForAdjustResultName\"\n  }, \"提案单位申请调整结果\", -1 /* HOISTED */)), !$setup.dataList.length ? (_openBlock(), _createBlock(_component_el_empty, {\n    key: 0,\n    description: \"暂无记录\"\n  })) : _createCommentVNode(\"v-if\", true), (_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.dataList, function (item) {\n    return _openBlock(), _createBlock(_component_global_info, {\n      key: item.id\n    }, {\n      default: _withCtx(function () {\n        return [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(item.applyOfficeInfo, function (row) {\n          return _openBlock(), _createElementBlock(\"div\", {\n            key: row.applyAdjustId\n          }, [_createVNode(_component_global_info_line, null, {\n            default: _withCtx(function () {\n              return [_createVNode(_component_global_info_item, {\n                label: \"申请单位\"\n              }, {\n                default: _withCtx(function () {\n                  return [_createTextVNode(_toDisplayString(row.officeName), 1 /* TEXT */)];\n                }),\n                _: 2 /* DYNAMIC */\n              }, 1024 /* DYNAMIC_SLOTS */), _createVNode(_component_global_info_item, {\n                label: \"申请时间\"\n              }, {\n                default: _withCtx(function () {\n                  return [_createTextVNode(_toDisplayString($setup.format(row.applyTime)), 1 /* TEXT */)];\n                }),\n                _: 2 /* DYNAMIC */\n              }, 1024 /* DYNAMIC_SLOTS */)];\n            }),\n            _: 2 /* DYNAMIC */\n          }, 1024 /* DYNAMIC_SLOTS */), _createVNode(_component_global_info_item, {\n            label: \"申请调整理由\"\n          }, {\n            default: _withCtx(function () {\n              return [_createElementVNode(\"pre\", null, _toDisplayString(row.applyReason), 1 /* TEXT */)];\n            }),\n            _: 2 /* DYNAMIC */\n          }, 1024 /* DYNAMIC_SLOTS */)]);\n        }), 128 /* KEYED_FRAGMENT */)), _createVNode(_component_global_info_item, {\n          label: \"调整时间\"\n        }, {\n          default: _withCtx(function () {\n            return [_createTextVNode(_toDisplayString($setup.format(item.adjustTime)), 1 /* TEXT */)];\n          }),\n          _: 2 /* DYNAMIC */\n        }, 1024 /* DYNAMIC_SLOTS */), _createVNode(_component_global_info_item, {\n          label: \"调整前\"\n        }, {\n          default: _withCtx(function () {\n            return [item.oldMain ? (_openBlock(), _createElementBlock(\"div\", _hoisted_2, \"主办：\" + _toDisplayString(item.oldMain), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" <div v-if=\\\"item.oldAssist\\\">协办：{{ item.oldAssist }}</div> \"), item.oldPublish ? (_openBlock(), _createElementBlock(\"div\", _hoisted_3, \"分办：\" + _toDisplayString(item.oldPublish), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true)];\n          }),\n          _: 2 /* DYNAMIC */\n        }, 1024 /* DYNAMIC_SLOTS */), _createVNode(_component_global_info_item, {\n          label: \"调整后\"\n        }, {\n          default: _withCtx(function () {\n            return [item.newMain ? (_openBlock(), _createElementBlock(\"div\", _hoisted_4, \"主办：\" + _toDisplayString(item.newMain), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" <div v-if=\\\"item.newAssist\\\">协办：{{ item.newAssist }}</div> \"), item.newPublish ? (_openBlock(), _createElementBlock(\"div\", _hoisted_5, \"分办：\" + _toDisplayString(item.newPublish), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true)];\n          }),\n          _: 2 /* DYNAMIC */\n        }, 1024 /* DYNAMIC_SLOTS */)];\n      }),\n      _: 2 /* DYNAMIC */\n    }, 1024 /* DYNAMIC_SLOTS */);\n  }), 128 /* KEYED_FRAGMENT */))]);\n}", "map": {"version": 3, "names": ["class", "key", "_createElementBlock", "_hoisted_1", "_createElementVNode", "$setup", "dataList", "length", "_createBlock", "_component_el_empty", "description", "_createCommentVNode", "_Fragment", "_renderList", "item", "_component_global_info", "id", "default", "_withCtx", "applyOfficeInfo", "row", "applyAdjustId", "_createVNode", "_component_global_info_line", "_component_global_info_item", "label", "_createTextVNode", "_toDisplayString", "officeName", "_", "format", "applyTime", "applyReason", "adjustTime", "<PERSON><PERSON><PERSON>", "_hoisted_2", "oldPublish", "_hoisted_3", "new<PERSON>ain", "_hoisted_4", "newPublish", "_hoisted_5"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\proposal\\src\\views\\SuggestDetail\\ApplyForAdjustResult\\ApplyForAdjustResult.vue"], "sourcesContent": ["<template>\r\n  <div class=\"ApplyForAdjustResult\">\r\n    <div class=\"ApplyForAdjustResultName\">提案单位申请调整结果</div>\r\n    <el-empty v-if=\"!dataList.length\" description=\"暂无记录\" />\r\n    <global-info v-for=\"item in dataList\" :key=\"item.id\">\r\n      <div v-for=\"row in item.applyOfficeInfo\" :key=\"row.applyAdjustId\">\r\n        <global-info-line>\r\n          <global-info-item label=\"申请单位\">{{ row.officeName }}</global-info-item>\r\n          <global-info-item label=\"申请时间\">{{ format(row.applyTime) }}</global-info-item>\r\n        </global-info-line>\r\n        <global-info-item label=\"申请调整理由\">\r\n          <pre>{{ row.applyReason }}</pre>\r\n        </global-info-item>\r\n      </div>\r\n      <global-info-item label=\"调整时间\">{{ format(item.adjustTime) }}</global-info-item>\r\n      <global-info-item label=\"调整前\">\r\n        <div v-if=\"item.oldMain\">主办：{{ item.oldMain }}</div>\r\n        <!-- <div v-if=\"item.oldAssist\">协办：{{ item.oldAssist }}</div> -->\r\n        <div v-if=\"item.oldPublish\">分办：{{ item.oldPublish }}</div>\r\n      </global-info-item>\r\n      <global-info-item label=\"调整后\">\r\n        <div v-if=\"item.newMain\">主办：{{ item.newMain }}</div>\r\n        <!-- <div v-if=\"item.newAssist\">协办：{{ item.newAssist }}</div> -->\r\n        <div v-if=\"item.newPublish\">分办：{{ item.newPublish }}</div>\r\n      </global-info-item>\r\n    </global-info>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'ApplyForAdjustResult' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, onMounted } from 'vue'\r\nimport { format } from 'common/js/time.js'\r\nconst props = defineProps({ id: { type: String, default: '' } })\r\nconst dataList = ref([])\r\nonMounted(() => { handingPortionAdjustRecord() })\r\n\r\nconst handingPortionAdjustRecord = async () => {\r\n  const { data } = await api.handingPortionAdjustRecord({ suggestionId: props.id })\r\n  dataList.value = data.map(v => ({\r\n    id: v.recordId,\r\n    adjustTime: v.adjustTime,\r\n    applyOfficeInfo: v.applyOfficeInfo,\r\n    oldMain: v.beforeOffices.filter(v => v.type === 'main').map(v => v.officeName).join('、'),\r\n    oldAssist: v.beforeOffices.filter(v => v.type === 'assist').map(v => v.officeName).join('、'),\r\n    oldPublish: v.beforeOffices.filter(v => v.type === 'publish').map(v => v.officeName).join('、'),\r\n    newMain: v.afterOffices.filter(v => v.type === 'main').map(v => v.officeName).join('、'),\r\n    newAssist: v.afterOffices.filter(v => v.type === 'assist').map(v => v.officeName).join('、'),\r\n    newPublish: v.afterOffices.filter(v => v.type === 'publish').map(v => v.officeName).join('、')\r\n  }))\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.ApplyForAdjustResult {\r\n  width: 990px;\r\n  padding: 0 var(--zy-distance-one);\r\n  padding-top: var(--zy-distance-one);\r\n\r\n  .ApplyForAdjustResultName {\r\n    font-size: var(--zy-title-font-size);\r\n    font-weight: bold;\r\n    color: var(--zy-el-color-primary);\r\n    border-bottom: 1px solid var(--zy-el-color-primary);\r\n    text-align: center;\r\n    padding: 20px 0;\r\n    margin-bottom: 20px;\r\n  }\r\n\r\n  .global-info {\r\n    padding-bottom: 12px;\r\n\r\n    .global-info-item {\r\n      .global-info-label {\r\n        width: 160px;\r\n      }\r\n\r\n      .global-info-content {\r\n        width: calc(100% - 160px);\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAsB;;EADnCC,GAAA;AAAA;;EAAAA,GAAA;AAAA;;EAAAA,GAAA;AAAA;;EAAAA,GAAA;AAAA;;;;;;uBACEC,mBAAA,CAyBM,OAzBNC,UAyBM,G,0BAxBJC,mBAAA,CAAsD;IAAjDJ,KAAK,EAAC;EAA0B,GAAC,YAAU,sB,CAC/BK,MAAA,CAAAC,QAAQ,CAACC,MAAM,I,cAAhCC,YAAA,CAAuDC,mBAAA;IAH3DR,GAAA;IAGsCS,WAAW,EAAC;QAHlDC,mBAAA,iB,kBAIIT,mBAAA,CAqBcU,SAAA,QAzBlBC,WAAA,CAIgCR,MAAA,CAAAC,QAAQ,EAJxC,UAIwBQ,IAAI;yBAAxBN,YAAA,CAqBcO,sBAAA;MArByBd,GAAG,EAAEa,IAAI,CAACE;;MAJrDC,OAAA,EAAAC,QAAA,CAKW;QAAA,OAAmC,E,kBAAxChB,mBAAA,CAQMU,SAAA,QAbZC,WAAA,CAKyBC,IAAI,CAACK,eAAe,EAL7C,UAKkBC,GAAG;+BAAflB,mBAAA,CAQM;YARoCD,GAAG,EAAEmB,GAAG,CAACC;cACjDC,YAAA,CAGmBC,2BAAA;YAT3BN,OAAA,EAAAC,QAAA,CAOU;cAAA,OAAsE,CAAtEI,YAAA,CAAsEE,2BAAA;gBAApDC,KAAK,EAAC;cAAM;gBAPxCR,OAAA,EAAAC,QAAA,CAOyC;kBAAA,OAAoB,CAP7DQ,gBAAA,CAAAC,gBAAA,CAO4CP,GAAG,CAACQ,UAAU,iB;;gBAP1DC,CAAA;4CAQUP,YAAA,CAA6EE,2BAAA;gBAA3DC,KAAK,EAAC;cAAM;gBARxCR,OAAA,EAAAC,QAAA,CAQyC;kBAAA,OAA2B,CARpEQ,gBAAA,CAAAC,gBAAA,CAQ4CtB,MAAA,CAAAyB,MAAM,CAACV,GAAG,CAACW,SAAS,kB;;gBARhEF,CAAA;;;YAAAA,CAAA;wCAUQP,YAAA,CAEmBE,2BAAA;YAFDC,KAAK,EAAC;UAAQ;YAVxCR,OAAA,EAAAC,QAAA,CAWU;cAAA,OAAgC,CAAhCd,mBAAA,CAAgC,aAAAuB,gBAAA,CAAxBP,GAAG,CAACY,WAAW,iB;;YAXjCH,CAAA;;wCAcMP,YAAA,CAA+EE,2BAAA;UAA7DC,KAAK,EAAC;QAAM;UAdpCR,OAAA,EAAAC,QAAA,CAcqC;YAAA,OAA6B,CAdlEQ,gBAAA,CAAAC,gBAAA,CAcwCtB,MAAA,CAAAyB,MAAM,CAAChB,IAAI,CAACmB,UAAU,kB;;UAd9DJ,CAAA;sCAeMP,YAAA,CAImBE,2BAAA;UAJDC,KAAK,EAAC;QAAK;UAfnCR,OAAA,EAAAC,QAAA,CAgBQ;YAAA,OAAoD,CAAzCJ,IAAI,CAACoB,OAAO,I,cAAvBhC,mBAAA,CAAoD,OAhB5DiC,UAAA,EAgBiC,KAAG,GAAAR,gBAAA,CAAGb,IAAI,CAACoB,OAAO,oBAhBnDvB,mBAAA,gBAiBQA,mBAAA,gEAAiE,EACtDG,IAAI,CAACsB,UAAU,I,cAA1BlC,mBAAA,CAA0D,OAlBlEmC,UAAA,EAkBoC,KAAG,GAAAV,gBAAA,CAAGb,IAAI,CAACsB,UAAU,oBAlBzDzB,mBAAA,e;;UAAAkB,CAAA;sCAoBMP,YAAA,CAImBE,2BAAA;UAJDC,KAAK,EAAC;QAAK;UApBnCR,OAAA,EAAAC,QAAA,CAqBQ;YAAA,OAAoD,CAAzCJ,IAAI,CAACwB,OAAO,I,cAAvBpC,mBAAA,CAAoD,OArB5DqC,UAAA,EAqBiC,KAAG,GAAAZ,gBAAA,CAAGb,IAAI,CAACwB,OAAO,oBArBnD3B,mBAAA,gBAsBQA,mBAAA,gEAAiE,EACtDG,IAAI,CAAC0B,UAAU,I,cAA1BtC,mBAAA,CAA0D,OAvBlEuC,UAAA,EAuBoC,KAAG,GAAAd,gBAAA,CAAGb,IAAI,CAAC0B,UAAU,oBAvBzD7B,mBAAA,e;;UAAAkB,CAAA;;;MAAAA,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}