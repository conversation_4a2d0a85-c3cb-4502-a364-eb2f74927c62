/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    PublicOpinionPrint: typeof import('./src/components/publicOpinionPrint/publicOpinionPrint.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SimilarityQuery: typeof import('./src/components/SimilarityQuery/SimilarityQuery.vue')['default']
  }
}
