/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    ElButton: typeof import('element-plus/es')['ElButton']
    ElLink: typeof import('element-plus/es')['ElLink']
    ElPagination: typeof import('element-plus/es')['ElPagination']
    ElScrollbar: typeof import('element-plus/es')['ElScrollbar']
    ElTable: typeof import('element-plus/es')['ElTable']
    ElTableColumn: typeof import('element-plus/es')['ElTableColumn']
    ElTag: typeof import('element-plus/es')['ElTag']
    PublicOpinionPrint: typeof import('./src/components/publicOpinionPrint/publicOpinionPrint.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SimilarityQuery: typeof import('./src/components/SimilarityQuery/SimilarityQuery.vue')['default']
  }
}
