{"ast": null, "code": "function _regeneratorRuntime() { \"use strict\"; /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */ _regeneratorRuntime = function _regeneratorRuntime() { return e; }; var t, e = {}, r = Object.prototype, n = r.hasOwnProperty, o = Object.defineProperty || function (t, e, r) { t[e] = r.value; }, i = \"function\" == typeof Symbol ? Symbol : {}, a = i.iterator || \"@@iterator\", c = i.asyncIterator || \"@@asyncIterator\", u = i.toStringTag || \"@@toStringTag\"; function define(t, e, r) { return Object.defineProperty(t, e, { value: r, enumerable: !0, configurable: !0, writable: !0 }), t[e]; } try { define({}, \"\"); } catch (t) { define = function define(t, e, r) { return t[e] = r; }; } function wrap(t, e, r, n) { var i = e && e.prototype instanceof Generator ? e : Generator, a = Object.create(i.prototype), c = new Context(n || []); return o(a, \"_invoke\", { value: makeInvokeMethod(t, r, c) }), a; } function tryCatch(t, e, r) { try { return { type: \"normal\", arg: t.call(e, r) }; } catch (t) { return { type: \"throw\", arg: t }; } } e.wrap = wrap; var h = \"suspendedStart\", l = \"suspendedYield\", f = \"executing\", s = \"completed\", y = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} var p = {}; define(p, a, function () { return this; }); var d = Object.getPrototypeOf, v = d && d(d(values([]))); v && v !== r && n.call(v, a) && (p = v); var g = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(p); function defineIteratorMethods(t) { [\"next\", \"throw\", \"return\"].forEach(function (e) { define(t, e, function (t) { return this._invoke(e, t); }); }); } function AsyncIterator(t, e) { function invoke(r, o, i, a) { var c = tryCatch(t[r], t, o); if (\"throw\" !== c.type) { var u = c.arg, h = u.value; return h && \"object\" == typeof h && n.call(h, \"__await\") ? e.resolve(h.__await).then(function (t) { invoke(\"next\", t, i, a); }, function (t) { invoke(\"throw\", t, i, a); }) : e.resolve(h).then(function (t) { u.value = t, i(u); }, function (t) { return invoke(\"throw\", t, i, a); }); } a(c.arg); } var r; o(this, \"_invoke\", { value: function value(t, n) { function callInvokeWithMethodAndArg() { return new e(function (e, r) { invoke(t, n, e, r); }); } return r = r ? r.then(callInvokeWithMethodAndArg, callInvokeWithMethodAndArg) : callInvokeWithMethodAndArg(); } }); } function makeInvokeMethod(e, r, n) { var o = h; return function (i, a) { if (o === f) throw Error(\"Generator is already running\"); if (o === s) { if (\"throw\" === i) throw a; return { value: t, done: !0 }; } for (n.method = i, n.arg = a;;) { var c = n.delegate; if (c) { var u = maybeInvokeDelegate(c, n); if (u) { if (u === y) continue; return u; } } if (\"next\" === n.method) n.sent = n._sent = n.arg;else if (\"throw\" === n.method) { if (o === h) throw o = s, n.arg; n.dispatchException(n.arg); } else \"return\" === n.method && n.abrupt(\"return\", n.arg); o = f; var p = tryCatch(e, r, n); if (\"normal\" === p.type) { if (o = n.done ? s : l, p.arg === y) continue; return { value: p.arg, done: n.done }; } \"throw\" === p.type && (o = s, n.method = \"throw\", n.arg = p.arg); } }; } function maybeInvokeDelegate(e, r) { var n = r.method, o = e.iterator[n]; if (o === t) return r.delegate = null, \"throw\" === n && e.iterator.return && (r.method = \"return\", r.arg = t, maybeInvokeDelegate(e, r), \"throw\" === r.method) || \"return\" !== n && (r.method = \"throw\", r.arg = new TypeError(\"The iterator does not provide a '\" + n + \"' method\")), y; var i = tryCatch(o, e.iterator, r.arg); if (\"throw\" === i.type) return r.method = \"throw\", r.arg = i.arg, r.delegate = null, y; var a = i.arg; return a ? a.done ? (r[e.resultName] = a.value, r.next = e.nextLoc, \"return\" !== r.method && (r.method = \"next\", r.arg = t), r.delegate = null, y) : a : (r.method = \"throw\", r.arg = new TypeError(\"iterator result is not an object\"), r.delegate = null, y); } function pushTryEntry(t) { var e = { tryLoc: t[0] }; 1 in t && (e.catchLoc = t[1]), 2 in t && (e.finallyLoc = t[2], e.afterLoc = t[3]), this.tryEntries.push(e); } function resetTryEntry(t) { var e = t.completion || {}; e.type = \"normal\", delete e.arg, t.completion = e; } function Context(t) { this.tryEntries = [{ tryLoc: \"root\" }], t.forEach(pushTryEntry, this), this.reset(!0); } function values(e) { if (e || \"\" === e) { var r = e[a]; if (r) return r.call(e); if (\"function\" == typeof e.next) return e; if (!isNaN(e.length)) { var o = -1, i = function next() { for (; ++o < e.length;) if (n.call(e, o)) return next.value = e[o], next.done = !1, next; return next.value = t, next.done = !0, next; }; return i.next = i; } } throw new TypeError(typeof e + \" is not iterable\"); } return GeneratorFunction.prototype = GeneratorFunctionPrototype, o(g, \"constructor\", { value: GeneratorFunctionPrototype, configurable: !0 }), o(GeneratorFunctionPrototype, \"constructor\", { value: GeneratorFunction, configurable: !0 }), GeneratorFunction.displayName = define(GeneratorFunctionPrototype, u, \"GeneratorFunction\"), e.isGeneratorFunction = function (t) { var e = \"function\" == typeof t && t.constructor; return !!e && (e === GeneratorFunction || \"GeneratorFunction\" === (e.displayName || e.name)); }, e.mark = function (t) { return Object.setPrototypeOf ? Object.setPrototypeOf(t, GeneratorFunctionPrototype) : (t.__proto__ = GeneratorFunctionPrototype, define(t, u, \"GeneratorFunction\")), t.prototype = Object.create(g), t; }, e.awrap = function (t) { return { __await: t }; }, defineIteratorMethods(AsyncIterator.prototype), define(AsyncIterator.prototype, c, function () { return this; }), e.AsyncIterator = AsyncIterator, e.async = function (t, r, n, o, i) { void 0 === i && (i = Promise); var a = new AsyncIterator(wrap(t, r, n, o), i); return e.isGeneratorFunction(r) ? a : a.next().then(function (t) { return t.done ? t.value : a.next(); }); }, defineIteratorMethods(g), define(g, u, \"Generator\"), define(g, a, function () { return this; }), define(g, \"toString\", function () { return \"[object Generator]\"; }), e.keys = function (t) { var e = Object(t), r = []; for (var n in e) r.push(n); return r.reverse(), function next() { for (; r.length;) { var t = r.pop(); if (t in e) return next.value = t, next.done = !1, next; } return next.done = !0, next; }; }, e.values = values, Context.prototype = { constructor: Context, reset: function reset(e) { if (this.prev = 0, this.next = 0, this.sent = this._sent = t, this.done = !1, this.delegate = null, this.method = \"next\", this.arg = t, this.tryEntries.forEach(resetTryEntry), !e) for (var r in this) \"t\" === r.charAt(0) && n.call(this, r) && !isNaN(+r.slice(1)) && (this[r] = t); }, stop: function stop() { this.done = !0; var t = this.tryEntries[0].completion; if (\"throw\" === t.type) throw t.arg; return this.rval; }, dispatchException: function dispatchException(e) { if (this.done) throw e; var r = this; function handle(n, o) { return a.type = \"throw\", a.arg = e, r.next = n, o && (r.method = \"next\", r.arg = t), !!o; } for (var o = this.tryEntries.length - 1; o >= 0; --o) { var i = this.tryEntries[o], a = i.completion; if (\"root\" === i.tryLoc) return handle(\"end\"); if (i.tryLoc <= this.prev) { var c = n.call(i, \"catchLoc\"), u = n.call(i, \"finallyLoc\"); if (c && u) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } else if (c) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); } else { if (!u) throw Error(\"try statement without catch or finally\"); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } } } }, abrupt: function abrupt(t, e) { for (var r = this.tryEntries.length - 1; r >= 0; --r) { var o = this.tryEntries[r]; if (o.tryLoc <= this.prev && n.call(o, \"finallyLoc\") && this.prev < o.finallyLoc) { var i = o; break; } } i && (\"break\" === t || \"continue\" === t) && i.tryLoc <= e && e <= i.finallyLoc && (i = null); var a = i ? i.completion : {}; return a.type = t, a.arg = e, i ? (this.method = \"next\", this.next = i.finallyLoc, y) : this.complete(a); }, complete: function complete(t, e) { if (\"throw\" === t.type) throw t.arg; return \"break\" === t.type || \"continue\" === t.type ? this.next = t.arg : \"return\" === t.type ? (this.rval = this.arg = t.arg, this.method = \"return\", this.next = \"end\") : \"normal\" === t.type && e && (this.next = e), y; }, finish: function finish(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.finallyLoc === t) return this.complete(r.completion, r.afterLoc), resetTryEntry(r), y; } }, catch: function _catch(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.tryLoc === t) { var n = r.completion; if (\"throw\" === n.type) { var o = n.arg; resetTryEntry(r); } return o; } } throw Error(\"illegal catch attempt\"); }, delegateYield: function delegateYield(e, r, n) { return this.delegate = { iterator: values(e), resultName: r, nextLoc: n }, \"next\" === this.method && (this.arg = t), y; } }, e; }\nfunction asyncGeneratorStep(n, t, e, r, o, a, c) { try { var i = n[a](c), u = i.value; } catch (n) { return void e(n); } i.done ? t(u) : Promise.resolve(u).then(r, o); }\nfunction _asyncToGenerator(n) { return function () { var t = this, e = arguments; return new Promise(function (r, o) { var a = n.apply(t, e); function _next(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"next\", n); } function _throw(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"throw\", n); } _next(void 0); }); }; }\nimport api from '@/api';\nimport http_stream from 'common/http/stream.js';\nimport { reactive, ref, onMounted, nextTick } from 'vue';\nimport { IntelligentAssistant, whetherAiChat } from 'common/js/system_var.js';\nimport { AIFunctionMethod } from 'common/js/AIFunctionMethod.js';\nimport { ElMessage } from 'element-plus';\nvar __default__ = {\n  name: 'SubmitSuggestReply'\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  props: {\n    id: {\n      type: String,\n      default: ''\n    },\n    unitId: {\n      type: String,\n      default: ''\n    },\n    suggestId: {\n      type: String,\n      default: ''\n    },\n    detailsObjectType: {\n      type: String,\n      default: 'handlingPortionId'\n    }\n  },\n  emits: ['callback'],\n  setup(__props, _ref) {\n    var __expose = _ref.expose,\n      __emit = _ref.emit;\n    __expose();\n    var props = __props;\n    var loadingIcon = '<svg t=\"1716976607389\" viewBox=\"0 0 1024 1024\" version=\"1.1\" p-id=\"2362\" width=\"60%\" height=\"60%\"><path d=\"M827.211075 221.676536m-54.351151 0a54.351151 54.351151 0 1 0 108.702302 0 54.351151 54.351151 0 1 0-108.702302 0Z\" fill=\"#2c2c2c\" p-id=\"2363\"></path><path d=\"M940.905298 515.399947m-67.086951 0a67.086952 67.086952 0 1 0 134.173903 0 67.086952 67.086952 0 1 0-134.173903 0Z\" fill=\"#2c2c2c\" p-id=\"2364\"></path><path d=\"M829.755035 810.595334m-78.974766 0a78.974766 78.974766 0 1 0 157.949532 0 78.974766 78.974766 0 1 0-157.949532 0Z\" fill=\"#2c2c2c\" p-id=\"2365\"></path><path d=\"M534.831643 928.64149m-91.48657 0a91.486571 91.486571 0 1 0 182.973141 0 91.486571 91.486571 0 1 0-182.973141 0Z\" fill=\"#2c2c2c\" p-id=\"2366\"></path><path d=\"M243.780191 805.955407m-101.902408 0a101.902408 101.902408 0 1 0 203.804816 0 101.902408 101.902408 0 1 0-203.804816 0Z\" fill=\"#2c2c2c\" p-id=\"2367\"></path><path d=\"M536.623615 107.870315m-107.854315 0a107.854315 107.854315 0 1 0 215.70863 0 107.854315 107.854315 0 1 0-215.70863 0Z\" fill=\"#2c2c2c\" p-id=\"2368\"></path><path d=\"M243.780191 224.220497m-107.854315 0a107.854315 107.854315 0 1 0 215.70863 0 107.854315 107.854315 0 1 0-215.70863 0Z\" fill=\"#2c2c2c\" p-id=\"2369\"></path><path d=\"M129.429978 512.008m-102.766395 0a102.766394 102.766394 0 1 0 205.532789 0 102.766394 102.766394 0 1 0-205.532789 0Z\" fill=\"#2c2c2c\" p-id=\"2370\"></path></svg>';\n    var ponderIcon = '<svg t=\"1741658991857\" class=\"icon\" viewBox=\"0 0 1024 1024\" version=\"1.1\" xmlns=\"http://www.w3.org/2000/svg\" p-id=\"11203\" width=\"16\" height=\"16\"><path d=\"M886.592 369.152c-42.24 89.28-116.48 190.848-211.456 285.76-94.976 94.976-196.48 169.216-285.824 211.52-44.352 20.992-88.96 35.712-130.24 39.168-40.832 3.456-87.68-3.712-122.432-38.4-34.752-34.816-41.92-81.664-38.464-122.496 3.456-41.216 18.176-85.888 39.168-130.24 42.304-89.28 116.544-190.912 211.456-285.824 94.976-94.912 196.544-169.152 285.824-211.456 44.416-21.056 88.96-35.712 130.24-39.232 40.832-3.456 87.68 3.712 122.496 38.464 34.752 34.752 41.92 81.664 38.4 122.496-3.456 41.216-18.112 85.824-39.168 130.24zM629.888 609.664c182.272-182.272 277.312-382.848 212.224-448-65.152-65.088-265.728 29.952-448 212.224-182.336 182.336-277.376 382.912-212.224 448 65.088 65.152 265.664-29.888 448-212.224z\" p-id=\"11204\"></path><path d=\"M137.344 369.152c42.304 89.28 116.544 190.848 211.52 285.76 94.912 94.976 196.48 169.216 285.76 211.52 44.416 20.992 88.96 35.712 130.24 39.168 40.832 3.456 87.68-3.712 122.496-38.4 34.752-34.816 41.92-81.664 38.4-122.496-3.456-41.216-18.112-85.888-39.168-130.24-42.24-89.28-116.48-190.912-211.456-285.824-94.912-94.912-196.48-169.152-285.824-211.456-44.352-21.056-88.96-35.712-130.24-39.232-40.832-3.456-87.68 3.712-122.432 38.464-34.752 34.752-41.92 81.664-38.464 122.496 3.456 41.216 18.176 85.824 39.168 130.24z m256.768 240.512c-182.336-182.272-277.376-382.848-212.224-448 65.088-65.088 265.664 29.952 448 212.224 182.272 182.336 277.312 382.912 212.224 448-65.152 65.152-265.728-29.888-448-212.224z\" p-id=\"11205\"></path><path d=\"M576 512a64 64 0 1 1-128 0 64 64 0 0 1 128 0z\" p-id=\"11206\"></path></svg>';\n    var emit = __emit;\n    var formRef = ref();\n    var form = reactive({\n      id: '',\n      replyType: '',\n      isOpen: 1,\n      noOpenReason: '',\n      fileData: [],\n      content: '',\n      suggestedReplies: [\n      // 将 SuggestedReplyList 移到 form 中\n      {\n        id: '1',\n        content: '',\n        SuggestedReplyType: '',\n        SuggestedReplyContent: ''\n      }, {\n        id: '2',\n        content: ' ',\n        SuggestedReplyType: '',\n        SuggestedReplyContent: ''\n      }]\n    });\n    var SuggestedReplyType = ref([{\n      id: 'A',\n      name: 'A类'\n    }, {\n      id: 'B',\n      name: 'B类'\n    }, {\n      id: 'C',\n      name: 'C类'\n    }]);\n    var rules = reactive({\n      replyType: [{\n        required: true,\n        message: '请选择答复类型',\n        trigger: ['blur', 'change']\n      }],\n      isOpen: [{\n        required: true,\n        message: '请选择是否公开',\n        trigger: ['blur', 'change']\n      }],\n      fileData: [{\n        required: true,\n        message: '请上传答复件',\n        trigger: ['blur', 'change']\n      }],\n      noOpenReason: [{\n        required: false,\n        message: '请输入不公开理由',\n        trigger: ['blur', 'change']\n      }],\n      content: [{\n        required: true,\n        message: '请输入内容',\n        trigger: ['blur', 'change']\n      }]\n    });\n    var details = ref({});\n    var fileData = ref([]);\n    var replyType = ref([]);\n    var ponderShow = ref(true);\n    var elTime = ref('');\n    var elRef = ref();\n    var elPonderRef = ref();\n    var elData = ref({\n      content: '',\n      contentOld: ''\n    });\n    var elPonderData = ref({\n      content: '',\n      contentOld: ''\n    });\n    var currentRequest = null;\n    var loading = ref(false);\n    var isStreaming = ref(false);\n    var startTime = null;\n    var endTime = null;\n    var guid = function guid() {\n      return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {\n        var r = Math.random() * 16 | 0,\n          v = c == 'x' ? r : r & 0x3 | 0x8;\n        return v.toString(16);\n      });\n    };\n    var _AIFunctionMethod = AIFunctionMethod('ai-proposal-answer'),\n      isAIFunction = _AIFunctionMethod.isAIFunction;\n    onMounted(function () {\n      dictionaryData();\n      proposalInventoryList();\n      if (props.id) {\n        handingPortionAnswerInfo();\n      }\n      suggestionInfo();\n    });\n    var dictionaryData = /*#__PURE__*/function () {\n      var _ref2 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee() {\n        var res, data;\n        return _regeneratorRuntime().wrap(function _callee$(_context) {\n          while (1) switch (_context.prev = _context.next) {\n            case 0:\n              _context.next = 2;\n              return api.dictionaryData({\n                dictCodes: ['suggestion_answer_type']\n              });\n            case 2:\n              res = _context.sent;\n              data = res.data;\n              replyType.value = data.suggestion_answer_type;\n            case 5:\n            case \"end\":\n              return _context.stop();\n          }\n        }, _callee);\n      }));\n      return function dictionaryData() {\n        return _ref2.apply(this, arguments);\n      };\n    }();\n    var proposalInventoryList = /*#__PURE__*/function () {\n      var _ref3 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee2() {\n        var res;\n        return _regeneratorRuntime().wrap(function _callee2$(_context2) {\n          while (1) switch (_context2.prev = _context2.next) {\n            case 0:\n              _context2.next = 2;\n              return api.proposalInventoryList({\n                pageNo: '1',\n                pageSize: '999',\n                query: {\n                  suggestionId: props.suggestId\n                }\n              });\n            case 2:\n              res = _context2.sent;\n              form.suggestedReplies = res.data;\n            case 4:\n            case \"end\":\n              return _context2.stop();\n          }\n        }, _callee2);\n      }));\n      return function proposalInventoryList() {\n        return _ref3.apply(this, arguments);\n      };\n    }();\n    var isOpenChange = function isOpenChange() {\n      rules.noOpenReason = [{\n        required: false,\n        message: '请输入不公开理由',\n        trigger: ['blur', 'change']\n      }];\n      if (!form.isOpen) {\n        rules.noOpenReason = [{\n          required: true,\n          message: '请输入不公开理由',\n          trigger: ['blur', 'change']\n        }];\n      }\n    };\n    var fileUpload = function fileUpload(file) {\n      form.fileData = file.map(function (v) {\n        return v.id;\n      });\n      fileData.value = file;\n      formRef.value.validateField('fileData');\n      nextTick(function () {\n        var _elRef$value, _elPonderRef$value;\n        elTime.value = '';\n        elData.value = {\n          content: '',\n          contentOld: ''\n        };\n        elPonderData.value = {\n          content: '',\n          contentOld: ''\n        };\n        (_elRef$value = elRef.value) === null || _elRef$value === void 0 || _elRef$value.clearContent();\n        (_elPonderRef$value = elPonderRef.value) === null || _elPonderRef$value === void 0 || _elPonderRef$value.clearContent();\n        handleStopMessage();\n        if (fileData.value.length) {\n          handleHttpStream();\n        }\n      });\n    };\n    var suggestionInfo = /*#__PURE__*/function () {\n      var _ref4 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee3() {\n        var _yield$api$suggestion, data;\n        return _regeneratorRuntime().wrap(function _callee3$(_context3) {\n          while (1) switch (_context3.prev = _context3.next) {\n            case 0:\n              _context3.next = 2;\n              return api.suggestionInfo({\n                detailId: props.suggestId\n              });\n            case 2:\n              _yield$api$suggestion = _context3.sent;\n              data = _yield$api$suggestion.data;\n              details.value = data;\n            case 5:\n            case \"end\":\n              return _context3.stop();\n          }\n        }, _callee3);\n      }));\n      return function suggestionInfo() {\n        return _ref4.apply(this, arguments);\n      };\n    }();\n    var handingPortionAnswerInfo = /*#__PURE__*/function () {\n      var _ref5 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee4() {\n        var _data$suggestionAnswe;\n        var params, res, data;\n        return _regeneratorRuntime().wrap(function _callee4$(_context4) {\n          while (1) switch (_context4.prev = _context4.next) {\n            case 0:\n              params = {};\n              params[props.detailsObjectType] = props.id;\n              _context4.next = 4;\n              return api.handingPortionAnswerInfo(params);\n            case 4:\n              res = _context4.sent;\n              data = res.data;\n              form.id = data.id;\n              form.replyType = (_data$suggestionAnswe = data.suggestionAnswerType) === null || _data$suggestionAnswe === void 0 ? void 0 : _data$suggestionAnswe.value;\n              form.isOpen = data.isOpen;\n              form.noOpenReason = data.noOpenReason;\n              form.content = data.content;\n              fileData.value = data.attachments || [];\n              form.fileData = data.attachments.map(function (v) {\n                return v.id;\n              });\n            case 13:\n            case \"end\":\n              return _context4.stop();\n          }\n        }, _callee4);\n      }));\n      return function handingPortionAnswerInfo() {\n        return _ref5.apply(this, arguments);\n      };\n    }();\n    var submitForm = /*#__PURE__*/function () {\n      var _ref6 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee5(formEl) {\n        return _regeneratorRuntime().wrap(function _callee5$(_context5) {\n          while (1) switch (_context5.prev = _context5.next) {\n            case 0:\n              if (formEl) {\n                _context5.next = 2;\n                break;\n              }\n              return _context5.abrupt(\"return\");\n            case 2:\n              _context5.next = 4;\n              return formEl.validate(function (valid, fields) {\n                if (valid) {\n                  globalJson();\n                } else {\n                  ElMessage({\n                    type: 'warning',\n                    message: '请根据提示信息完善字段内容！'\n                  });\n                }\n              });\n            case 4:\n            case \"end\":\n              return _context5.stop();\n          }\n        }, _callee5);\n      }));\n      return function submitForm(_x) {\n        return _ref6.apply(this, arguments);\n      };\n    }();\n    var globalJson = /*#__PURE__*/function () {\n      var _ref7 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee6() {\n        var _yield$api$globalJson, code;\n        return _regeneratorRuntime().wrap(function _callee6$(_context6) {\n          while (1) switch (_context6.prev = _context6.next) {\n            case 0:\n              _context6.next = 2;\n              return api.globalJson(props.id ? '/cppcc/handingPortionAnswer/edit' : '/cppcc/handingPortionAnswer/add', {\n                form: {\n                  id: form.id,\n                  handlingPortionId: props.unitId,\n                  suggestionId: props.suggestId,\n                  suggestionAnswerType: form.replyType,\n                  isOpen: form.isOpen,\n                  noOpenReason: form.isOpen ? '' : form.noOpenReason,\n                  content: form.content,\n                  attachmentIds: fileData.value.map(function (v) {\n                    return v.id;\n                  })\n                }\n              });\n            case 2:\n              _yield$api$globalJson = _context6.sent;\n              code = _yield$api$globalJson.code;\n              if (code === 200) {\n                ElMessage({\n                  type: 'success',\n                  message: props.id ? '编辑成功' : '新增成功'\n                });\n                emit('callback', true);\n              }\n            case 5:\n            case \"end\":\n              return _context6.stop();\n          }\n        }, _callee6);\n      }));\n      return function globalJson() {\n        return _ref7.apply(this, arguments);\n      };\n    }();\n    var resetForm = function resetForm() {\n      emit('callback');\n    };\n    var formatDuring = function formatDuring(mss) {\n      var days = parseInt(mss / (1000 * 60 * 60 * 24));\n      var hours = parseInt(mss % (1000 * 60 * 60 * 24) / (1000 * 60 * 60));\n      var minutes = parseInt(mss % (1000 * 60 * 60) / (1000 * 60));\n      var seconds = mss % (1000 * 60) / 1000;\n      var time = '';\n      if (days > 0) time += `${days} 天 `;\n      if (hours > 0) time += `${hours} 小时 `;\n      if (minutes > 0) time += `${minutes} 分钟 `;\n      if (seconds > 0) time += `${seconds} 秒 `;\n      return time;\n    };\n    var handleHttpStream = /*#__PURE__*/function () {\n      var _ref8 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee7() {\n        var AiChatParam, data, _choice$, choice, _details, _elPonderRef$value2, executionTime, _elRef$value2, _elRef$value3, _elRef$value4;\n        return _regeneratorRuntime().wrap(function _callee7$(_context7) {\n          while (1) switch (_context7.prev = _context7.next) {\n            case 0:\n              if (whetherAiChat.value) {\n                _context7.next = 2;\n                break;\n              }\n              return _context7.abrupt(\"return\");\n            case 2:\n              startTime = new Date();\n              loading.value = true;\n              isStreaming.value = true;\n              _context7.prev = 5;\n              AiChatParam = {\n                chatBusinessScene: 'ai-proposal-answer',\n                chatId: guid(),\n                question: '提案答复件智能检测',\n                businessId: props.suggestId,\n                attachmentIds: fileData.value.map(function (v) {\n                  return v.id;\n                }).join(',')\n              };\n              currentRequest = http_stream('/aigpt/chatStream', {\n                body: JSON.stringify(AiChatParam),\n                onMessage(event) {\n                  // if (event.data === '{\\\"status\\\":\\\"running\\\",\\\"name\\\":\\\"AI 对话\\\"}') loading.value = false\n                  loading.value = false;\n                  if (event.data !== '[DONE]') {\n                    data = JSON.parse(event.data);\n                    if (Array.isArray(data)) {\n                      console.log('[]', data);\n                    } else {\n                      // console.log('{}', data)\n                      choice = (data === null || data === void 0 ? void 0 : data.choices) || [{}];\n                      _details = ((_choice$ = choice[0]) === null || _choice$ === void 0 ? void 0 : _choice$.delta) || {};\n                      if (Object.prototype.hasOwnProperty.call(_details, 'reasoning_content')) {\n                        (_elPonderRef$value2 = elPonderRef.value) === null || _elPonderRef$value2 === void 0 || _elPonderRef$value2.enqueueRender(_details.reasoning_content || '');\n                        if (elTime.value) {\n                          startTime = null;\n                          endTime = null;\n                        } else {\n                          endTime = new Date();\n                          executionTime = endTime - startTime;\n                          elTime.value = formatDuring(executionTime);\n                        }\n                      }\n                      if (Object.prototype.hasOwnProperty.call(_details, 'content')) {\n                        (_elRef$value2 = elRef.value) === null || _elRef$value2 === void 0 || _elRef$value2.enqueueRender(_details.content || '');\n                      }\n                    }\n                  } else {\n                    // console.log(event.data)\n                    (_elRef$value3 = elRef.value) === null || _elRef$value3 === void 0 || _elRef$value3.enqueueRender('');\n                    isStreaming.value = false;\n                  }\n                },\n                onError(err) {\n                  console.log('流式接口错误:', err);\n                },\n                onClose() {\n                  loading.value = false;\n                  isStreaming.value = false;\n                  console.log('流式接口关闭');\n                }\n              });\n              _context7.next = 10;\n              return currentRequest.promise;\n            case 10:\n              _context7.next = 18;\n              break;\n            case 12:\n              _context7.prev = 12;\n              _context7.t0 = _context7[\"catch\"](5);\n              loading.value = false;\n              isStreaming.value = false;\n              (_elRef$value4 = elRef.value) === null || _elRef$value4 === void 0 || _elRef$value4.enqueueRender('服务器繁忙，请稍后再试。');\n              console.error('启动流式接口失败:', _context7.t0);\n            case 18:\n              _context7.prev = 18;\n              return _context7.finish(18);\n            case 20:\n            case \"end\":\n              return _context7.stop();\n          }\n        }, _callee7, null, [[5, 12, 18, 20]]);\n      }));\n      return function handleHttpStream() {\n        return _ref8.apply(this, arguments);\n      };\n    }();\n    var handleCloseMessage = function handleCloseMessage() {\n      currentRequest = null;\n      loading.value = false;\n      isStreaming.value = false;\n    };\n    var handleStopMessage = function handleStopMessage() {\n      if (currentRequest) {\n        currentRequest.abort();\n        loading.value = false;\n        isStreaming.value = false;\n        console.log('启动流式接口停止');\n      }\n      handleCloseMessage();\n    };\n    var __returned__ = {\n      props,\n      loadingIcon,\n      ponderIcon,\n      emit,\n      formRef,\n      form,\n      SuggestedReplyType,\n      rules,\n      details,\n      fileData,\n      replyType,\n      ponderShow,\n      elTime,\n      elRef,\n      elPonderRef,\n      elData,\n      elPonderData,\n      get currentRequest() {\n        return currentRequest;\n      },\n      set currentRequest(v) {\n        currentRequest = v;\n      },\n      loading,\n      isStreaming,\n      get startTime() {\n        return startTime;\n      },\n      set startTime(v) {\n        startTime = v;\n      },\n      get endTime() {\n        return endTime;\n      },\n      set endTime(v) {\n        endTime = v;\n      },\n      guid,\n      isAIFunction,\n      dictionaryData,\n      proposalInventoryList,\n      isOpenChange,\n      fileUpload,\n      suggestionInfo,\n      handingPortionAnswerInfo,\n      submitForm,\n      globalJson,\n      resetForm,\n      formatDuring,\n      handleHttpStream,\n      handleCloseMessage,\n      handleStopMessage,\n      get api() {\n        return api;\n      },\n      get http_stream() {\n        return http_stream;\n      },\n      reactive,\n      ref,\n      onMounted,\n      nextTick,\n      get IntelligentAssistant() {\n        return IntelligentAssistant;\n      },\n      get whetherAiChat() {\n        return whetherAiChat;\n      },\n      get AIFunctionMethod() {\n        return AIFunctionMethod;\n      },\n      get ElMessage() {\n        return ElMessage;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["_regeneratorRuntime", "e", "t", "r", "Object", "prototype", "n", "hasOwnProperty", "o", "defineProperty", "value", "i", "Symbol", "a", "iterator", "c", "asyncIterator", "u", "toStringTag", "define", "enumerable", "configurable", "writable", "wrap", "Generator", "create", "Context", "makeInvokeMethod", "tryCatch", "type", "arg", "call", "h", "l", "f", "s", "y", "GeneratorFunction", "GeneratorFunctionPrototype", "p", "d", "getPrototypeOf", "v", "values", "g", "defineIteratorMethods", "for<PERSON>ach", "_invoke", "AsyncIterator", "invoke", "resolve", "__await", "then", "callInvokeWithMethodAndArg", "Error", "done", "method", "delegate", "maybeInvokeDelegate", "sent", "_sent", "dispatchException", "abrupt", "return", "TypeError", "resultName", "next", "nextLoc", "pushTryEntry", "tryLoc", "catchLoc", "finallyLoc", "afterLoc", "tryEntries", "push", "resetTryEntry", "completion", "reset", "isNaN", "length", "displayName", "isGeneratorFunction", "constructor", "name", "mark", "setPrototypeOf", "__proto__", "awrap", "async", "Promise", "keys", "reverse", "pop", "prev", "char<PERSON>t", "slice", "stop", "rval", "handle", "complete", "finish", "catch", "_catch", "<PERSON><PERSON><PERSON>", "asyncGeneratorStep", "_asyncToGenerator", "arguments", "apply", "_next", "_throw", "api", "http_stream", "reactive", "ref", "onMounted", "nextTick", "IntelligentAssistant", "whetherAiChat", "AIFunctionMethod", "ElMessage", "__default__", "props", "__props", "loadingIcon", "ponderIcon", "emit", "__emit", "formRef", "form", "id", "replyType", "isOpen", "noOpenReason", "fileData", "content", "suggestedReplies", "SuggestedReplyType", "Suggested<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rules", "required", "message", "trigger", "details", "ponderShow", "elTime", "elRef", "elPonderRef", "elData", "contentOld", "elPonderData", "currentRequest", "loading", "isStreaming", "startTime", "endTime", "guid", "replace", "Math", "random", "toString", "_AIFunctionMethod", "isAIFunction", "dictionaryData", "proposalInventoryList", "handingPortionAnswerInfo", "suggestionInfo", "_ref2", "_callee", "res", "data", "_callee$", "_context", "dictCodes", "suggestion_answer_type", "_ref3", "_callee2", "_callee2$", "_context2", "pageNo", "pageSize", "query", "suggestionId", "suggestId", "isOpenChange", "fileUpload", "file", "map", "validateField", "_elRef$value", "_elPonderRef$value", "clearContent", "handleStopMessage", "handleHttpStream", "_ref4", "_callee3", "_yield$api$suggestion", "_callee3$", "_context3", "detailId", "_ref5", "_callee4", "_data$suggestionAnswe", "params", "_callee4$", "_context4", "detailsObjectType", "suggestionAnswerType", "attachments", "submitForm", "_ref6", "_callee5", "formEl", "_callee5$", "_context5", "validate", "valid", "fields", "globalJson", "_x", "_ref7", "_callee6", "_yield$api$globalJson", "code", "_callee6$", "_context6", "handlingPortionId", "unitId", "attachmentIds", "resetForm", "formatDuring", "mss", "days", "parseInt", "hours", "minutes", "seconds", "time", "_ref8", "_callee7", "AiChatParam", "_choice$", "choice", "_details", "_elPonderRef$value2", "executionTime", "_elRef$value2", "_elRef$value3", "_elRef$value4", "_callee7$", "_context7", "Date", "chatBusinessScene", "chatId", "question", "businessId", "join", "body", "JSON", "stringify", "onMessage", "event", "parse", "Array", "isArray", "console", "log", "choices", "delta", "enqueueRender", "reasoning_content", "onError", "err", "onClose", "promise", "t0", "error", "handleCloseMessage", "abort"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/microApp/proposal/src/views/UnitSuggestDetail/component/SubmitSuggestReply.vue"], "sourcesContent": ["<template>\r\n  <div class=\"SubmitSuggestReply\">\r\n    <el-form ref=\"formRef\" :model=\"form\" :rules=\"rules\" inline label-position=\"top\" class=\"globalForm\"\r\n      @submit.enter.prevent>\r\n      <el-form-item label=\"答复类型\" prop=\"replyType\">\r\n        <el-select v-model=\"form.replyType\" placeholder=\"请选择答复类型\" clearable>\r\n          <el-option v-for=\"item in replyType\" :key=\"item.id\" :label=\"item.name\" :value=\"item.id\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"是否公开\" prop=\"isOpen\">\r\n        <el-radio-group v-model=\"form.isOpen\" @change=\"isOpenChange\">\r\n          <el-radio :label=\"1\">公开</el-radio>\r\n          <el-radio :label=\"0\">不公开</el-radio>\r\n        </el-radio-group>\r\n      </el-form-item>\r\n      <el-form-item label=\"不公开理由\" prop=\"noOpenReason\" v-show=\"!form.isOpen\" class=\"globalFormTitle\">\r\n        <el-input v-model=\"form.noOpenReason\" placeholder=\"请输入不公开理由\" type=\"textarea\" :rows=\"5\" clearable />\r\n      </el-form-item>\r\n      <el-form-item label=\"上传答复件\" prop=\"fileData\" class=\"globalFormTitle\">\r\n        <xyl-upload-file :fileData=\"fileData\" @fileUpload=\"fileUpload\" />\r\n      </el-form-item>\r\n      <div class=\"GlobalAiChatProposalReply\" v-if=\"whetherAiChat && isAIFunction\">\r\n        <div class=\"GlobalAiChatProposalHead\">\r\n          <el-image :src=\"IntelligentAssistant\" loading=\"lazy\" fit=\"cover\" draggable=\"false\" />\r\n          <div class=\"GlobalAiChatProposalName\">答复件符合性评估</div>\r\n        </div>\r\n        <div class=\"GlobalAiChatProposalLoading\" v-if=\"loading\">\r\n          <div class=\"answerLoading\" v-html=\"loadingIcon\"></div>\r\n        </div>\r\n        <div class=\"GlobalAiChatProposalPonder forbidSelect\" @click=\"ponderShow = !ponderShow\" v-if=\"elTime\">\r\n          <div v-html=\"ponderIcon\"></div>\r\n          已深度思考\r\n          <span v-if=\"elTime !== '1'\">（用时 {{ elTime }}）</span>\r\n          <el-icon>\r\n            <ArrowUpBold v-if=\"ponderShow\" />\r\n            <ArrowDownBold v-if=\"!ponderShow\" />\r\n          </el-icon>\r\n        </div>\r\n        <div class=\"GlobalAiChatProposalPonderContent\" v-show=\"ponderShow\">\r\n          <GlobalMarkdown ref=\"elPonderRef\" v-model=\"elPonderData.content\" :content=\"elPonderData.contentOld\" />\r\n        </div>\r\n        <div class=\"GlobalAiChatProposalContent\">\r\n          <GlobalMarkdown ref=\"elRef\" v-model=\"elData.content\" :content=\"elData.contentOld\" />\r\n        </div>\r\n      </div>\r\n      <el-form-item label=\"内容\" prop=\"content\" class=\"globalFormTitle\">\r\n        <TinyMceEditor v-model=\"form.content\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"意见建议清单答复\" class=\"globalFormTitle\" prop=\"suggestedReplies\"\r\n        v-if=\"form.suggestedReplies && form.suggestedReplies.length > 0\">\r\n        <div v-for=\"(item, index) in form.suggestedReplies\" :key=\"index\" class=\"SuggestedReplyList\">\r\n          <div style=\"width: 8%;\">建议{{ index + 1 }}</div>\r\n          <div style=\"width: 42%;\">{{ item.content }}</div>\r\n          <div style=\"width: 50%;margin-left: 20px;\">\r\n            <el-form-item label=\"答复类型\" prop=\"SuggestedReplyType\" class=\"globalFormTitle\">\r\n              <el-select v-model=\"item.SuggestedReplyType\" placeholder=\"请选择答复类型\" clearable>\r\n                <el-option v-for=\"items in SuggestedReplyType\" :key=\"items.id\" :label=\"items.name\" :value=\"items.id\" />\r\n              </el-select>\r\n            </el-form-item>\r\n            <el-form-item label=\"答复内容\" prop=\"SuggestedReplyContent\" style=\"margin-top: 12px;\" class=\"globalFormTitle\">\r\n              <el-input v-model=\"item.SuggestedReplyContent\" placeholder=\"请输入答复内容\" type=\"textarea\" :rows=\"3\"\r\n                clearable />\r\n            </el-form-item>\r\n          </div>\r\n        </div>\r\n      </el-form-item>\r\n      <div class=\"globalFormButton\">\r\n        <el-button type=\"primary\" @click=\"submitForm(formRef)\">提交</el-button>\r\n        <el-button @click=\"resetForm\">取消</el-button>\r\n      </div>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'SubmitSuggestReply' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport http_stream from 'common/http/stream.js'\r\nimport { reactive, ref, onMounted, nextTick } from 'vue'\r\nimport { IntelligentAssistant, whetherAiChat } from 'common/js/system_var.js'\r\nimport { AIFunctionMethod } from 'common/js/AIFunctionMethod.js'\r\nimport { ElMessage } from 'element-plus'\r\nconst props = defineProps({\r\n  id: { type: String, default: '' },\r\n  unitId: { type: String, default: '' },\r\n  suggestId: { type: String, default: '' },\r\n  detailsObjectType: { type: String, default: 'handlingPortionId' }\r\n})\r\nconst loadingIcon =\r\n  '<svg t=\"1716976607389\" viewBox=\"0 0 1024 1024\" version=\"1.1\" p-id=\"2362\" width=\"60%\" height=\"60%\"><path d=\"M827.211075 221.676536m-54.351151 0a54.351151 54.351151 0 1 0 108.702302 0 54.351151 54.351151 0 1 0-108.702302 0Z\" fill=\"#2c2c2c\" p-id=\"2363\"></path><path d=\"M940.905298 515.399947m-67.086951 0a67.086952 67.086952 0 1 0 134.173903 0 67.086952 67.086952 0 1 0-134.173903 0Z\" fill=\"#2c2c2c\" p-id=\"2364\"></path><path d=\"M829.755035 810.595334m-78.974766 0a78.974766 78.974766 0 1 0 157.949532 0 78.974766 78.974766 0 1 0-157.949532 0Z\" fill=\"#2c2c2c\" p-id=\"2365\"></path><path d=\"M534.831643 928.64149m-91.48657 0a91.486571 91.486571 0 1 0 182.973141 0 91.486571 91.486571 0 1 0-182.973141 0Z\" fill=\"#2c2c2c\" p-id=\"2366\"></path><path d=\"M243.780191 805.955407m-101.902408 0a101.902408 101.902408 0 1 0 203.804816 0 101.902408 101.902408 0 1 0-203.804816 0Z\" fill=\"#2c2c2c\" p-id=\"2367\"></path><path d=\"M536.623615 107.870315m-107.854315 0a107.854315 107.854315 0 1 0 215.70863 0 107.854315 107.854315 0 1 0-215.70863 0Z\" fill=\"#2c2c2c\" p-id=\"2368\"></path><path d=\"M243.780191 224.220497m-107.854315 0a107.854315 107.854315 0 1 0 215.70863 0 107.854315 107.854315 0 1 0-215.70863 0Z\" fill=\"#2c2c2c\" p-id=\"2369\"></path><path d=\"M129.429978 512.008m-102.766395 0a102.766394 102.766394 0 1 0 205.532789 0 102.766394 102.766394 0 1 0-205.532789 0Z\" fill=\"#2c2c2c\" p-id=\"2370\"></path></svg>'\r\nconst ponderIcon =\r\n  '<svg t=\"1741658991857\" class=\"icon\" viewBox=\"0 0 1024 1024\" version=\"1.1\" xmlns=\"http://www.w3.org/2000/svg\" p-id=\"11203\" width=\"16\" height=\"16\"><path d=\"M886.592 369.152c-42.24 89.28-116.48 190.848-211.456 285.76-94.976 94.976-196.48 169.216-285.824 211.52-44.352 20.992-88.96 35.712-130.24 39.168-40.832 3.456-87.68-3.712-122.432-38.4-34.752-34.816-41.92-81.664-38.464-122.496 3.456-41.216 18.176-85.888 39.168-130.24 42.304-89.28 116.544-190.912 211.456-285.824 94.976-94.912 196.544-169.152 285.824-211.456 44.416-21.056 88.96-35.712 130.24-39.232 40.832-3.456 87.68 3.712 122.496 38.464 34.752 34.752 41.92 81.664 38.4 122.496-3.456 41.216-18.112 85.824-39.168 130.24zM629.888 609.664c182.272-182.272 277.312-382.848 212.224-448-65.152-65.088-265.728 29.952-448 212.224-182.336 182.336-277.376 382.912-212.224 448 65.088 65.152 265.664-29.888 448-212.224z\" p-id=\"11204\"></path><path d=\"M137.344 369.152c42.304 89.28 116.544 190.848 211.52 285.76 94.912 94.976 196.48 169.216 285.76 211.52 44.416 20.992 88.96 35.712 130.24 39.168 40.832 3.456 87.68-3.712 122.496-38.4 34.752-34.816 41.92-81.664 38.4-122.496-3.456-41.216-18.112-85.888-39.168-130.24-42.24-89.28-116.48-190.912-211.456-285.824-94.912-94.912-196.48-169.152-285.824-211.456-44.352-21.056-88.96-35.712-130.24-39.232-40.832-3.456-87.68 3.712-122.432 38.464-34.752 34.752-41.92 81.664-38.464 122.496 3.456 41.216 18.176 85.824 39.168 130.24z m256.768 240.512c-182.336-182.272-277.376-382.848-212.224-448 65.088-65.088 265.664 29.952 448 212.224 182.272 182.336 277.312 382.912 212.224 448-65.152 65.152-265.728-29.888-448-212.224z\" p-id=\"11205\"></path><path d=\"M576 512a64 64 0 1 1-128 0 64 64 0 0 1 128 0z\" p-id=\"11206\"></path></svg>'\r\nconst emit = defineEmits(['callback'])\r\n\r\nconst formRef = ref()\r\nconst form = reactive({\r\n  id: '',\r\n  replyType: '',\r\n  isOpen: 1,\r\n  noOpenReason: '',\r\n  fileData: [],\r\n  content: '',\r\n  suggestedReplies: [ // 将 SuggestedReplyList 移到 form 中\r\n    { id: '1', content: '', SuggestedReplyType: '', SuggestedReplyContent: '' },\r\n    { id: '2', content: ' ', SuggestedReplyType: '', SuggestedReplyContent: '' }\r\n  ]\r\n})\r\nconst SuggestedReplyType = ref([\r\n  { id: 'A', name: 'A类' },\r\n  { id: 'B', name: 'B类' },\r\n  { id: 'C', name: 'C类' }\r\n])\r\nconst rules = reactive({\r\n  replyType: [{ required: true, message: '请选择答复类型', trigger: ['blur', 'change'] }],\r\n  isOpen: [{ required: true, message: '请选择是否公开', trigger: ['blur', 'change'] }],\r\n  fileData: [{ required: true, message: '请上传答复件', trigger: ['blur', 'change'] }],\r\n  noOpenReason: [{ required: false, message: '请输入不公开理由', trigger: ['blur', 'change'] }],\r\n  content: [{ required: true, message: '请输入内容', trigger: ['blur', 'change'] }]\r\n})\r\nconst details = ref({})\r\nconst fileData = ref([])\r\nconst replyType = ref([])\r\n\r\nconst ponderShow = ref(true)\r\nconst elTime = ref('')\r\nconst elRef = ref()\r\nconst elPonderRef = ref()\r\nconst elData = ref({ content: '', contentOld: '' })\r\nconst elPonderData = ref({ content: '', contentOld: '' })\r\n\r\nlet currentRequest = null\r\nconst loading = ref(false)\r\nconst isStreaming = ref(false)\r\nlet startTime = null\r\nlet endTime = null\r\n\r\nconst guid = () => {\r\n  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {\r\n    var r = (Math.random() * 16) | 0,\r\n      v = c == 'x' ? r : (r & 0x3) | 0x8\r\n    return v.toString(16)\r\n  })\r\n}\r\nconst { isAIFunction } = AIFunctionMethod('ai-proposal-answer')\r\nonMounted(() => {\r\n  dictionaryData()\r\n  proposalInventoryList()\r\n  if (props.id) { handingPortionAnswerInfo() }\r\n  suggestionInfo()\r\n})\r\n\r\nconst dictionaryData = async () => {\r\n  const res = await api.dictionaryData({ dictCodes: ['suggestion_answer_type'] })\r\n  var { data } = res\r\n  replyType.value = data.suggestion_answer_type\r\n}\r\n\r\nconst proposalInventoryList = async () => {\r\n  const res = await api.proposalInventoryList({ pageNo: '1', pageSize: '999', query: { suggestionId: props.suggestId } })\r\n  form.suggestedReplies = res.data\r\n}\r\n\r\nconst isOpenChange = () => {\r\n  rules.noOpenReason = [{ required: false, message: '请输入不公开理由', trigger: ['blur', 'change'] }]\r\n  if (!form.isOpen) {\r\n    rules.noOpenReason = [{ required: true, message: '请输入不公开理由', trigger: ['blur', 'change'] }]\r\n  }\r\n}\r\nconst fileUpload = (file) => {\r\n  form.fileData = file.map((v) => v.id)\r\n  fileData.value = file\r\n  formRef.value.validateField('fileData')\r\n  nextTick(() => {\r\n    elTime.value = ''\r\n    elData.value = { content: '', contentOld: '' }\r\n    elPonderData.value = { content: '', contentOld: '' }\r\n    elRef.value?.clearContent()\r\n    elPonderRef.value?.clearContent()\r\n    handleStopMessage()\r\n    if (fileData.value.length) {\r\n      handleHttpStream()\r\n    }\r\n  })\r\n}\r\nconst suggestionInfo = async () => {\r\n  const { data } = await api.suggestionInfo({ detailId: props.suggestId })\r\n  details.value = data\r\n}\r\nconst handingPortionAnswerInfo = async () => {\r\n  var params = {}\r\n  params[props.detailsObjectType] = props.id\r\n  const res = await api.handingPortionAnswerInfo(params)\r\n  var { data } = res\r\n  form.id = data.id\r\n  form.replyType = data.suggestionAnswerType?.value\r\n  form.isOpen = data.isOpen\r\n  form.noOpenReason = data.noOpenReason\r\n  form.content = data.content\r\n  fileData.value = data.attachments || []\r\n  form.fileData = data.attachments.map((v) => v.id)\r\n}\r\nconst submitForm = async (formEl) => {\r\n  if (!formEl) return\r\n  await formEl.validate((valid, fields) => {\r\n    if (valid) {\r\n      globalJson()\r\n    } else {\r\n      ElMessage({ type: 'warning', message: '请根据提示信息完善字段内容！' })\r\n    }\r\n  })\r\n}\r\nconst globalJson = async () => {\r\n  const { code } = await api.globalJson(\r\n    props.id ? '/cppcc/handingPortionAnswer/edit' : '/cppcc/handingPortionAnswer/add',\r\n    {\r\n      form: {\r\n        id: form.id,\r\n        handlingPortionId: props.unitId,\r\n        suggestionId: props.suggestId,\r\n        suggestionAnswerType: form.replyType,\r\n        isOpen: form.isOpen,\r\n        noOpenReason: form.isOpen ? '' : form.noOpenReason,\r\n        content: form.content,\r\n        attachmentIds: fileData.value.map((v) => v.id)\r\n      }\r\n    }\r\n  )\r\n  if (code === 200) {\r\n    ElMessage({ type: 'success', message: props.id ? '编辑成功' : '新增成功' })\r\n    emit('callback', true)\r\n  }\r\n}\r\nconst resetForm = () => {\r\n  emit('callback')\r\n}\r\n\r\nconst formatDuring = (mss) => {\r\n  const days = parseInt(mss / (1000 * 60 * 60 * 24))\r\n  const hours = parseInt((mss % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))\r\n  const minutes = parseInt((mss % (1000 * 60 * 60)) / (1000 * 60))\r\n  const seconds = (mss % (1000 * 60)) / 1000\r\n  var time = ''\r\n  if (days > 0) time += `${days} 天 `\r\n  if (hours > 0) time += `${hours} 小时 `\r\n  if (minutes > 0) time += `${minutes} 分钟 `\r\n  if (seconds > 0) time += `${seconds} 秒 `\r\n  return time\r\n}\r\nconst handleHttpStream = async () => {\r\n  if (!whetherAiChat.value) return\r\n  startTime = new Date()\r\n  loading.value = true\r\n  isStreaming.value = true\r\n  try {\r\n    const AiChatParam = {\r\n      chatBusinessScene: 'ai-proposal-answer',\r\n      chatId: guid(),\r\n      question: '提案答复件智能检测',\r\n      businessId: props.suggestId,\r\n      attachmentIds: fileData.value.map((v) => v.id).join(',')\r\n    }\r\n    currentRequest = http_stream('/aigpt/chatStream', {\r\n      body: JSON.stringify(AiChatParam),\r\n      onMessage (event) {\r\n        // if (event.data === '{\\\"status\\\":\\\"running\\\",\\\"name\\\":\\\"AI 对话\\\"}') loading.value = false\r\n        loading.value = false\r\n        if (event.data !== '[DONE]') {\r\n          const data = JSON.parse(event.data)\r\n          if (Array.isArray(data)) {\r\n            console.log('[]', data)\r\n          } else {\r\n            // console.log('{}', data)\r\n            const choice = data?.choices || [{}]\r\n            const details = choice[0]?.delta || {}\r\n            if (Object.prototype.hasOwnProperty.call(details, 'reasoning_content')) {\r\n              elPonderRef.value?.enqueueRender(details.reasoning_content || '')\r\n              if (elTime.value) {\r\n                startTime = null\r\n                endTime = null\r\n              } else {\r\n                endTime = new Date()\r\n                const executionTime = endTime - startTime\r\n                elTime.value = formatDuring(executionTime)\r\n              }\r\n            }\r\n            if (Object.prototype.hasOwnProperty.call(details, 'content')) {\r\n              elRef.value?.enqueueRender(details.content || '')\r\n            }\r\n          }\r\n        } else {\r\n          // console.log(event.data)\r\n          elRef.value?.enqueueRender('')\r\n          isStreaming.value = false\r\n        }\r\n      },\r\n      onError (err) {\r\n        console.log('流式接口错误:', err)\r\n      },\r\n      onClose () {\r\n        loading.value = false\r\n        isStreaming.value = false\r\n        console.log('流式接口关闭')\r\n      }\r\n    })\r\n    await currentRequest.promise\r\n  } catch (error) {\r\n    loading.value = false\r\n    isStreaming.value = false\r\n    elRef.value?.enqueueRender('服务器繁忙，请稍后再试。')\r\n    console.error('启动流式接口失败:', error)\r\n  } finally {\r\n    // currentRequest = null\r\n  }\r\n}\r\nconst handleCloseMessage = () => {\r\n  currentRequest = null\r\n  loading.value = false\r\n  isStreaming.value = false\r\n}\r\nconst handleStopMessage = () => {\r\n  if (currentRequest) {\r\n    currentRequest.abort()\r\n    loading.value = false\r\n    isStreaming.value = false\r\n    console.log('启动流式接口停止')\r\n  }\r\n  handleCloseMessage()\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.SubmitSuggestReply {\r\n  width: 990px;\r\n}\r\n\r\n.SuggestedReplyList {\r\n  display: flex;\r\n  align-items: center;\r\n  width: 100%;\r\n  border-bottom: 1px solid #ccc;\r\n  padding: 6px 20px 20px 20px;\r\n}\r\n\r\n.GlobalAiChatProposalReply {\r\n  width: calc(100% - 20px);\r\n  height: 100%;\r\n  padding: var(--zy-distance-two);\r\n  border-radius: var(--el-border-radius-base);\r\n  border: 1px dashed var(--zy-el-color-primary);\r\n  margin-bottom: 18px !important;\r\n\r\n  .GlobalAiChatProposalHead {\r\n    width: 100%;\r\n    display: flex;\r\n    align-items: flex-end;\r\n    padding-bottom: 12px;\r\n\r\n    .zy-el-image {\r\n      width: 52px;\r\n      height: 52px;\r\n    }\r\n\r\n    .GlobalAiChatProposalName {\r\n      width: 100%;\r\n      font-weight: bold;\r\n      padding: 0 0 10px 12px;\r\n      line-height: var(--zy-line-height);\r\n      font-size: var(--zy-name-font-size);\r\n    }\r\n  }\r\n\r\n  .GlobalAiChatProposalLoading {\r\n    width: 100%;\r\n    height: 32px;\r\n    position: relative;\r\n\r\n    @keyframes circleRoate {\r\n      from {\r\n        transform: translateY(-50%) rotate(0deg);\r\n      }\r\n\r\n      to {\r\n        transform: translateY(-50%) rotate(360deg);\r\n      }\r\n    }\r\n\r\n    .answerLoading {\r\n      width: calc((var(--zy-text-font-size) * var(--zy-line-height)) + 20px);\r\n      height: calc((var(--zy-text-font-size) * var(--zy-line-height)) + 20px);\r\n      position: absolute;\r\n      top: 50%;\r\n      left: 0;\r\n      z-index: 3;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      animation: circleRoate 1s infinite linear;\r\n\r\n      path {\r\n        fill: var(--zy-el-color-primary);\r\n      }\r\n    }\r\n\r\n    .answerLoading+.QuestionsAndAnswersChatText {\r\n      color: var(--zy-el-color-primary);\r\n      padding-left: calc((var(--zy-text-font-size) * var(--zy-line-height)) + 20px);\r\n    }\r\n  }\r\n\r\n  .GlobalAiChatProposalPonder {\r\n    width: 100%;\r\n    height: 32px;\r\n    display: flex;\r\n    align-items: center;\r\n    padding: 0 12px;\r\n    border-radius: var(--el-border-radius-base);\r\n    line-height: var(--zy-line-height);\r\n    font-size: var(--zy-text-font-size);\r\n    color: var(--zy-el-text-color-primary);\r\n    background: var(--zy-el-color-info-light-9);\r\n    margin-bottom: 12px;\r\n    cursor: pointer;\r\n\r\n    div {\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      margin-right: 6px;\r\n    }\r\n\r\n    span {\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      color: var(--zy-el-text-color-primary);\r\n    }\r\n  }\r\n\r\n  .GlobalAiChatProposalPonderContent {\r\n    width: 100%;\r\n    padding-left: 12px;\r\n    border-left: 2px solid var(--zy-el-border-color);\r\n    margin-bottom: 12px;\r\n\r\n    * {\r\n      color: var(--zy-el-text-color-secondary);\r\n    }\r\n  }\r\n\r\n  .GlobalAiChatProposalContent {\r\n    width: 100%;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": "+CA8EA,qJAAAA,mBAAA,YAAAA,oBAAA,WAAAC,CAAA,SAAAC,CAAA,EAAAD,CAAA,OAAAE,CAAA,GAAAC,MAAA,CAAAC,SAAA,EAAAC,CAAA,GAAAH,CAAA,CAAAI,cAAA,EAAAC,CAAA,GAAAJ,MAAA,CAAAK,cAAA,cAAAP,CAAA,EAAAD,CAAA,EAAAE,CAAA,IAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,CAAAO,KAAA,KAAAC,CAAA,wBAAAC,MAAA,GAAAA,MAAA,OAAAC,CAAA,GAAAF,CAAA,CAAAG,QAAA,kBAAAC,CAAA,GAAAJ,CAAA,CAAAK,aAAA,uBAAAC,CAAA,GAAAN,CAAA,CAAAO,WAAA,8BAAAC,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAC,MAAA,CAAAK,cAAA,CAAAP,CAAA,EAAAD,CAAA,IAAAS,KAAA,EAAAP,CAAA,EAAAiB,UAAA,MAAAC,YAAA,MAAAC,QAAA,SAAApB,CAAA,CAAAD,CAAA,WAAAkB,MAAA,mBAAAjB,CAAA,IAAAiB,MAAA,YAAAA,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,gBAAAoB,KAAArB,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAK,CAAA,GAAAV,CAAA,IAAAA,CAAA,CAAAI,SAAA,YAAAmB,SAAA,GAAAvB,CAAA,GAAAuB,SAAA,EAAAX,CAAA,GAAAT,MAAA,CAAAqB,MAAA,CAAAd,CAAA,CAAAN,SAAA,GAAAU,CAAA,OAAAW,OAAA,CAAApB,CAAA,gBAAAE,CAAA,CAAAK,CAAA,eAAAH,KAAA,EAAAiB,gBAAA,CAAAzB,CAAA,EAAAC,CAAA,EAAAY,CAAA,MAAAF,CAAA,aAAAe,SAAA1B,CAAA,EAAAD,CAAA,EAAAE,CAAA,mBAAA0B,IAAA,YAAAC,GAAA,EAAA5B,CAAA,CAAA6B,IAAA,CAAA9B,CAAA,EAAAE,CAAA,cAAAD,CAAA,aAAA2B,IAAA,WAAAC,GAAA,EAAA5B,CAAA,QAAAD,CAAA,CAAAsB,IAAA,GAAAA,IAAA,MAAAS,CAAA,qBAAAC,CAAA,qBAAAC,CAAA,gBAAAC,CAAA,gBAAAC,CAAA,gBAAAZ,UAAA,cAAAa,kBAAA,cAAAC,2BAAA,SAAAC,CAAA,OAAApB,MAAA,CAAAoB,CAAA,EAAA1B,CAAA,qCAAA2B,CAAA,GAAApC,MAAA,CAAAqC,cAAA,EAAAC,CAAA,GAAAF,CAAA,IAAAA,CAAA,CAAAA,CAAA,CAAAG,MAAA,QAAAD,CAAA,IAAAA,CAAA,KAAAvC,CAAA,IAAAG,CAAA,CAAAyB,IAAA,CAAAW,CAAA,EAAA7B,CAAA,MAAA0B,CAAA,GAAAG,CAAA,OAAAE,CAAA,GAAAN,0BAAA,CAAAjC,SAAA,GAAAmB,SAAA,CAAAnB,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAc,CAAA,YAAAM,sBAAA3C,CAAA,gCAAA4C,OAAA,WAAA7C,CAAA,IAAAkB,MAAA,CAAAjB,CAAA,EAAAD,CAAA,YAAAC,CAAA,gBAAA6C,OAAA,CAAA9C,CAAA,EAAAC,CAAA,sBAAA8C,cAAA9C,CAAA,EAAAD,CAAA,aAAAgD,OAAA9C,CAAA,EAAAK,CAAA,EAAAG,CAAA,EAAAE,CAAA,QAAAE,CAAA,GAAAa,QAAA,CAAA1B,CAAA,CAAAC,CAAA,GAAAD,CAAA,EAAAM,CAAA,mBAAAO,CAAA,CAAAc,IAAA,QAAAZ,CAAA,GAAAF,CAAA,CAAAe,GAAA,EAAAE,CAAA,GAAAf,CAAA,CAAAP,KAAA,SAAAsB,CAAA,uBAAAA,CAAA,IAAA1B,CAAA,CAAAyB,IAAA,CAAAC,CAAA,eAAA/B,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,CAAAmB,OAAA,EAAAC,IAAA,WAAAlD,CAAA,IAAA+C,MAAA,SAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,gBAAAX,CAAA,IAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,QAAAZ,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,EAAAoB,IAAA,WAAAlD,CAAA,IAAAe,CAAA,CAAAP,KAAA,GAAAR,CAAA,EAAAS,CAAA,CAAAM,CAAA,gBAAAf,CAAA,WAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,SAAAA,CAAA,CAAAE,CAAA,CAAAe,GAAA,SAAA3B,CAAA,EAAAK,CAAA,oBAAAE,KAAA,WAAAA,MAAAR,CAAA,EAAAI,CAAA,aAAA+C,2BAAA,eAAApD,CAAA,WAAAA,CAAA,EAAAE,CAAA,IAAA8C,MAAA,CAAA/C,CAAA,EAAAI,CAAA,EAAAL,CAAA,EAAAE,CAAA,gBAAAA,CAAA,GAAAA,CAAA,GAAAA,CAAA,CAAAiD,IAAA,CAAAC,0BAAA,EAAAA,0BAAA,IAAAA,0BAAA,qBAAA1B,iBAAA1B,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAE,CAAA,GAAAwB,CAAA,mBAAArB,CAAA,EAAAE,CAAA,QAAAL,CAAA,KAAA0B,CAAA,QAAAoB,KAAA,sCAAA9C,CAAA,KAAA2B,CAAA,oBAAAxB,CAAA,QAAAE,CAAA,WAAAH,KAAA,EAAAR,CAAA,EAAAqD,IAAA,eAAAjD,CAAA,CAAAkD,MAAA,GAAA7C,CAAA,EAAAL,CAAA,CAAAwB,GAAA,GAAAjB,CAAA,UAAAE,CAAA,GAAAT,CAAA,CAAAmD,QAAA,MAAA1C,CAAA,QAAAE,CAAA,GAAAyC,mBAAA,CAAA3C,CAAA,EAAAT,CAAA,OAAAW,CAAA,QAAAA,CAAA,KAAAmB,CAAA,mBAAAnB,CAAA,qBAAAX,CAAA,CAAAkD,MAAA,EAAAlD,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAsD,KAAA,GAAAtD,CAAA,CAAAwB,GAAA,sBAAAxB,CAAA,CAAAkD,MAAA,QAAAhD,CAAA,KAAAwB,CAAA,QAAAxB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAwB,GAAA,EAAAxB,CAAA,CAAAuD,iBAAA,CAAAvD,CAAA,CAAAwB,GAAA,uBAAAxB,CAAA,CAAAkD,MAAA,IAAAlD,CAAA,CAAAwD,MAAA,WAAAxD,CAAA,CAAAwB,GAAA,GAAAtB,CAAA,GAAA0B,CAAA,MAAAK,CAAA,GAAAX,QAAA,CAAA3B,CAAA,EAAAE,CAAA,EAAAG,CAAA,oBAAAiC,CAAA,CAAAV,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAiD,IAAA,GAAApB,CAAA,GAAAF,CAAA,EAAAM,CAAA,CAAAT,GAAA,KAAAM,CAAA,qBAAA1B,KAAA,EAAA6B,CAAA,CAAAT,GAAA,EAAAyB,IAAA,EAAAjD,CAAA,CAAAiD,IAAA,kBAAAhB,CAAA,CAAAV,IAAA,KAAArB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAkD,MAAA,YAAAlD,CAAA,CAAAwB,GAAA,GAAAS,CAAA,CAAAT,GAAA,mBAAA4B,oBAAAzD,CAAA,EAAAE,CAAA,QAAAG,CAAA,GAAAH,CAAA,CAAAqD,MAAA,EAAAhD,CAAA,GAAAP,CAAA,CAAAa,QAAA,CAAAR,CAAA,OAAAE,CAAA,KAAAN,CAAA,SAAAC,CAAA,CAAAsD,QAAA,qBAAAnD,CAAA,IAAAL,CAAA,CAAAa,QAAA,CAAAiD,MAAA,KAAA5D,CAAA,CAAAqD,MAAA,aAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,EAAAwD,mBAAA,CAAAzD,CAAA,EAAAE,CAAA,eAAAA,CAAA,CAAAqD,MAAA,kBAAAlD,CAAA,KAAAH,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,uCAAA1D,CAAA,iBAAA8B,CAAA,MAAAzB,CAAA,GAAAiB,QAAA,CAAApB,CAAA,EAAAP,CAAA,CAAAa,QAAA,EAAAX,CAAA,CAAA2B,GAAA,mBAAAnB,CAAA,CAAAkB,IAAA,SAAA1B,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,GAAAnB,CAAA,CAAAmB,GAAA,EAAA3B,CAAA,CAAAsD,QAAA,SAAArB,CAAA,MAAAvB,CAAA,GAAAF,CAAA,CAAAmB,GAAA,SAAAjB,CAAA,GAAAA,CAAA,CAAA0C,IAAA,IAAApD,CAAA,CAAAF,CAAA,CAAAgE,UAAA,IAAApD,CAAA,CAAAH,KAAA,EAAAP,CAAA,CAAA+D,IAAA,GAAAjE,CAAA,CAAAkE,OAAA,eAAAhE,CAAA,CAAAqD,MAAA,KAAArD,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,GAAAC,CAAA,CAAAsD,QAAA,SAAArB,CAAA,IAAAvB,CAAA,IAAAV,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,sCAAA7D,CAAA,CAAAsD,QAAA,SAAArB,CAAA,cAAAgC,aAAAlE,CAAA,QAAAD,CAAA,KAAAoE,MAAA,EAAAnE,CAAA,YAAAA,CAAA,KAAAD,CAAA,CAAAqE,QAAA,GAAApE,CAAA,WAAAA,CAAA,KAAAD,CAAA,CAAAsE,UAAA,GAAArE,CAAA,KAAAD,CAAA,CAAAuE,QAAA,GAAAtE,CAAA,WAAAuE,UAAA,CAAAC,IAAA,CAAAzE,CAAA,cAAA0E,cAAAzE,CAAA,QAAAD,CAAA,GAAAC,CAAA,CAAA0E,UAAA,QAAA3E,CAAA,CAAA4B,IAAA,oBAAA5B,CAAA,CAAA6B,GAAA,EAAA5B,CAAA,CAAA0E,UAAA,GAAA3E,CAAA,aAAAyB,QAAAxB,CAAA,SAAAuE,UAAA,MAAAJ,MAAA,aAAAnE,CAAA,CAAA4C,OAAA,CAAAsB,YAAA,cAAAS,KAAA,iBAAAlC,OAAA1C,CAAA,QAAAA,CAAA,WAAAA,CAAA,QAAAE,CAAA,GAAAF,CAAA,CAAAY,CAAA,OAAAV,CAAA,SAAAA,CAAA,CAAA4B,IAAA,CAAA9B,CAAA,4BAAAA,CAAA,CAAAiE,IAAA,SAAAjE,CAAA,OAAA6E,KAAA,CAAA7E,CAAA,CAAA8E,MAAA,SAAAvE,CAAA,OAAAG,CAAA,YAAAuD,KAAA,aAAA1D,CAAA,GAAAP,CAAA,CAAA8E,MAAA,OAAAzE,CAAA,CAAAyB,IAAA,CAAA9B,CAAA,EAAAO,CAAA,UAAA0D,IAAA,CAAAxD,KAAA,GAAAT,CAAA,CAAAO,CAAA,GAAA0D,IAAA,CAAAX,IAAA,OAAAW,IAAA,SAAAA,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,YAAAvD,CAAA,CAAAuD,IAAA,GAAAvD,CAAA,gBAAAqD,SAAA,QAAA/D,CAAA,iCAAAoC,iBAAA,CAAAhC,SAAA,GAAAiC,0BAAA,EAAA9B,CAAA,CAAAoC,CAAA,mBAAAlC,KAAA,EAAA4B,0BAAA,EAAAjB,YAAA,SAAAb,CAAA,CAAA8B,0BAAA,mBAAA5B,KAAA,EAAA2B,iBAAA,EAAAhB,YAAA,SAAAgB,iBAAA,CAAA2C,WAAA,GAAA7D,MAAA,CAAAmB,0BAAA,EAAArB,CAAA,wBAAAhB,CAAA,CAAAgF,mBAAA,aAAA/E,CAAA,QAAAD,CAAA,wBAAAC,CAAA,IAAAA,CAAA,CAAAgF,WAAA,WAAAjF,CAAA,KAAAA,CAAA,KAAAoC,iBAAA,6BAAApC,CAAA,CAAA+E,WAAA,IAAA/E,CAAA,CAAAkF,IAAA,OAAAlF,CAAA,CAAAmF,IAAA,aAAAlF,CAAA,WAAAE,MAAA,CAAAiF,cAAA,GAAAjF,MAAA,CAAAiF,cAAA,CAAAnF,CAAA,EAAAoC,0BAAA,KAAApC,CAAA,CAAAoF,SAAA,GAAAhD,0BAAA,EAAAnB,MAAA,CAAAjB,CAAA,EAAAe,CAAA,yBAAAf,CAAA,CAAAG,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAmB,CAAA,GAAA1C,CAAA,KAAAD,CAAA,CAAAsF,KAAA,aAAArF,CAAA,aAAAiD,OAAA,EAAAjD,CAAA,OAAA2C,qBAAA,CAAAG,aAAA,CAAA3C,SAAA,GAAAc,MAAA,CAAA6B,aAAA,CAAA3C,SAAA,EAAAU,CAAA,iCAAAd,CAAA,CAAA+C,aAAA,GAAAA,aAAA,EAAA/C,CAAA,CAAAuF,KAAA,aAAAtF,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,EAAAG,CAAA,eAAAA,CAAA,KAAAA,CAAA,GAAA8E,OAAA,OAAA5E,CAAA,OAAAmC,aAAA,CAAAzB,IAAA,CAAArB,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,GAAAG,CAAA,UAAAV,CAAA,CAAAgF,mBAAA,CAAA9E,CAAA,IAAAU,CAAA,GAAAA,CAAA,CAAAqD,IAAA,GAAAd,IAAA,WAAAlD,CAAA,WAAAA,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAQ,KAAA,GAAAG,CAAA,CAAAqD,IAAA,WAAArB,qBAAA,CAAAD,CAAA,GAAAzB,MAAA,CAAAyB,CAAA,EAAA3B,CAAA,gBAAAE,MAAA,CAAAyB,CAAA,EAAA/B,CAAA,iCAAAM,MAAA,CAAAyB,CAAA,6DAAA3C,CAAA,CAAAyF,IAAA,aAAAxF,CAAA,QAAAD,CAAA,GAAAG,MAAA,CAAAF,CAAA,GAAAC,CAAA,gBAAAG,CAAA,IAAAL,CAAA,EAAAE,CAAA,CAAAuE,IAAA,CAAApE,CAAA,UAAAH,CAAA,CAAAwF,OAAA,aAAAzB,KAAA,WAAA/D,CAAA,CAAA4E,MAAA,SAAA7E,CAAA,GAAAC,CAAA,CAAAyF,GAAA,QAAA1F,CAAA,IAAAD,CAAA,SAAAiE,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,WAAAA,IAAA,CAAAX,IAAA,OAAAW,IAAA,QAAAjE,CAAA,CAAA0C,MAAA,GAAAA,MAAA,EAAAjB,OAAA,CAAArB,SAAA,KAAA6E,WAAA,EAAAxD,OAAA,EAAAmD,KAAA,WAAAA,MAAA5E,CAAA,aAAA4F,IAAA,WAAA3B,IAAA,WAAAP,IAAA,QAAAC,KAAA,GAAA1D,CAAA,OAAAqD,IAAA,YAAAE,QAAA,cAAAD,MAAA,gBAAA1B,GAAA,GAAA5B,CAAA,OAAAuE,UAAA,CAAA3B,OAAA,CAAA6B,aAAA,IAAA1E,CAAA,WAAAE,CAAA,kBAAAA,CAAA,CAAA2F,MAAA,OAAAxF,CAAA,CAAAyB,IAAA,OAAA5B,CAAA,MAAA2E,KAAA,EAAA3E,CAAA,CAAA4F,KAAA,cAAA5F,CAAA,IAAAD,CAAA,MAAA8F,IAAA,WAAAA,KAAA,SAAAzC,IAAA,WAAArD,CAAA,QAAAuE,UAAA,IAAAG,UAAA,kBAAA1E,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,cAAAmE,IAAA,KAAApC,iBAAA,WAAAA,kBAAA5D,CAAA,aAAAsD,IAAA,QAAAtD,CAAA,MAAAE,CAAA,kBAAA+F,OAAA5F,CAAA,EAAAE,CAAA,WAAAK,CAAA,CAAAgB,IAAA,YAAAhB,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAE,CAAA,CAAA+D,IAAA,GAAA5D,CAAA,EAAAE,CAAA,KAAAL,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,KAAAM,CAAA,aAAAA,CAAA,QAAAiE,UAAA,CAAAM,MAAA,MAAAvE,CAAA,SAAAA,CAAA,QAAAG,CAAA,QAAA8D,UAAA,CAAAjE,CAAA,GAAAK,CAAA,GAAAF,CAAA,CAAAiE,UAAA,iBAAAjE,CAAA,CAAA0D,MAAA,SAAA6B,MAAA,aAAAvF,CAAA,CAAA0D,MAAA,SAAAwB,IAAA,QAAA9E,CAAA,GAAAT,CAAA,CAAAyB,IAAA,CAAApB,CAAA,eAAAM,CAAA,GAAAX,CAAA,CAAAyB,IAAA,CAAApB,CAAA,qBAAAI,CAAA,IAAAE,CAAA,aAAA4E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,gBAAAuB,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,cAAAxD,CAAA,aAAA8E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,qBAAArD,CAAA,QAAAqC,KAAA,qDAAAuC,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,YAAAT,MAAA,WAAAA,OAAA5D,CAAA,EAAAD,CAAA,aAAAE,CAAA,QAAAsE,UAAA,CAAAM,MAAA,MAAA5E,CAAA,SAAAA,CAAA,QAAAK,CAAA,QAAAiE,UAAA,CAAAtE,CAAA,OAAAK,CAAA,CAAA6D,MAAA,SAAAwB,IAAA,IAAAvF,CAAA,CAAAyB,IAAA,CAAAvB,CAAA,wBAAAqF,IAAA,GAAArF,CAAA,CAAA+D,UAAA,QAAA5D,CAAA,GAAAH,CAAA,aAAAG,CAAA,iBAAAT,CAAA,mBAAAA,CAAA,KAAAS,CAAA,CAAA0D,MAAA,IAAApE,CAAA,IAAAA,CAAA,IAAAU,CAAA,CAAA4D,UAAA,KAAA5D,CAAA,cAAAE,CAAA,GAAAF,CAAA,GAAAA,CAAA,CAAAiE,UAAA,cAAA/D,CAAA,CAAAgB,IAAA,GAAA3B,CAAA,EAAAW,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAU,CAAA,SAAA6C,MAAA,gBAAAU,IAAA,GAAAvD,CAAA,CAAA4D,UAAA,EAAAnC,CAAA,SAAA+D,QAAA,CAAAtF,CAAA,MAAAsF,QAAA,WAAAA,SAAAjG,CAAA,EAAAD,CAAA,oBAAAC,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,qBAAA5B,CAAA,CAAA2B,IAAA,mBAAA3B,CAAA,CAAA2B,IAAA,QAAAqC,IAAA,GAAAhE,CAAA,CAAA4B,GAAA,gBAAA5B,CAAA,CAAA2B,IAAA,SAAAoE,IAAA,QAAAnE,GAAA,GAAA5B,CAAA,CAAA4B,GAAA,OAAA0B,MAAA,kBAAAU,IAAA,yBAAAhE,CAAA,CAAA2B,IAAA,IAAA5B,CAAA,UAAAiE,IAAA,GAAAjE,CAAA,GAAAmC,CAAA,KAAAgE,MAAA,WAAAA,OAAAlG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAoE,UAAA,KAAArE,CAAA,cAAAiG,QAAA,CAAAhG,CAAA,CAAAyE,UAAA,EAAAzE,CAAA,CAAAqE,QAAA,GAAAG,aAAA,CAAAxE,CAAA,GAAAiC,CAAA,OAAAiE,KAAA,WAAAC,OAAApG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAkE,MAAA,KAAAnE,CAAA,QAAAI,CAAA,GAAAH,CAAA,CAAAyE,UAAA,kBAAAtE,CAAA,CAAAuB,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAwB,GAAA,EAAA6C,aAAA,CAAAxE,CAAA,YAAAK,CAAA,YAAA8C,KAAA,8BAAAiD,aAAA,WAAAA,cAAAtG,CAAA,EAAAE,CAAA,EAAAG,CAAA,gBAAAmD,QAAA,KAAA3C,QAAA,EAAA6B,MAAA,CAAA1C,CAAA,GAAAgE,UAAA,EAAA9D,CAAA,EAAAgE,OAAA,EAAA7D,CAAA,oBAAAkD,MAAA,UAAA1B,GAAA,GAAA5B,CAAA,GAAAkC,CAAA,OAAAnC,CAAA;AAAA,SAAAuG,mBAAAlG,CAAA,EAAAJ,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAK,CAAA,EAAAK,CAAA,EAAAE,CAAA,cAAAJ,CAAA,GAAAL,CAAA,CAAAO,CAAA,EAAAE,CAAA,GAAAE,CAAA,GAAAN,CAAA,CAAAD,KAAA,WAAAJ,CAAA,gBAAAL,CAAA,CAAAK,CAAA,KAAAK,CAAA,CAAA4C,IAAA,GAAArD,CAAA,CAAAe,CAAA,IAAAwE,OAAA,CAAAvC,OAAA,CAAAjC,CAAA,EAAAmC,IAAA,CAAAjD,CAAA,EAAAK,CAAA;AAAA,SAAAiG,kBAAAnG,CAAA,6BAAAJ,CAAA,SAAAD,CAAA,GAAAyG,SAAA,aAAAjB,OAAA,WAAAtF,CAAA,EAAAK,CAAA,QAAAK,CAAA,GAAAP,CAAA,CAAAqG,KAAA,CAAAzG,CAAA,EAAAD,CAAA,YAAA2G,MAAAtG,CAAA,IAAAkG,kBAAA,CAAA3F,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAoG,KAAA,EAAAC,MAAA,UAAAvG,CAAA,cAAAuG,OAAAvG,CAAA,IAAAkG,kBAAA,CAAA3F,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAoG,KAAA,EAAAC,MAAA,WAAAvG,CAAA,KAAAsG,KAAA;AADA,OAAOE,GAAG,MAAM,OAAO;AACvB,OAAOC,WAAW,MAAM,uBAAuB;AAC/C,SAASC,QAAQ,EAAEC,GAAG,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,KAAK;AACxD,SAASC,oBAAoB,EAAEC,aAAa,QAAQ,yBAAyB;AAC7E,SAASC,gBAAgB,QAAQ,+BAA+B;AAChE,SAASC,SAAS,QAAQ,cAAc;AARxC,IAAAC,WAAA,GAAe;EAAErC,IAAI,EAAE;AAAqB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;IAS7C,IAAMsC,KAAK,GAAGC,OAKZ;IACF,IAAMC,WAAW,GACf,62CAA62C;IAC/2C,IAAMC,UAAU,GACd,qqDAAqqD;IACvqD,IAAMC,IAAI,GAAGC,MAAyB;IAEtC,IAAMC,OAAO,GAAGd,GAAG,CAAC,CAAC;IACrB,IAAMe,IAAI,GAAGhB,QAAQ,CAAC;MACpBiB,EAAE,EAAE,EAAE;MACNC,SAAS,EAAE,EAAE;MACbC,MAAM,EAAE,CAAC;MACTC,YAAY,EAAE,EAAE;MAChBC,QAAQ,EAAE,EAAE;MACZC,OAAO,EAAE,EAAE;MACXC,gBAAgB,EAAE;MAAE;MAClB;QAAEN,EAAE,EAAE,GAAG;QAAEK,OAAO,EAAE,EAAE;QAAEE,kBAAkB,EAAE,EAAE;QAAEC,qBAAqB,EAAE;MAAG,CAAC,EAC3E;QAAER,EAAE,EAAE,GAAG;QAAEK,OAAO,EAAE,GAAG;QAAEE,kBAAkB,EAAE,EAAE;QAAEC,qBAAqB,EAAE;MAAG,CAAC;IAEhF,CAAC,CAAC;IACF,IAAMD,kBAAkB,GAAGvB,GAAG,CAAC,CAC7B;MAAEgB,EAAE,EAAE,GAAG;MAAE9C,IAAI,EAAE;IAAK,CAAC,EACvB;MAAE8C,EAAE,EAAE,GAAG;MAAE9C,IAAI,EAAE;IAAK,CAAC,EACvB;MAAE8C,EAAE,EAAE,GAAG;MAAE9C,IAAI,EAAE;IAAK,CAAC,CACxB,CAAC;IACF,IAAMuD,KAAK,GAAG1B,QAAQ,CAAC;MACrBkB,SAAS,EAAE,CAAC;QAAES,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,SAAS;QAAEC,OAAO,EAAE,CAAC,MAAM,EAAE,QAAQ;MAAE,CAAC,CAAC;MAChFV,MAAM,EAAE,CAAC;QAAEQ,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,SAAS;QAAEC,OAAO,EAAE,CAAC,MAAM,EAAE,QAAQ;MAAE,CAAC,CAAC;MAC7ER,QAAQ,EAAE,CAAC;QAAEM,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,QAAQ;QAAEC,OAAO,EAAE,CAAC,MAAM,EAAE,QAAQ;MAAE,CAAC,CAAC;MAC9ET,YAAY,EAAE,CAAC;QAAEO,QAAQ,EAAE,KAAK;QAAEC,OAAO,EAAE,UAAU;QAAEC,OAAO,EAAE,CAAC,MAAM,EAAE,QAAQ;MAAE,CAAC,CAAC;MACrFP,OAAO,EAAE,CAAC;QAAEK,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE,CAAC,MAAM,EAAE,QAAQ;MAAE,CAAC;IAC7E,CAAC,CAAC;IACF,IAAMC,OAAO,GAAG7B,GAAG,CAAC,CAAC,CAAC,CAAC;IACvB,IAAMoB,QAAQ,GAAGpB,GAAG,CAAC,EAAE,CAAC;IACxB,IAAMiB,SAAS,GAAGjB,GAAG,CAAC,EAAE,CAAC;IAEzB,IAAM8B,UAAU,GAAG9B,GAAG,CAAC,IAAI,CAAC;IAC5B,IAAM+B,MAAM,GAAG/B,GAAG,CAAC,EAAE,CAAC;IACtB,IAAMgC,KAAK,GAAGhC,GAAG,CAAC,CAAC;IACnB,IAAMiC,WAAW,GAAGjC,GAAG,CAAC,CAAC;IACzB,IAAMkC,MAAM,GAAGlC,GAAG,CAAC;MAAEqB,OAAO,EAAE,EAAE;MAAEc,UAAU,EAAE;IAAG,CAAC,CAAC;IACnD,IAAMC,YAAY,GAAGpC,GAAG,CAAC;MAAEqB,OAAO,EAAE,EAAE;MAAEc,UAAU,EAAE;IAAG,CAAC,CAAC;IAEzD,IAAIE,cAAc,GAAG,IAAI;IACzB,IAAMC,OAAO,GAAGtC,GAAG,CAAC,KAAK,CAAC;IAC1B,IAAMuC,WAAW,GAAGvC,GAAG,CAAC,KAAK,CAAC;IAC9B,IAAIwC,SAAS,GAAG,IAAI;IACpB,IAAIC,OAAO,GAAG,IAAI;IAElB,IAAMC,IAAI,GAAG,SAAPA,IAAIA,CAAA,EAAS;MACjB,OAAO,sCAAsC,CAACC,OAAO,CAAC,OAAO,EAAE,UAAC7I,CAAC,EAAK;QACpE,IAAIZ,CAAC,GAAI0J,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAI,CAAC;UAC9BpH,CAAC,GAAG3B,CAAC,IAAI,GAAG,GAAGZ,CAAC,GAAIA,CAAC,GAAG,GAAG,GAAI,GAAG;QACpC,OAAOuC,CAAC,CAACqH,QAAQ,CAAC,EAAE,CAAC;MACvB,CAAC,CAAC;IACJ,CAAC;IACD,IAAAC,iBAAA,GAAyB1C,gBAAgB,CAAC,oBAAoB,CAAC;MAAvD2C,YAAY,GAAAD,iBAAA,CAAZC,YAAY;IACpB/C,SAAS,CAAC,YAAM;MACdgD,cAAc,CAAC,CAAC;MAChBC,qBAAqB,CAAC,CAAC;MACvB,IAAI1C,KAAK,CAACQ,EAAE,EAAE;QAAEmC,wBAAwB,CAAC,CAAC;MAAC;MAC3CC,cAAc,CAAC,CAAC;IAClB,CAAC,CAAC;IAEF,IAAMH,cAAc;MAAA,IAAAI,KAAA,GAAA7D,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAmF,QAAA;QAAA,IAAAC,GAAA,EAAAC,IAAA;QAAA,OAAAzK,mBAAA,GAAAuB,IAAA,UAAAmJ,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAA9E,IAAA,GAAA8E,QAAA,CAAAzG,IAAA;YAAA;cAAAyG,QAAA,CAAAzG,IAAA;cAAA,OACH4C,GAAG,CAACoD,cAAc,CAAC;gBAAEU,SAAS,EAAE,CAAC,wBAAwB;cAAE,CAAC,CAAC;YAAA;cAAzEJ,GAAG,GAAAG,QAAA,CAAAhH,IAAA;cACH8G,IAAI,GAAKD,GAAG,CAAZC,IAAI;cACVvC,SAAS,CAACxH,KAAK,GAAG+J,IAAI,CAACI,sBAAsB;YAAA;YAAA;cAAA,OAAAF,QAAA,CAAA3E,IAAA;UAAA;QAAA,GAAAuE,OAAA;MAAA,CAC9C;MAAA,gBAJKL,cAAcA,CAAA;QAAA,OAAAI,KAAA,CAAA3D,KAAA,OAAAD,SAAA;MAAA;IAAA,GAInB;IAED,IAAMyD,qBAAqB;MAAA,IAAAW,KAAA,GAAArE,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAA2F,SAAA;QAAA,IAAAP,GAAA;QAAA,OAAAxK,mBAAA,GAAAuB,IAAA,UAAAyJ,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAApF,IAAA,GAAAoF,SAAA,CAAA/G,IAAA;YAAA;cAAA+G,SAAA,CAAA/G,IAAA;cAAA,OACV4C,GAAG,CAACqD,qBAAqB,CAAC;gBAAEe,MAAM,EAAE,GAAG;gBAAEC,QAAQ,EAAE,KAAK;gBAAEC,KAAK,EAAE;kBAAEC,YAAY,EAAE5D,KAAK,CAAC6D;gBAAU;cAAE,CAAC,CAAC;YAAA;cAAjHd,GAAG,GAAAS,SAAA,CAAAtH,IAAA;cACTqE,IAAI,CAACO,gBAAgB,GAAGiC,GAAG,CAACC,IAAI;YAAA;YAAA;cAAA,OAAAQ,SAAA,CAAAjF,IAAA;UAAA;QAAA,GAAA+E,QAAA;MAAA,CACjC;MAAA,gBAHKZ,qBAAqBA,CAAA;QAAA,OAAAW,KAAA,CAAAnE,KAAA,OAAAD,SAAA;MAAA;IAAA,GAG1B;IAED,IAAM6E,YAAY,GAAG,SAAfA,YAAYA,CAAA,EAAS;MACzB7C,KAAK,CAACN,YAAY,GAAG,CAAC;QAAEO,QAAQ,EAAE,KAAK;QAAEC,OAAO,EAAE,UAAU;QAAEC,OAAO,EAAE,CAAC,MAAM,EAAE,QAAQ;MAAE,CAAC,CAAC;MAC5F,IAAI,CAACb,IAAI,CAACG,MAAM,EAAE;QAChBO,KAAK,CAACN,YAAY,GAAG,CAAC;UAAEO,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,UAAU;UAAEC,OAAO,EAAE,CAAC,MAAM,EAAE,QAAQ;QAAE,CAAC,CAAC;MAC7F;IACF,CAAC;IACD,IAAM2C,UAAU,GAAG,SAAbA,UAAUA,CAAIC,IAAI,EAAK;MAC3BzD,IAAI,CAACK,QAAQ,GAAGoD,IAAI,CAACC,GAAG,CAAC,UAAChJ,CAAC;QAAA,OAAKA,CAAC,CAACuF,EAAE;MAAA,EAAC;MACrCI,QAAQ,CAAC3H,KAAK,GAAG+K,IAAI;MACrB1D,OAAO,CAACrH,KAAK,CAACiL,aAAa,CAAC,UAAU,CAAC;MACvCxE,QAAQ,CAAC,YAAM;QAAA,IAAAyE,YAAA,EAAAC,kBAAA;QACb7C,MAAM,CAACtI,KAAK,GAAG,EAAE;QACjByI,MAAM,CAACzI,KAAK,GAAG;UAAE4H,OAAO,EAAE,EAAE;UAAEc,UAAU,EAAE;QAAG,CAAC;QAC9CC,YAAY,CAAC3I,KAAK,GAAG;UAAE4H,OAAO,EAAE,EAAE;UAAEc,UAAU,EAAE;QAAG,CAAC;QACpD,CAAAwC,YAAA,GAAA3C,KAAK,CAACvI,KAAK,cAAAkL,YAAA,eAAXA,YAAA,CAAaE,YAAY,CAAC,CAAC;QAC3B,CAAAD,kBAAA,GAAA3C,WAAW,CAACxI,KAAK,cAAAmL,kBAAA,eAAjBA,kBAAA,CAAmBC,YAAY,CAAC,CAAC;QACjCC,iBAAiB,CAAC,CAAC;QACnB,IAAI1D,QAAQ,CAAC3H,KAAK,CAACqE,MAAM,EAAE;UACzBiH,gBAAgB,CAAC,CAAC;QACpB;MACF,CAAC,CAAC;IACJ,CAAC;IACD,IAAM3B,cAAc;MAAA,IAAA4B,KAAA,GAAAxF,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAA8G,SAAA;QAAA,IAAAC,qBAAA,EAAA1B,IAAA;QAAA,OAAAzK,mBAAA,GAAAuB,IAAA,UAAA6K,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAxG,IAAA,GAAAwG,SAAA,CAAAnI,IAAA;YAAA;cAAAmI,SAAA,CAAAnI,IAAA;cAAA,OACE4C,GAAG,CAACuD,cAAc,CAAC;gBAAEiC,QAAQ,EAAE7E,KAAK,CAAC6D;cAAU,CAAC,CAAC;YAAA;cAAAa,qBAAA,GAAAE,SAAA,CAAA1I,IAAA;cAAhE8G,IAAI,GAAA0B,qBAAA,CAAJ1B,IAAI;cACZ3B,OAAO,CAACpI,KAAK,GAAG+J,IAAI;YAAA;YAAA;cAAA,OAAA4B,SAAA,CAAArG,IAAA;UAAA;QAAA,GAAAkG,QAAA;MAAA,CACrB;MAAA,gBAHK7B,cAAcA,CAAA;QAAA,OAAA4B,KAAA,CAAAtF,KAAA,OAAAD,SAAA;MAAA;IAAA,GAGnB;IACD,IAAM0D,wBAAwB;MAAA,IAAAmC,KAAA,GAAA9F,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAoH,SAAA;QAAA,IAAAC,qBAAA;QAAA,IAAAC,MAAA,EAAAlC,GAAA,EAAAC,IAAA;QAAA,OAAAzK,mBAAA,GAAAuB,IAAA,UAAAoL,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA/G,IAAA,GAAA+G,SAAA,CAAA1I,IAAA;YAAA;cAC3BwI,MAAM,GAAG,CAAC,CAAC;cACfA,MAAM,CAACjF,KAAK,CAACoF,iBAAiB,CAAC,GAAGpF,KAAK,CAACQ,EAAE;cAAA2E,SAAA,CAAA1I,IAAA;cAAA,OACxB4C,GAAG,CAACsD,wBAAwB,CAACsC,MAAM,CAAC;YAAA;cAAhDlC,GAAG,GAAAoC,SAAA,CAAAjJ,IAAA;cACH8G,IAAI,GAAKD,GAAG,CAAZC,IAAI;cACVzC,IAAI,CAACC,EAAE,GAAGwC,IAAI,CAACxC,EAAE;cACjBD,IAAI,CAACE,SAAS,IAAAuE,qBAAA,GAAGhC,IAAI,CAACqC,oBAAoB,cAAAL,qBAAA,uBAAzBA,qBAAA,CAA2B/L,KAAK;cACjDsH,IAAI,CAACG,MAAM,GAAGsC,IAAI,CAACtC,MAAM;cACzBH,IAAI,CAACI,YAAY,GAAGqC,IAAI,CAACrC,YAAY;cACrCJ,IAAI,CAACM,OAAO,GAAGmC,IAAI,CAACnC,OAAO;cAC3BD,QAAQ,CAAC3H,KAAK,GAAG+J,IAAI,CAACsC,WAAW,IAAI,EAAE;cACvC/E,IAAI,CAACK,QAAQ,GAAGoC,IAAI,CAACsC,WAAW,CAACrB,GAAG,CAAC,UAAChJ,CAAC;gBAAA,OAAKA,CAAC,CAACuF,EAAE;cAAA,EAAC;YAAA;YAAA;cAAA,OAAA2E,SAAA,CAAA5G,IAAA;UAAA;QAAA,GAAAwG,QAAA;MAAA,CAClD;MAAA,gBAZKpC,wBAAwBA,CAAA;QAAA,OAAAmC,KAAA,CAAA5F,KAAA,OAAAD,SAAA;MAAA;IAAA,GAY7B;IACD,IAAMsG,UAAU;MAAA,IAAAC,KAAA,GAAAxG,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAA8H,SAAOC,MAAM;QAAA,OAAAnN,mBAAA,GAAAuB,IAAA,UAAA6L,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAxH,IAAA,GAAAwH,SAAA,CAAAnJ,IAAA;YAAA;cAAA,IACzBiJ,MAAM;gBAAAE,SAAA,CAAAnJ,IAAA;gBAAA;cAAA;cAAA,OAAAmJ,SAAA,CAAAvJ,MAAA;YAAA;cAAAuJ,SAAA,CAAAnJ,IAAA;cAAA,OACLiJ,MAAM,CAACG,QAAQ,CAAC,UAACC,KAAK,EAAEC,MAAM,EAAK;gBACvC,IAAID,KAAK,EAAE;kBACTE,UAAU,CAAC,CAAC;gBACd,CAAC,MAAM;kBACLlG,SAAS,CAAC;oBAAE1F,IAAI,EAAE,SAAS;oBAAE+G,OAAO,EAAE;kBAAiB,CAAC,CAAC;gBAC3D;cACF,CAAC,CAAC;YAAA;YAAA;cAAA,OAAAyE,SAAA,CAAArH,IAAA;UAAA;QAAA,GAAAkH,QAAA;MAAA,CACH;MAAA,gBATKF,UAAUA,CAAAU,EAAA;QAAA,OAAAT,KAAA,CAAAtG,KAAA,OAAAD,SAAA;MAAA;IAAA,GASf;IACD,IAAM+G,UAAU;MAAA,IAAAE,KAAA,GAAAlH,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAwI,SAAA;QAAA,IAAAC,qBAAA,EAAAC,IAAA;QAAA,OAAA9N,mBAAA,GAAAuB,IAAA,UAAAwM,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAnI,IAAA,GAAAmI,SAAA,CAAA9J,IAAA;YAAA;cAAA8J,SAAA,CAAA9J,IAAA;cAAA,OACM4C,GAAG,CAAC2G,UAAU,CACnChG,KAAK,CAACQ,EAAE,GAAG,kCAAkC,GAAG,iCAAiC,EACjF;gBACED,IAAI,EAAE;kBACJC,EAAE,EAAED,IAAI,CAACC,EAAE;kBACXgG,iBAAiB,EAAExG,KAAK,CAACyG,MAAM;kBAC/B7C,YAAY,EAAE5D,KAAK,CAAC6D,SAAS;kBAC7BwB,oBAAoB,EAAE9E,IAAI,CAACE,SAAS;kBACpCC,MAAM,EAAEH,IAAI,CAACG,MAAM;kBACnBC,YAAY,EAAEJ,IAAI,CAACG,MAAM,GAAG,EAAE,GAAGH,IAAI,CAACI,YAAY;kBAClDE,OAAO,EAAEN,IAAI,CAACM,OAAO;kBACrB6F,aAAa,EAAE9F,QAAQ,CAAC3H,KAAK,CAACgL,GAAG,CAAC,UAAChJ,CAAC;oBAAA,OAAKA,CAAC,CAACuF,EAAE;kBAAA;gBAC/C;cACF,CACF,CAAC;YAAA;cAAA4F,qBAAA,GAAAG,SAAA,CAAArK,IAAA;cAdOmK,IAAI,GAAAD,qBAAA,CAAJC,IAAI;cAeZ,IAAIA,IAAI,KAAK,GAAG,EAAE;gBAChBvG,SAAS,CAAC;kBAAE1F,IAAI,EAAE,SAAS;kBAAE+G,OAAO,EAAEnB,KAAK,CAACQ,EAAE,GAAG,MAAM,GAAG;gBAAO,CAAC,CAAC;gBACnEJ,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC;cACxB;YAAC;YAAA;cAAA,OAAAmG,SAAA,CAAAhI,IAAA;UAAA;QAAA,GAAA4H,QAAA;MAAA,CACF;MAAA,gBApBKH,UAAUA,CAAA;QAAA,OAAAE,KAAA,CAAAhH,KAAA,OAAAD,SAAA;MAAA;IAAA,GAoBf;IACD,IAAM0H,SAAS,GAAG,SAAZA,SAASA,CAAA,EAAS;MACtBvG,IAAI,CAAC,UAAU,CAAC;IAClB,CAAC;IAED,IAAMwG,YAAY,GAAG,SAAfA,YAAYA,CAAIC,GAAG,EAAK;MAC5B,IAAMC,IAAI,GAAGC,QAAQ,CAACF,GAAG,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;MAClD,IAAMG,KAAK,GAAGD,QAAQ,CAAEF,GAAG,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,IAAK,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;MACxE,IAAMI,OAAO,GAAGF,QAAQ,CAAEF,GAAG,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,IAAK,IAAI,GAAG,EAAE,CAAC,CAAC;MAChE,IAAMK,OAAO,GAAIL,GAAG,IAAI,IAAI,GAAG,EAAE,CAAC,GAAI,IAAI;MAC1C,IAAIM,IAAI,GAAG,EAAE;MACb,IAAIL,IAAI,GAAG,CAAC,EAAEK,IAAI,IAAI,GAAGL,IAAI,KAAK;MAClC,IAAIE,KAAK,GAAG,CAAC,EAAEG,IAAI,IAAI,GAAGH,KAAK,MAAM;MACrC,IAAIC,OAAO,GAAG,CAAC,EAAEE,IAAI,IAAI,GAAGF,OAAO,MAAM;MACzC,IAAIC,OAAO,GAAG,CAAC,EAAEC,IAAI,IAAI,GAAGD,OAAO,KAAK;MACxC,OAAOC,IAAI;IACb,CAAC;IACD,IAAM5C,gBAAgB;MAAA,IAAA6C,KAAA,GAAApI,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAA0J,SAAA;QAAA,IAAAC,WAAA,EAAAtE,IAAA,EAAAuE,QAAA,EAAAC,MAAA,EAAAC,QAAA,EAAAC,mBAAA,EAAAC,aAAA,EAAAC,aAAA,EAAAC,aAAA,EAAAC,aAAA;QAAA,OAAAvP,mBAAA,GAAAuB,IAAA,UAAAiO,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA5J,IAAA,GAAA4J,SAAA,CAAAvL,IAAA;YAAA;cAAA,IAClBmD,aAAa,CAAC3G,KAAK;gBAAA+O,SAAA,CAAAvL,IAAA;gBAAA;cAAA;cAAA,OAAAuL,SAAA,CAAA3L,MAAA;YAAA;cACxB2F,SAAS,GAAG,IAAIiG,IAAI,CAAC,CAAC;cACtBnG,OAAO,CAAC7I,KAAK,GAAG,IAAI;cACpB8I,WAAW,CAAC9I,KAAK,GAAG,IAAI;cAAA+O,SAAA,CAAA5J,IAAA;cAEhBkJ,WAAW,GAAG;gBAClBY,iBAAiB,EAAE,oBAAoB;gBACvCC,MAAM,EAAEjG,IAAI,CAAC,CAAC;gBACdkG,QAAQ,EAAE,WAAW;gBACrBC,UAAU,EAAErI,KAAK,CAAC6D,SAAS;gBAC3B6C,aAAa,EAAE9F,QAAQ,CAAC3H,KAAK,CAACgL,GAAG,CAAC,UAAChJ,CAAC;kBAAA,OAAKA,CAAC,CAACuF,EAAE;gBAAA,EAAC,CAAC8H,IAAI,CAAC,GAAG;cACzD,CAAC;cACDzG,cAAc,GAAGvC,WAAW,CAAC,mBAAmB,EAAE;gBAChDiJ,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACnB,WAAW,CAAC;gBACjCoB,SAASA,CAAEC,KAAK,EAAE;kBAChB;kBACA7G,OAAO,CAAC7I,KAAK,GAAG,KAAK;kBACrB,IAAI0P,KAAK,CAAC3F,IAAI,KAAK,QAAQ,EAAE;oBACrBA,IAAI,GAAGwF,IAAI,CAACI,KAAK,CAACD,KAAK,CAAC3F,IAAI,CAAC;oBACnC,IAAI6F,KAAK,CAACC,OAAO,CAAC9F,IAAI,CAAC,EAAE;sBACvB+F,OAAO,CAACC,GAAG,CAAC,IAAI,EAAEhG,IAAI,CAAC;oBACzB,CAAC,MAAM;sBACL;sBACMwE,MAAM,GAAG,CAAAxE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiG,OAAO,KAAI,CAAC,CAAC,CAAC,CAAC;sBAC9B5H,QAAO,GAAG,EAAAkG,QAAA,GAAAC,MAAM,CAAC,CAAC,CAAC,cAAAD,QAAA,uBAATA,QAAA,CAAW2B,KAAK,KAAI,CAAC,CAAC;sBACtC,IAAIvQ,MAAM,CAACC,SAAS,CAACE,cAAc,CAACwB,IAAI,CAAC+G,QAAO,EAAE,mBAAmB,CAAC,EAAE;wBACtE,CAAAqG,mBAAA,GAAAjG,WAAW,CAACxI,KAAK,cAAAyO,mBAAA,eAAjBA,mBAAA,CAAmByB,aAAa,CAAC9H,QAAO,CAAC+H,iBAAiB,IAAI,EAAE,CAAC;wBACjE,IAAI7H,MAAM,CAACtI,KAAK,EAAE;0BAChB+I,SAAS,GAAG,IAAI;0BAChBC,OAAO,GAAG,IAAI;wBAChB,CAAC,MAAM;0BACLA,OAAO,GAAG,IAAIgG,IAAI,CAAC,CAAC;0BACdN,aAAa,GAAG1F,OAAO,GAAGD,SAAS;0BACzCT,MAAM,CAACtI,KAAK,GAAG2N,YAAY,CAACe,aAAa,CAAC;wBAC5C;sBACF;sBACA,IAAIhP,MAAM,CAACC,SAAS,CAACE,cAAc,CAACwB,IAAI,CAAC+G,QAAO,EAAE,SAAS,CAAC,EAAE;wBAC5D,CAAAuG,aAAA,GAAApG,KAAK,CAACvI,KAAK,cAAA2O,aAAA,eAAXA,aAAA,CAAauB,aAAa,CAAC9H,QAAO,CAACR,OAAO,IAAI,EAAE,CAAC;sBACnD;oBACF;kBACF,CAAC,MAAM;oBACL;oBACA,CAAAgH,aAAA,GAAArG,KAAK,CAACvI,KAAK,cAAA4O,aAAA,eAAXA,aAAA,CAAasB,aAAa,CAAC,EAAE,CAAC;oBAC9BpH,WAAW,CAAC9I,KAAK,GAAG,KAAK;kBAC3B;gBACF,CAAC;gBACDoQ,OAAOA,CAAEC,GAAG,EAAE;kBACZP,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEM,GAAG,CAAC;gBAC7B,CAAC;gBACDC,OAAOA,CAAA,EAAI;kBACTzH,OAAO,CAAC7I,KAAK,GAAG,KAAK;kBACrB8I,WAAW,CAAC9I,KAAK,GAAG,KAAK;kBACzB8P,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAC;gBACvB;cACF,CAAC,CAAC;cAAAhB,SAAA,CAAAvL,IAAA;cAAA,OACIoF,cAAc,CAAC2H,OAAO;YAAA;cAAAxB,SAAA,CAAAvL,IAAA;cAAA;YAAA;cAAAuL,SAAA,CAAA5J,IAAA;cAAA4J,SAAA,CAAAyB,EAAA,GAAAzB,SAAA;cAE5BlG,OAAO,CAAC7I,KAAK,GAAG,KAAK;cACrB8I,WAAW,CAAC9I,KAAK,GAAG,KAAK;cACzB,CAAA6O,aAAA,GAAAtG,KAAK,CAACvI,KAAK,cAAA6O,aAAA,eAAXA,aAAA,CAAaqB,aAAa,CAAC,cAAc,CAAC;cAC1CJ,OAAO,CAACW,KAAK,CAAC,WAAW,EAAA1B,SAAA,CAAAyB,EAAO,CAAC;YAAA;cAAAzB,SAAA,CAAA5J,IAAA;cAAA,OAAA4J,SAAA,CAAArJ,MAAA;YAAA;YAAA;cAAA,OAAAqJ,SAAA,CAAAzJ,IAAA;UAAA;QAAA,GAAA8I,QAAA;MAAA,CAIpC;MAAA,gBAjEK9C,gBAAgBA,CAAA;QAAA,OAAA6C,KAAA,CAAAlI,KAAA,OAAAD,SAAA;MAAA;IAAA,GAiErB;IACD,IAAM0K,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAA,EAAS;MAC/B9H,cAAc,GAAG,IAAI;MACrBC,OAAO,CAAC7I,KAAK,GAAG,KAAK;MACrB8I,WAAW,CAAC9I,KAAK,GAAG,KAAK;IAC3B,CAAC;IACD,IAAMqL,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAA,EAAS;MAC9B,IAAIzC,cAAc,EAAE;QAClBA,cAAc,CAAC+H,KAAK,CAAC,CAAC;QACtB9H,OAAO,CAAC7I,KAAK,GAAG,KAAK;QACrB8I,WAAW,CAAC9I,KAAK,GAAG,KAAK;QACzB8P,OAAO,CAACC,GAAG,CAAC,UAAU,CAAC;MACzB;MACAW,kBAAkB,CAAC,CAAC;IACtB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}